# SPQR System Documentation

## System Overview

SPQR (Smart Process Query & Response) is an intelligent CRM+AI+Automation system that enables users to interact with the system through natural language processing, automate workflows, and integrate external services.

## Table of Contents

1. [Architecture](#architecture)
2. [Technology Stack](#technology-stack)
3. [Core Features](#core-features)
4. [API Documentation](#api-documentation)
5. [Integration Guide](#integration-guide)
6. [Deployment Guide](#deployment-guide)
7. [Security & Compliance](#security--compliance)

## Architecture

SPQR follows a modular microservices architecture with the following main components:

```
┌─────────────────────────────────────────────────────────────────┐
│                        SPQR-System                              │
│                                                                 │
│  ┌─────────┐     ┌─────────────┐     ┌────────────────┐        │
│  │ Frontend│◄───►│  Nginx      │◄───►│  Core API      │        │
│  └─────────┘     └─────────────┘     └────────────────┘        │
│                                              ▲                  │
│                                              │                  │
│                                              ▼                  │
│  ┌─────────┐     ┌─────────────┐     ┌────────────────┐        │
│  │ Redis   │◄───►│  PostgreSQL │◄───►│  Vector Store  │        │
│  └─────────┘     └─────────────┘     └────────────────┘        │
│                                                                 │
│  ┌─────────────┐ ┌─────────────┐     ┌────────────────┐        │
│  │ MCP         │ │ Task        │     │ Browser        │        │
│  │ Coordinator │ │ Manager     │     │ Automation     │        │
│  └─────────────┘ └─────────────┘     └────────────────┘        │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                  External Integrations                   │   │
│  │  (Google, Email, OpenAI, Claude, Ollama, etc.)          │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### Component Descriptions

1. **Core API Service**: Central backend service that manages the main business logic and database operations
2. **MCP Coordinator**: Coordinates AI models and their interactions
3. **Task Manager**: Manages automated tasks and workflows
4. **Browser Automation**: Enables web browser automation for external integrations
5. **Vector Store**: Stores vector embeddings for semantic search and AI functions
6. **Frontend**: Reactive user interface with modern design

### Design Patterns

- **Module Pattern**: Clear separation of responsibilities
- **Factory Pattern**: Creation of services and components
- **Observer Pattern**: Event-based communication
- **Strategy Pattern**: Interchangeable algorithms
- **Adapter Pattern**: External integrations
- **Singleton Pattern**: Shared resources

## Technology Stack

### Backend
- **Language**: Node.js with Express.js
- **Database**: PostgreSQL with pgvector extension for vector storage
- **Caching**: Redis for token tracking and task management
- **AI Integration**: 
  - OpenAI API (GPT-4)
  - Claude API (Claude 3 Opus)
  - Local LLMs via Ollama (Llama 3)
  - Google AI Studio
  - Mistral AI
  - Cohere
  - OpenRouter
  - Huggingface

### Frontend
- **Framework**: React with TypeScript
- **UI Library**: Custom components based on Shadcn/UI
- **State Management**: React Query and Context API
- **Routing**: React Router

### Infrastructure
- **Containerization**: Docker and Docker Compose
- **Vector Database**: Qdrant for semantic search
- **Reverse Proxy**: Nginx for frontend serving and API routing

## Core Features

### 1. CRM Functionality
- Contact management with detailed profiles
- Task and appointment management
- Communication tracking (emails, calls, messages)
- Document management and storage

### 2. AI Integration
- Natural language processing for user queries
- Multi-model support (OpenAI, Claude, local LLMs)
- Context-aware memory for conversations
- Semantic search across documents and contacts
- Automatic summaries and insights

### 3. Process Automation
- Natural Language Process Builder for workflow creation
- Rule-based automations with approval steps
- Browser automation for external web services
- Task scheduling and monitoring
- Event-based triggers and actions

### 4. Integrations
- Email integration (IMAP/SMTP)
- Google services (Calendar, Contacts, Email) via OAuth
- API connections to external services
- Webhook support for bidirectional integrations
- File synchronization with cloud storage services

## API Documentation

```
┌─────────────────────────────────────────────────────────────────┐
│                        SPQR API                                 │
│                                                                 │
│  ┌─────────────────────────┐     ┌─────────────────────────┐    │
│  │ /api/v1/auth            │     │ /api/v1/contacts        │    │
│  ├─────────────────────────┤     ├─────────────────────────┤    │
│  │ POST /login             │     │ GET /                   │    │
│  │ POST /register          │     │ GET /:id                │    │
│  │ POST /refresh-token     │     │ POST /                  │    │
│  │ POST /logout            │     │ PUT /:id                │    │
│  └─────────────────────────┘     │ DELETE /:id             │    │
│                                  └─────────────────────────┘    │
│  ┌─────────────────────────┐     ┌─────────────────────────┐    │
│  │ /api/v1/tasks           │     │ /api/v1/workflows       │    │
│  ├─────────────────────────┤     ├─────────────────────────┤    │
│  │ GET /                   │     │ GET /                   │    │
│  │ GET /:id                │     │ GET /:id                │    │
│  │ POST /                  │     │ POST /                  │    │
│  │ PUT /:id                │     │ PUT /:id                │    │
│  │ DELETE /:id             │     │ DELETE /:id             │    │
│  └─────────────────────────┘     │ POST /:id/execute       │    │
│                                  └─────────────────────────┘    │
│  ┌─────────────────────────┐     ┌─────────────────────────┐    │
│  │ /api/v1/integrations    │     │ /api/v1/ai              │    │
│  ├─────────────────────────┤     ├─────────────────────────┤    │
│  │ GET /                   │     │ POST /chat              │    │
│  │ GET /:id                │     │ POST /analyze           │    │
│  │ POST /                  │     │ POST /summarize         │    │
│  │ PUT /:id                │     │ POST /generate          │    │
│  │ DELETE /:id             │     └─────────────────────────┘    │
│  │ POST /:id/connect       │                                    │
│  └─────────────────────────┘                                    │
└─────────────────────────────────────────────────────────────────┘
```

## Integration Guide

### Available AI Integrations

| Provider | API Key | Rate Limits | Models |
|----------|---------|-------------|--------|
| OpenAI | ******************************************************************************************************************************************************************** | Varies by tier | GPT-4, GPT-3.5 |
| Claude | ************************************************************************************************************ | Varies by tier | Claude 3 Opus, Sonnet, Haiku |
| Google AI | AIzaSyDEZPNmGvZqnfgNFev7Zss4NSGbJzWNmbs | 5M tokens/day, 1M tokens/min | Gemini 2.5 Pro, Gemini 2.0 Flash |
| OpenRouter | 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 | 20 req/min, 50 req/day | Various models |
| Mistral | 5zzh6GldrXGuxWEO8u7XvVGv2STvfKzK | 1 req/sec, 500K tokens/min | Mistral models |
| Cohere | d2cchd71cXgNdh2IcFkrNNrJuVYjpdBSzBCsna0X | 20 req/min, 1K req/month | Command-R, Command-R+ |
| Huggingface | ************************************* | Varies by model | Various open models |
| Nvidia NIM | ********************************************************************** | 40 req/min | Various open models |
| Cloudflare Workers AI | **************************************** | 10K neurons/day | Various models |

### Google OAuth Integration

```json
{
  "web": {
    "client_id": "************-08jvm0irh1geer7378rbu6vr247svmng.apps.googleusercontent.com",
    "project_id": "jarvis-386211",
    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
    "token_uri": "https://oauth2.googleapis.com/token",
    "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
    "client_secret": "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"
  }
}
```

## Database Schema

```
┌───────────────┐       ┌───────────────┐       ┌───────────────┐
│    Users      │       │   Contacts    │       │    Tasks      │
├───────────────┤       ├───────────────┤       ├───────────────┤
│ id            │       │ id            │       │ id            │
│ username      │       │ user_id       │◄──────┤ user_id       │
│ email         │       │ name          │       │ title         │
│ password_hash │       │ email         │       │ description   │
│ role          │       │ phone         │       │ status        │
│ created_at    │       │ company       │       │ due_date      │
│ updated_at    │       │ notes         │       │ created_at    │
└───────┬───────┘       │ created_at    │       │ updated_at    │
        │               │ updated_at    │       └───────────────┘
        │               └───────────────┘
        │                      ▲
        │                      │
┌───────▼───────┐       ┌──────┴────────┐       ┌───────────────┐
│ Integrations  │       │Communications │       │  Workflows    │
├───────────────┤       ├───────────────┤       ├───────────────┤
│ id            │       │ id            │       │ id            │
│ user_id       │       │ contact_id    │       │ user_id       │
│ type          │       │ type          │       │ name          │
│ credentials   │       │ content       │       │ description   │
│ status        │       │ direction     │       │ steps         │
│ created_at    │       │ timestamp     │       │ triggers      │
│ updated_at    │       │ created_at    │       │ created_at    │
└───────────────┘       │ updated_at    │       │ updated_at    │
                        └───────────────┘       └───────────────┘
```

## Deployment Guide

### Docker Deployment

```
┌─────────────────────────────────────────────────────────────────┐
│                      Docker Environment                         │
│                                                                 │
│  ┌─────────────┐     ┌─────────────┐     ┌─────────────┐        │
│  │ Frontend    │     │ Core API    │     │ MCP         │        │
│  │ Container   │     │ Container   │     │ Container   │        │
│  └─────────────┘     └─────────────┘     └─────────────┘        │
│                                                                 │
│  ┌─────────────┐     ┌─────────────┐     ┌─────────────┐        │
│  │ Task Manager│     │ Browser     │     │ Vector Store│        │
│  │ Container   │     │ Automation  │     │ Container   │        │
│  └─────────────┘     └─────────────┘     └─────────────┘        │
│                                                                 │
│  ┌─────────────┐     ┌─────────────┐     ┌─────────────┐        │
│  │ PostgreSQL  │     │ Redis       │     │ Ollama      │        │
│  │ Container   │     │ Container   │     │ Container   │        │
│  └─────────────┘     └─────────────┘     └─────────────┘        │
│                                                                 │
│                Docker Compose Orchestration                     │
└─────────────────────────────────────────────────────────────────┘
```

### System Requirements

- Docker and Docker Compose
- Minimum 4GB RAM (8GB+ recommended)
- 20GB+ disk space
- Internet connection for external API integrations

### Installation Steps

1. Clone the repository
2. Configure environment variables in `.env` file
3. Create required data directories
4. Build and start services with Docker Compose
5. Initialize the database
6. Access the application at http://localhost

## Security & Compliance

- JWT-based authentication
- Role-based access controls
- Encrypted data storage
- Audit logs for system activities
- GDPR-compliant data processing

## User Interface

```
┌─────────────────────────────────────────────────────────────────┐
│                        SPQR Dashboard                           │
│                                                                 │
│  ┌─────────────┐                             ┌───────────────┐  │
│  │ SPQR Logo   │                             │ User Profile  │  │
│  └─────────────┘                             └───────────────┘  │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                                                         │   │
│  │  Dashboard Overview                                     │   │
│  │                                                         │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │   │
│  │  │ Tasks       │  │ Contacts    │  │ Recent      │      │   │
│  │  │ Widget      │  │ Widget      │  │ Activities  │      │   │
│  │  └─────────────┘  └─────────────┘  └─────────────┘      │   │
│  │                                                         │   │
│  │  ┌─────────────────────────────────────────────────┐    │   │
│  │  │                                                 │    │   │
│  │  │  AI Assistant Chat Interface                    │    │   │
│  │  │                                                 │    │   │
│  │  └─────────────────────────────────────────────────┘    │   │
│  │                                                         │   │
│  └─────────────────────────────────────────────────────────┘   │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## Data Flow

```
┌──────────┐     ┌───────────┐     ┌───────────┐     ┌───────────┐
│ User     │────►│ Frontend  │────►│ Core API  │────►│ Database  │
└──────────┘     └───────────┘     └───────────┘     └───────────┘
      │                                  │                 ▲
      │                                  │                 │
      │                                  ▼                 │
      │                            ┌───────────┐           │
      │                            │   MCP     │           │
      │                            │Coordinator│           │
      │                            └───────────┘           │
      │                                  │                 │
      │                                  ▼                 │
      │                            ┌───────────┐           │
      │                            │ AI Models │           │
      │                            └───────────┘           │
      │                                  │                 │
      │                                  ▼                 │
      │                            ┌───────────┐           │
      └───────────────────────────►│   Task    │───────────┘
                                   │  Manager  │
                                   └───────────┘
                                         │
                                         ▼
                                   ┌───────────┐
                                   │  Browser  │
                                   │Automation │
                                   └───────────┘
```

## GitHub Repository Access

- GitHub Access Token: ****************************************
