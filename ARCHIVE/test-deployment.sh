#!/bin/bash

# SPQR Deployment Testing Script
# Tests all critical functionality before developer handoff

set -e

# Configuration
SERVER_HOST="**************"
APP_PORT="8080"
BASE_URL="http://${SERVER_HOST}:${APP_PORT}"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_pass() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_fail() {
    echo -e "${RED}[FAIL]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Test counters
TESTS_RUN=0
TESTS_PASSED=0
TESTS_FAILED=0

run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TESTS_RUN=$((TESTS_RUN + 1))
    print_test "$test_name"
    
    if eval "$test_command" > /dev/null 2>&1; then
        print_pass "$test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        print_fail "$test_name"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Test server connectivity
test_server_connectivity() {
    print_test "Server SSH connectivity"
    if ssh -o ConnectTimeout=10 vince@${SERVER_HOST} "echo 'SSH OK'" > /dev/null 2>&1; then
        print_pass "SSH connection successful"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_fail "Cannot connect via SSH"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

# Test application health
test_application_health() {
    run_test "Application health endpoint" "curl -f ${BASE_URL}/health"
}

# Test main application
test_main_application() {
    run_test "Main application loads" "curl -f ${BASE_URL}/"
}

# Test API endpoints
test_api_endpoints() {
    run_test "API proxy endpoint" "curl -f ${BASE_URL}/api/"
    run_test "Auth endpoint" "curl -f ${BASE_URL}/auth/"
}

# Test AI services
test_ai_services() {
    print_test "Ollama AI service"
    if ssh vince@${SERVER_HOST} "curl -f http://localhost:11434/api/version" > /dev/null 2>&1; then
        print_pass "Ollama service responding"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_fail "Ollama service not responding"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

# Test Docker services
test_docker_services() {
    print_test "Docker services status"
    
    local services_output
    services_output=$(ssh vince@${SERVER_HOST} "cd /home/<USER>/spqr-production && docker-compose ps --format table" 2>/dev/null || echo "")
    
    if [[ -n "$services_output" ]]; then
        print_pass "Docker services are running"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        echo "$services_output"
    else
        print_fail "Docker services not running or not accessible"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

# Test database connectivity
test_database() {
    print_test "Database connectivity"
    
    if ssh vince@${SERVER_HOST} "cd /home/<USER>/spqr-production && docker-compose exec -T supabase-db pg_isready -U postgres" > /dev/null 2>&1; then
        print_pass "Database is accessible"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_fail "Database connection failed"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

# Test system resources
test_system_resources() {
    print_test "System resource usage"
    
    local resource_info
    resource_info=$(ssh vince@${SERVER_HOST} "
        echo 'Memory Usage:' && free -h | grep Mem
        echo 'Disk Usage:' && df -h / | tail -1
        echo 'CPU Load:' && uptime
    " 2>/dev/null)
    
    if [[ -n "$resource_info" ]]; then
        print_pass "System resources checked"
        echo "$resource_info"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_fail "Could not check system resources"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

# Test log files
test_logs() {
    print_test "Application logs"
    
    local log_check
    log_check=$(ssh vince@${SERVER_HOST} "cd /home/<USER>/spqr-production && docker-compose logs --tail=5 crm-app 2>/dev/null | wc -l")
    
    if [[ "$log_check" -gt 0 ]]; then
        print_pass "Application logs are being generated"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_warn "No recent application logs found"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

# Test security
test_security() {
    print_test "Firewall status"
    
    if ssh vince@${SERVER_HOST} "sudo ufw status | grep -q 'Status: active'" 2>/dev/null; then
        print_pass "Firewall is active"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_warn "Firewall status unclear"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

# Performance test
test_performance() {
    print_test "Application response time"
    
    local response_time
    response_time=$(curl -o /dev/null -s -w '%{time_total}' ${BASE_URL}/health 2>/dev/null || echo "999")
    
    if (( $(echo "$response_time < 5.0" | bc -l) )); then
        print_pass "Response time: ${response_time}s (acceptable)"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        print_warn "Response time: ${response_time}s (slow)"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    TESTS_RUN=$((TESTS_RUN + 1))
}

# Generate test report
generate_report() {
    echo ""
    echo "🧪 SPQR Deployment Test Report"
    echo "=============================="
    echo "Server: ${SERVER_HOST}"
    echo "Application URL: ${BASE_URL}"
    echo "Test Date: $(date)"
    echo ""
    echo "📊 Test Results:"
    echo "Tests Run: ${TESTS_RUN}"
    echo "Tests Passed: ${TESTS_PASSED}"
    echo "Tests Failed: ${TESTS_FAILED}"
    echo "Success Rate: $(( TESTS_PASSED * 100 / TESTS_RUN ))%"
    echo ""
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo "🎉 All tests passed! System is ready for developer acceptance testing."
        echo ""
        echo "✅ Ready for handoff to developer"
        echo "✅ All core services operational"
        echo "✅ Performance within acceptable limits"
    elif [[ $TESTS_FAILED -le 2 ]]; then
        echo "⚠️  Minor issues detected. System is mostly functional."
        echo ""
        echo "🔧 Recommended actions:"
        echo "- Review failed tests"
        echo "- Check service logs"
        echo "- Verify configuration"
    else
        echo "❌ Multiple issues detected. System needs attention."
        echo ""
        echo "🚨 Critical actions needed:"
        echo "- Review deployment logs"
        echo "- Check service status"
        echo "- Verify environment configuration"
        echo "- Contact system administrator"
    fi
    
    echo ""
    echo "📋 Next Steps for Developer:"
    echo "1. Review test results above"
    echo "2. Access application at: ${BASE_URL}"
    echo "3. Test CRM functionality"
    echo "4. Verify AI integration"
    echo "5. Check user authentication"
    echo "6. Test automation features"
    echo ""
    echo "🔗 Useful Commands:"
    echo "- SSH to server: ssh vince@${SERVER_HOST}"
    echo "- View logs: docker-compose logs -f"
    echo "- Restart services: docker-compose restart"
    echo "- Check status: docker-compose ps"
}

# Main test execution
main() {
    echo "🧪 Starting SPQR Deployment Tests"
    echo "=================================="
    echo "Target: ${BASE_URL}"
    echo "Time: $(date)"
    echo ""
    
    # Core connectivity tests
    test_server_connectivity
    test_application_health
    test_main_application
    
    # Service tests
    test_docker_services
    test_database
    test_ai_services
    test_api_endpoints
    
    # System tests
    test_system_resources
    test_logs
    test_security
    test_performance
    
    # Generate final report
    generate_report
}

# Check dependencies
if ! command -v curl &> /dev/null; then
    echo "Error: curl is required for testing"
    exit 1
fi

if ! command -v ssh &> /dev/null; then
    echo "Error: ssh is required for testing"
    exit 1
fi

if ! command -v bc &> /dev/null; then
    echo "Warning: bc not found, performance test may not work"
fi

# Run tests
main "$@"
