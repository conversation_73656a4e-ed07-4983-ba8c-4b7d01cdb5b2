# SPQR3 Troubleshooting Guide

This guide provides detailed troubleshooting steps for common issues with SPQR3 installation and operation.

## Table of Contents

1. [Diagnostic Tools](#diagnostic-tools)
2. [Container Issues](#container-issues)
3. [Database Issues](#database-issues)
4. [Network Issues](#network-issues)
5. [Frontend Issues](#frontend-issues)
6. [API Issues](#api-issues)
7. [Resource Issues](#resource-issues)
8. [Common Error Messages](#common-error-messages)
9. [Recovery Procedures](#recovery-procedures)

## Diagnostic Tools

### Running the Diagnostic Script

The diagnostic script helps identify issues with your SPQR3 installation:

```bash
./tools/diagnose-installation.sh
```

This script checks:
- System requirements
- Docker installation
- Container status
- Network connectivity
- Port availability
- Common errors in logs

### Checking Container Logs

Container logs are the most valuable source of information for troubleshooting:

```bash
# View logs for a specific container
docker logs spqr-core

# View last 100 lines
docker logs --tail 100 spqr-core

# Follow logs in real-time
docker logs -f spqr-core

# View logs with timestamps
docker logs --timestamps spqr-core
```

### Checking Container Health

```bash
# Check health status of all containers
docker ps --format "table {{.Names}}\t{{.Status}}"

# Get detailed health status
docker inspect --format "{{.Name}} - {{.State.Health.Status}}" $(docker ps -q)
```

## Container Issues

### Containers Not Starting

**Symptoms**:
- Container status shows "Exited" or "Created"
- `docker compose ps` shows containers in a non-running state

**Troubleshooting Steps**:

1. Check container logs:
   ```bash
   docker logs spqr-core
   ```

2. Check if required files exist:
   ```bash
   ls -la backend/src/
   ```

3. Check for port conflicts:
   ```bash
   netstat -tuln | grep 3200
   ```

4. Try rebuilding the container:
   ```bash
   docker compose build spqr-core
   docker compose up -d spqr-core
   ```

5. Check for missing dependencies in package.json:
   ```bash
   cat backend/package.json
   ```

### Container Crashes After Starting

**Symptoms**:
- Container starts but exits shortly after
- Health checks fail

**Troubleshooting Steps**:

1. Check for startup errors:
   ```bash
   docker logs spqr-core
   ```

2. Check for missing environment variables:
   ```bash
   docker exec spqr-core env
   ```

3. Check for database connection issues:
   ```bash
   docker exec spqr-core curl -v postgres:5432
   ```

4. Check for memory issues:
   ```bash
   docker stats
   ```

### Missing Module Errors

**Symptoms**:
- Logs show "Cannot find module" errors
- Application fails to start

**Troubleshooting Steps**:

1. Run the fix script:
   ```bash
   ./tools/fix-common-issues.sh
   ```

2. Check if the module exists:
   ```bash
   find . -name "error.js"
   ```

3. Manually create missing modules:
   ```bash
   mkdir -p backend/src/middleware
   touch backend/src/middleware/error.js
   ```

4. Reinstall node modules:
   ```bash
   docker exec spqr-core npm install
   ```

## Database Issues

### PostgreSQL Container Not Starting

**Symptoms**:
- PostgreSQL container exits shortly after starting
- Logs show initialization errors

**Troubleshooting Steps**:

1. Check PostgreSQL logs:
   ```bash
   docker logs spqr-postgres
   ```

2. Check data directory permissions:
   ```bash
   ls -la data/postgres
   ```

3. Fix permissions:
   ```bash
   sudo chown -R 1000:1000 data/postgres
   ```

4. Try with a fresh data directory:
   ```bash
   mv data/postgres data/postgres.bak
   mkdir -p data/postgres
   docker compose up -d postgres
   ```

### Database Connection Failures

**Symptoms**:
- Services can't connect to the database
- Logs show connection errors

**Troubleshooting Steps**:

1. Check if PostgreSQL is running:
   ```bash
   docker ps | grep postgres
   ```

2. Verify database credentials:
   ```bash
   grep POSTGRES .env
   ```

3. Test connection from another container:
   ```bash
   docker exec spqr-core pg_isready -h postgres -U postgres
   ```

4. Check PostgreSQL logs for authentication errors:
   ```bash
   docker logs spqr-postgres | grep "authentication failed"
   ```

### Database Schema Issues

**Symptoms**:
- Services start but show database schema errors
- Missing tables or columns

**Troubleshooting Steps**:

1. Check if schema was initialized:
   ```bash
   docker exec spqr-postgres psql -U postgres -d spqr2 -c "\dt"
   ```

2. Manually apply schema:
   ```bash
   cat sql/schema.sql | docker exec -i spqr-postgres psql -U postgres -d spqr2
   ```

3. Check for pgvector extension:
   ```bash
   docker exec spqr-postgres psql -U postgres -d spqr2 -c "SELECT * FROM pg_extension;"
   ```

4. Install pgvector extension:
   ```bash
   docker exec spqr-postgres psql -U postgres -d spqr2 -c "CREATE EXTENSION IF NOT EXISTS vector;"
   ```

## Network Issues

### Containers Can't Communicate

**Symptoms**:
- Services can't reach other services
- Connection refused errors

**Troubleshooting Steps**:

1. Check if all containers are on the same network:
   ```bash
   docker network inspect spqr-network
   ```

2. Test network connectivity:
   ```bash
   docker exec spqr-core ping -c 3 postgres
   ```

3. Check DNS resolution:
   ```bash
   docker exec spqr-core nslookup postgres
   ```

4. Recreate the network:
   ```bash
   docker compose down
   docker network prune
   docker compose up -d
   ```

### Port Conflicts

**Symptoms**:
- Services can't bind to ports
- "Address already in use" errors

**Troubleshooting Steps**:

1. Check which process is using the port:
   ```bash
   sudo netstat -tuln | grep 3200
   sudo lsof -i :3200
   ```

2. Kill the process using the port:
   ```bash
   sudo kill $(sudo lsof -t -i:3200)
   ```

3. Change the port in docker-compose.yml and .env:
   ```bash
   sed -i 's/3200:3200/3201:3200/g' docker-compose.yml
   ```

## Frontend Issues

### Frontend Not Building

**Symptoms**:
- Frontend build fails
- build-frontend.sh script errors

**Troubleshooting Steps**:

1. Check build logs:
   ```bash
   ./build-frontend.sh
   ```

2. Check for Node.js version issues:
   ```bash
   node -v
   ```

3. Check for npm dependency issues:
   ```bash
   cd frontend
   npm install --legacy-peer-deps
   ```

4. Check for build script issues:
   ```bash
   cat frontend/package.json | grep build
   ```

### Frontend Not Loading

**Symptoms**:
- Blank page in browser
- Network errors in browser console

**Troubleshooting Steps**:

1. Check if frontend container is running:
   ```bash
   docker ps | grep frontend
   ```

2. Check frontend logs:
   ```bash
   docker logs spqr-frontend
   ```

3. Check if frontend is accessible:
   ```bash
   curl -I http://localhost
   ```

4. Check for CORS issues:
   ```bash
   curl -I -H "Origin: http://localhost" http://localhost:3200/health
   ```

## API Issues

### API Endpoints Not Responding

**Symptoms**:
- API calls return 404 or 500 errors
- Services can't communicate with API

**Troubleshooting Steps**:

1. Check if API container is running:
   ```bash
   docker ps | grep spqr-core
   ```

2. Check API logs:
   ```bash
   docker logs spqr-core
   ```

3. Test API directly:
   ```bash
   curl http://localhost:3200/health
   ```

4. Check for route configuration issues:
   ```bash
   docker exec spqr-core ls -la src/routes
   ```

### Authentication Issues

**Symptoms**:
- API calls return 401 or 403 errors
- Can't log in to the system

**Troubleshooting Steps**:

1. Check JWT configuration:
   ```bash
   grep JWT .env
   ```

2. Check if JWT secret is set:
   ```bash
   docker exec spqr-core printenv | grep JWT
   ```

3. Check for expired tokens:
   ```bash
   # Decode JWT token (replace with your token)
   echo "your-jwt-token" | cut -d. -f2 | base64 -d | json_pp
   ```

## Resource Issues

### Memory Issues

**Symptoms**:
- Containers crash with OOM (Out of Memory) errors
- System becomes unresponsive

**Troubleshooting Steps**:

1. Check memory usage:
   ```bash
   docker stats
   ```

2. Check system memory:
   ```bash
   free -h
   ```

3. Increase container memory limits in docker-compose.yml:
   ```yaml
   services:
     spqr-core:
       deploy:
         resources:
           limits:
             memory: 1G
   ```

4. Add swap space:
   ```bash
   sudo fallocate -l 2G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

### Disk Space Issues

**Symptoms**:
- "No space left on device" errors
- Containers can't write files

**Troubleshooting Steps**:

1. Check disk usage:
   ```bash
   df -h
   ```

2. Find large files:
   ```bash
   sudo find / -type f -size +100M
   ```

3. Clean up Docker:
   ```bash
   docker system prune -a
   ```

4. Remove old logs:
   ```bash
   sudo find /var/log -type f -name "*.log" -exec truncate -s 0 {} \;
   ```

## Common Error Messages

### "Cannot find module"

**Cause**: Missing JavaScript module or incorrect import path

**Solution**:
1. Run the fix script:
   ```bash
   ./tools/fix-common-issues.sh
   ```

2. Check import paths in the code:
   ```bash
   grep -r "require" backend/src/
   ```

3. Install missing dependencies:
   ```bash
   docker exec spqr-core npm install
   ```

### "ECONNREFUSED"

**Cause**: Service can't connect to another service

**Solution**:
1. Check if the target service is running:
   ```bash
   docker ps | grep postgres
   ```

2. Check network connectivity:
   ```bash
   docker exec spqr-core ping -c 3 postgres
   ```

3. Check if the service is listening on the expected port:
   ```bash
   docker exec postgres netstat -tuln
   ```

### "password authentication failed"

**Cause**: Incorrect database credentials

**Solution**:
1. Check database credentials in .env:
   ```bash
   grep POSTGRES .env
   ```

2. Reset PostgreSQL password:
   ```bash
   docker exec -it spqr-postgres psql -U postgres -c "ALTER USER postgres WITH PASSWORD 'new_password';"
   ```

3. Update .env with the new password:
   ```bash
   sed -i 's/POSTGRES_PASSWORD=.*/POSTGRES_PASSWORD=new_password/g' .env
   ```

## Recovery Procedures

### Complete System Reset

**Warning**: This will delete all data!

```bash
# Stop all containers
docker compose down

# Remove volumes
docker compose down -v

# Remove data directories
rm -rf data/*

# Create fresh data directories
mkdir -p data/postgres data/redis data/vector-store data/ollama

# Run fix script
./tools/fix-common-issues.sh

# Start fresh
docker compose up -d
```

### Database Recovery

If the database is corrupted but you have a backup:

```bash
# Stop the database container
docker compose stop postgres

# Remove the database data
rm -rf data/postgres/*

# Start the database container
docker compose up -d postgres

# Wait for it to initialize
sleep 10

# Restore from backup
cat backups/spqr2_db_backup.sql | docker exec -i spqr-postgres psql -U postgres -d spqr2
```

### Single Service Recovery

If a single service is problematic:

```bash
# Stop the service
docker compose stop spqr-core

# Remove the container
docker compose rm spqr-core

# Rebuild the service
docker compose build spqr-core

# Start the service
docker compose up -d spqr-core
```
