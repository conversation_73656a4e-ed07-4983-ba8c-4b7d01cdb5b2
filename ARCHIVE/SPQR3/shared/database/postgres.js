/**
 * PostgreSQL Database Service
 * 
 * Direct PostgreSQL implementation to replace Supabase.
 * Provides a unified interface for database operations.
 */

const { Pool } = require('pg');
const format = require('pg-format');

/**
 * PostgreSQL database service
 */
class PostgresService {
  /**
   * Create a new PostgreSQL service instance
   * 
   * @param {Object} config - Database configuration
   */
  constructor(config = {}) {
    this.config = {
      connectionString: config.url || process.env.DATABASE_URL,
      ssl: config.ssl !== undefined ? config.ssl : process.env.DATABASE_SSL === 'true',
      max: config.poolSize || 10,
      idleTimeoutMillis: config.idleTimeout || 30000,
      connectionTimeoutMillis: config.connectionTimeout || 10000
    };
    
    this.pool = new Pool(this.config);
    
    // Handle pool errors
    this.pool.on('error', (err) => {
      console.error('Unexpected error on idle client', err);
    });
  }
  
  /**
   * Initialize the database connection
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Test the connection
      const client = await this.pool.connect();
      await client.query('SELECT NOW()');
      client.release();
      
      console.log('PostgreSQL connection established successfully');
      
      // Initialize pgvector extension if needed
      await this.initPgVector();
    } catch (error) {
      console.error('Error initializing PostgreSQL connection:', error);
      throw error;
    }
  }
  
  /**
   * Initialize pgvector extension
   * 
   * @returns {Promise<void>}
   */
  async initPgVector() {
    const client = await this.pool.connect();
    try {
      await client.query('CREATE EXTENSION IF NOT EXISTS vector');
      console.log('pgvector extension initialized');
    } catch (error) {
      console.error('Error initializing pgvector extension:', error);
      throw error;
    } finally {
      client.release();
    }
  }
  
  /**
   * Execute a query
   * 
   * @param {String} text - Query text
   * @param {Array} params - Query parameters
   * @returns {Promise<Object>} Query result
   */
  async query(text, params = []) {
    const start = Date.now();
    const result = await this.pool.query(text, params);
    const duration = Date.now() - start;
    
    if (process.env.NODE_ENV === 'development') {
      console.log('Executed query', { text, duration, rows: result.rowCount });
    }
    
    return result;
  }
  
  /**
   * Execute a transaction
   * 
   * @param {Function} callback - Transaction callback
   * @returns {Promise<any>} Transaction result
   */
  async transaction(callback) {
    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');
      
      const result = await callback(client);
      
      await client.query('COMMIT');
      
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }
  
  /**
   * Insert a record
   * 
   * @param {String} table - Table name
   * @param {Object} data - Data to insert
   * @param {String} returning - Returning clause
   * @returns {Promise<Object>} Inserted record
   */
  async insert(table, data, returning = '*') {
    const keys = Object.keys(data);
    const values = Object.values(data);
    const placeholders = keys.map((_, i) => `$${i + 1}`).join(', ');
    
    const query = `
      INSERT INTO ${table} (${keys.join(', ')})
      VALUES (${placeholders})
      RETURNING ${returning}
    `;
    
    const result = await this.query(query, values);
    
    return result.rows[0];
  }
  
  /**
   * Insert multiple records
   * 
   * @param {String} table - Table name
   * @param {Array} data - Data to insert
   * @param {String} returning - Returning clause
   * @returns {Promise<Array>} Inserted records
   */
  async insertMany(table, data, returning = '*') {
    if (!data.length) {
      return [];
    }
    
    const keys = Object.keys(data[0]);
    const values = data.map(item => Object.values(item));
    
    const query = format(
      `
        INSERT INTO ${table} (${keys.join(', ')})
        VALUES %L
        RETURNING ${returning}
      `,
      values
    );
    
    const result = await this.query(query);
    
    return result.rows;
  }
  
  /**
   * Update a record
   * 
   * @param {String} table - Table name
   * @param {Object} data - Data to update
   * @param {Object} where - Where clause
   * @param {String} returning - Returning clause
   * @returns {Promise<Object>} Updated record
   */
  async update(table, data, where, returning = '*') {
    const keys = Object.keys(data);
    const values = Object.values(data);
    const whereKeys = Object.keys(where);
    const whereValues = Object.values(where);
    
    const setClause = keys.map((key, i) => `${key} = $${i + 1}`).join(', ');
    const whereClause = whereKeys.map((key, i) => `${key} = $${i + keys.length + 1}`).join(' AND ');
    
    const query = `
      UPDATE ${table}
      SET ${setClause}
      WHERE ${whereClause}
      RETURNING ${returning}
    `;
    
    const result = await this.query(query, [...values, ...whereValues]);
    
    return result.rows[0];
  }
  
  /**
   * Delete a record
   * 
   * @param {String} table - Table name
   * @param {Object} where - Where clause
   * @param {String} returning - Returning clause
   * @returns {Promise<Object>} Deleted record
   */
  async delete(table, where, returning = '*') {
    const keys = Object.keys(where);
    const values = Object.values(where);
    
    const whereClause = keys.map((key, i) => `${key} = $${i + 1}`).join(' AND ');
    
    const query = `
      DELETE FROM ${table}
      WHERE ${whereClause}
      RETURNING ${returning}
    `;
    
    const result = await this.query(query, values);
    
    return result.rows[0];
  }
  
  /**
   * Select records
   * 
   * @param {String} table - Table name
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Selected records
   */
  async select(table, options = {}) {
    const {
      columns = '*',
      where = {},
      orderBy = null,
      limit = null,
      offset = null
    } = options;
    
    const whereKeys = Object.keys(where);
    const whereValues = Object.values(where);
    
    let query = `SELECT ${Array.isArray(columns) ? columns.join(', ') : columns} FROM ${table}`;
    
    if (whereKeys.length) {
      const whereClause = whereKeys.map((key, i) => `${key} = $${i + 1}`).join(' AND ');
      query += ` WHERE ${whereClause}`;
    }
    
    if (orderBy) {
      query += ` ORDER BY ${orderBy}`;
    }
    
    if (limit) {
      query += ` LIMIT ${limit}`;
    }
    
    if (offset) {
      query += ` OFFSET ${offset}`;
    }
    
    const result = await this.query(query, whereValues);
    
    return result.rows;
  }
  
  /**
   * Select a single record
   * 
   * @param {String} table - Table name
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Selected record
   */
  async selectOne(table, options = {}) {
    const result = await this.select(table, { ...options, limit: 1 });
    
    return result[0] || null;
  }
  
  /**
   * Execute a raw query
   * 
   * @param {String} query - Raw query
   * @param {Array} params - Query parameters
   * @returns {Promise<Object>} Query result
   */
  async raw(query, params = []) {
    return this.query(query, params);
  }
  
  /**
   * Close the database connection
   * 
   * @returns {Promise<void>}
   */
  async close() {
    await this.pool.end();
  }
  
  /**
   * Create a vector table
   * 
   * @param {String} table - Table name
   * @param {Object} columns - Column definitions
   * @param {Array} vectorColumns - Vector column names
   * @param {Number} dimensions - Vector dimensions
   * @returns {Promise<void>}
   */
  async createVectorTable(table, columns = {}, vectorColumns = [], dimensions = 1536) {
    const columnDefinitions = Object.entries(columns)
      .map(([name, type]) => `${name} ${type}`)
      .concat(vectorColumns.map(name => `${name} vector(${dimensions})`))
      .join(', ');
    
    const query = `
      CREATE TABLE IF NOT EXISTS ${table} (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        ${columnDefinitions},
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
      )
    `;
    
    await this.query(query);
    
    // Create updated_at trigger
    const triggerQuery = `
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
      
      DROP TRIGGER IF EXISTS update_${table}_updated_at ON ${table};
      
      CREATE TRIGGER update_${table}_updated_at
      BEFORE UPDATE ON ${table}
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at_column();
    `;
    
    await this.query(triggerQuery);
    
    // Create vector indexes
    for (const column of vectorColumns) {
      const indexQuery = `
        CREATE INDEX IF NOT EXISTS ${table}_${column}_idx
        ON ${table}
        USING ivfflat (${column} vector_cosine_ops)
        WITH (lists = 100)
      `;
      
      await this.query(indexQuery);
    }
  }
  
  /**
   * Perform a vector search
   * 
   * @param {String} table - Table name
   * @param {String} vectorColumn - Vector column name
   * @param {Array} vector - Query vector
   * @param {Object} options - Search options
   * @returns {Promise<Array>} Search results
   */
  async vectorSearch(table, vectorColumn, vector, options = {}) {
    const {
      limit = 10,
      threshold = 0.5,
      filter = null,
      columns = '*'
    } = options;
    
    let query = `
      SELECT ${Array.isArray(columns) ? columns.join(', ') : columns},
             1 - (${vectorColumn} <=> $1) AS similarity
      FROM ${table}
      WHERE 1 - (${vectorColumn} <=> $1) > $2
    `;
    
    const params = [vector, threshold];
    
    if (filter) {
      query += ` AND ${filter.condition}`;
      params.push(...filter.params);
    }
    
    query += `
      ORDER BY similarity DESC
      LIMIT $${params.length + 1}
    `;
    
    params.push(limit);
    
    const result = await this.query(query, params);
    
    return result.rows;
  }
}

module.exports = PostgresService;
