/**
 * Authentication Service
 * 
 * Provides authentication functionality.
 */

const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');
const { ApiError } = require('../middleware/errorHandler');
const { logger } = require('../utils/logger');

/**
 * Authentication service
 */
class AuthService {
  /**
   * Create a new authentication service
   * 
   * @param {Object} options - Service options
   */
  constructor(options = {}) {
    this.options = {
      jwtSecret: options.jwtSecret || process.env.JWT_SECRET || 'secret',
      jwtExpiresIn: options.jwtExpiresIn || process.env.JWT_EXPIRES_IN || '1d',
      refreshTokenExpiresIn: options.refreshTokenExpiresIn || process.env.REFRESH_TOKEN_EXPIRES_IN || '7d',
      saltRounds: options.saltRounds || 10,
      ...options
    };
    
    this.db = options.db;
    this.logger = options.logger || logger;
    this.initialized = false;
  }
  
  /**
   * Initialize the service
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }
    
    if (!this.db) {
      throw new Error('Database is required');
    }
    
    try {
      // Create users table
      await this.db.query(`
        CREATE TABLE IF NOT EXISTS users (
          id UUID PRIMARY KEY,
          username TEXT NOT NULL UNIQUE,
          email TEXT NOT NULL UNIQUE,
          password TEXT NOT NULL,
          role TEXT NOT NULL,
          status TEXT NOT NULL,
          created_at TIMESTAMPTZ NOT NULL,
          updated_at TIMESTAMPTZ NOT NULL
        )
      `);
      
      // Create refresh tokens table
      await this.db.query(`
        CREATE TABLE IF NOT EXISTS refresh_tokens (
          id UUID PRIMARY KEY,
          user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          token TEXT NOT NULL,
          expires_at TIMESTAMPTZ NOT NULL,
          created_at TIMESTAMPTZ NOT NULL,
          UNIQUE(user_id, token)
        )
      `);
      
      this.initialized = true;
      
      this.logger.info('Authentication service initialized');
    } catch (error) {
      this.logger.error('Error initializing authentication service', error);
      throw error;
    }
  }
  
  /**
   * Register a user
   * 
   * @param {Object} userData - User data
   * @returns {Promise<Object>} User
   */
  async register(userData) {
    await this.initialize();
    
    const { username, email, password, role = 'user' } = userData;
    
    // Validate input
    if (!username || !email || !password) {
      throw ApiError.badRequest('Username, email, and password are required');
    }
    
    try {
      // Check if user exists
      const existingUser = await this.db.query(
        `
          SELECT * FROM users
          WHERE username = $1 OR email = $2
        `,
        [username, email]
      );
      
      if (existingUser.rows.length > 0) {
        throw ApiError.conflict('Username or email already exists');
      }
      
      // Hash password
      const hashedPassword = await bcrypt.hash(password, this.options.saltRounds);
      
      // Create user
      const userId = uuidv4();
      const now = new Date().toISOString();
      
      await this.db.query(
        `
          INSERT INTO users
          (id, username, email, password, role, status, created_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        `,
        [userId, username, email, hashedPassword, role, 'active', now, now]
      );
      
      // Get user
      const result = await this.db.query(
        `
          SELECT id, username, email, role, status, created_at, updated_at
          FROM users
          WHERE id = $1
        `,
        [userId]
      );
      
      const user = result.rows[0];
      
      this.logger.info(`User registered: ${username}`);
      
      return user;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      
      this.logger.error(`Error registering user: ${error.message}`, {
        username,
        email,
        error
      });
      
      throw ApiError.internal('Error registering user');
    }
  }
  
  /**
   * Login a user
   * 
   * @param {Object} credentials - User credentials
   * @returns {Promise<Object>} Authentication result
   */
  async login(credentials) {
    await this.initialize();
    
    const { username, password } = credentials;
    
    // Validate input
    if (!username || !password) {
      throw ApiError.badRequest('Username and password are required');
    }
    
    try {
      // Get user
      const result = await this.db.query(
        `
          SELECT *
          FROM users
          WHERE username = $1
        `,
        [username]
      );
      
      if (result.rows.length === 0) {
        throw ApiError.unauthorized('Invalid username or password');
      }
      
      const user = result.rows[0];
      
      // Check password
      const passwordMatch = await bcrypt.compare(password, user.password);
      
      if (!passwordMatch) {
        throw ApiError.unauthorized('Invalid username or password');
      }
      
      // Check status
      if (user.status !== 'active') {
        throw ApiError.forbidden(`User is ${user.status}`);
      }
      
      // Generate tokens
      const accessToken = this._generateAccessToken(user);
      const refreshToken = await this._generateRefreshToken(user.id);
      
      this.logger.info(`User logged in: ${username}`);
      
      return {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role
        },
        accessToken,
        refreshToken
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      
      this.logger.error(`Error logging in user: ${error.message}`, {
        username,
        error
      });
      
      throw ApiError.internal('Error logging in user');
    }
  }
  
  /**
   * Refresh access token
   * 
   * @param {String} refreshToken - Refresh token
   * @returns {Promise<Object>} Authentication result
   */
  async refreshToken(refreshToken) {
    await this.initialize();
    
    // Validate input
    if (!refreshToken) {
      throw ApiError.badRequest('Refresh token is required');
    }
    
    try {
      // Get refresh token
      const result = await this.db.query(
        `
          SELECT *
          FROM refresh_tokens
          WHERE token = $1
        `,
        [refreshToken]
      );
      
      if (result.rows.length === 0) {
        throw ApiError.unauthorized('Invalid refresh token');
      }
      
      const tokenData = result.rows[0];
      
      // Check expiration
      if (new Date(tokenData.expires_at) < new Date()) {
        // Delete expired token
        await this.db.query(
          `
            DELETE FROM refresh_tokens
            WHERE id = $1
          `,
          [tokenData.id]
        );
        
        throw ApiError.unauthorized('Refresh token expired');
      }
      
      // Get user
      const userResult = await this.db.query(
        `
          SELECT *
          FROM users
          WHERE id = $1
        `,
        [tokenData.user_id]
      );
      
      if (userResult.rows.length === 0) {
        throw ApiError.unauthorized('User not found');
      }
      
      const user = userResult.rows[0];
      
      // Check status
      if (user.status !== 'active') {
        throw ApiError.forbidden(`User is ${user.status}`);
      }
      
      // Generate new access token
      const accessToken = this._generateAccessToken(user);
      
      this.logger.info(`Access token refreshed for user: ${user.username}`);
      
      return {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role
        },
        accessToken,
        refreshToken
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      
      this.logger.error(`Error refreshing token: ${error.message}`, {
        error
      });
      
      throw ApiError.internal('Error refreshing token');
    }
  }
  
  /**
   * Logout a user
   * 
   * @param {String} refreshToken - Refresh token
   * @returns {Promise<Boolean>} Whether the logout was successful
   */
  async logout(refreshToken) {
    await this.initialize();
    
    // Validate input
    if (!refreshToken) {
      throw ApiError.badRequest('Refresh token is required');
    }
    
    try {
      // Delete refresh token
      const result = await this.db.query(
        `
          DELETE FROM refresh_tokens
          WHERE token = $1
          RETURNING *
        `,
        [refreshToken]
      );
      
      const deleted = result.rows.length > 0;
      
      if (deleted) {
        this.logger.info(`User logged out: ${result.rows[0].user_id}`);
      }
      
      return deleted;
    } catch (error) {
      this.logger.error(`Error logging out user: ${error.message}`, {
        error
      });
      
      throw ApiError.internal('Error logging out user');
    }
  }
  
  /**
   * Verify access token
   * 
   * @param {String} accessToken - Access token
   * @returns {Object} Token payload
   */
  verifyAccessToken(accessToken) {
    try {
      return jwt.verify(accessToken, this.options.jwtSecret);
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw ApiError.unauthorized('Access token expired');
      }
      
      throw ApiError.unauthorized('Invalid access token');
    }
  }
  
  /**
   * Generate access token
   * 
   * @param {Object} user - User
   * @returns {String} Access token
   * @private
   */
  _generateAccessToken(user) {
    const payload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      role: user.role
    };
    
    return jwt.sign(payload, this.options.jwtSecret, {
      expiresIn: this.options.jwtExpiresIn
    });
  }
  
  /**
   * Generate refresh token
   * 
   * @param {String} userId - User ID
   * @returns {Promise<String>} Refresh token
   * @private
   */
  async _generateRefreshToken(userId) {
    const tokenId = uuidv4();
    const token = uuidv4();
    const now = new Date();
    const expiresAt = new Date(now);
    
    // Parse refresh token expiration
    const expiresIn = this.options.refreshTokenExpiresIn;
    const match = expiresIn.match(/^(\d+)([smhdw])$/);
    
    if (match) {
      const value = parseInt(match[1], 10);
      const unit = match[2];
      
      switch (unit) {
        case 's':
          expiresAt.setSeconds(now.getSeconds() + value);
          break;
        case 'm':
          expiresAt.setMinutes(now.getMinutes() + value);
          break;
        case 'h':
          expiresAt.setHours(now.getHours() + value);
          break;
        case 'd':
          expiresAt.setDate(now.getDate() + value);
          break;
        case 'w':
          expiresAt.setDate(now.getDate() + (value * 7));
          break;
      }
    } else {
      // Default to 7 days
      expiresAt.setDate(now.getDate() + 7);
    }
    
    // Save refresh token
    await this.db.query(
      `
        INSERT INTO refresh_tokens
        (id, user_id, token, expires_at, created_at)
        VALUES ($1, $2, $3, $4, $5)
      `,
      [tokenId, userId, token, expiresAt.toISOString(), now.toISOString()]
    );
    
    return token;
  }
}

module.exports = {
  AuthService
};
