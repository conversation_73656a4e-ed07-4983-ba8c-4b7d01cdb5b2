/**
 * Authentication Module
 * 
 * Main entry point for authentication functionality.
 */

const { AuthService } = require('./authService');
const { createAuthMiddleware, createRoleMiddleware } = require('./authMiddleware');

/**
 * Create authentication module
 * 
 * @param {Object} options - Module options
 * @returns {Object} Authentication module
 */
function createAuthModule(options = {}) {
  const authService = new AuthService(options);
  
  const authenticate = createAuthMiddleware({
    authService,
    ...options.middleware
  });
  
  return {
    authService,
    authenticate,
    requireRole: createRoleMiddleware,
    
    // Initialization
    async initialize() {
      await authService.initialize();
    }
  };
}

module.exports = {
  createAuthModule,
  AuthService,
  createAuthMiddleware,
  createRoleMiddleware
};
