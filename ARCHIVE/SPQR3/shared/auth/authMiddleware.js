/**
 * Authentication Middleware
 * 
 * Middleware for authenticating requests.
 */

const { ApiError } = require('../middleware/errorHandler');
const { logger } = require('../utils/logger');

/**
 * Create authentication middleware
 * 
 * @param {Object} options - Middleware options
 * @returns {Function} Authentication middleware
 */
function createAuthMiddleware(options = {}) {
  const authService = options.authService;
  
  if (!authService) {
    throw new Error('Authentication service is required');
  }
  
  /**
   * Authentication middleware
   * 
   * @param {Object} req - Express request
   * @param {Object} res - Express response
   * @param {Function} next - Express next function
   */
  return function authenticate(req, res, next) {
    // Get authorization header
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return next(ApiError.unauthorized('Authorization header is required'));
    }
    
    // Check authorization header format
    const parts = authHeader.split(' ');
    
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return next(ApiError.unauthorized('Invalid authorization header format'));
    }
    
    const token = parts[1];
    
    try {
      // Verify token
      const payload = authService.verifyAccessToken(token);
      
      // Set user in request
      req.user = {
        id: payload.sub,
        username: payload.username,
        email: payload.email,
        role: payload.role
      };
      
      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Create role middleware
 * 
 * @param {Array<String>} roles - Allowed roles
 * @returns {Function} Role middleware
 */
function createRoleMiddleware(roles = []) {
  /**
   * Role middleware
   * 
   * @param {Object} req - Express request
   * @param {Object} res - Express response
   * @param {Function} next - Express next function
   */
  return function checkRole(req, res, next) {
    // Check if user exists
    if (!req.user) {
      return next(ApiError.unauthorized('User not authenticated'));
    }
    
    // Check if user has allowed role
    if (roles.length > 0 && !roles.includes(req.user.role)) {
      return next(ApiError.forbidden('Insufficient permissions'));
    }
    
    next();
  };
}

module.exports = {
  createAuthMiddleware,
  createRoleMiddleware
};
