/**
 * Webhook Service
 * 
 * Direct webhook implementation to replace n8n.
 * Provides a unified interface for webhook operations.
 */

const axios = require('axios');
const crypto = require('crypto');
const { logger } = require('../utils/logger');

/**
 * Webhook service
 */
class WebhookService {
  /**
   * Create a new webhook service instance
   * 
   * @param {Object} config - Webhook configuration
   */
  constructor(config = {}) {
    this.config = {
      enabled: config.enabled !== undefined ? config.enabled : (process.env.DIRECT_WEBHOOKS_ENABLED === 'true'),
      baseUrl: config.baseUrl || process.env.WEBHOOK_BASE_URL || '',
      secret: config.secret || process.env.WEBHOOK_SECRET || '',
      timeout: config.timeout || parseInt(process.env.WEBHOOK_TIMEOUT || '30000', 10),
      retryAttempts: config.retryAttempts || parseInt(process.env.WEBHOOK_RETRY_ATTEMPTS || '3', 10),
      retryDelay: config.retryDelay || parseInt(process.env.WEBHOOK_RETRY_DELAY || '1000', 10),
      rateLimitMax: config.rateLimitMax || parseInt(process.env.WEBHOOK_RATE_LIMIT_MAX || '100', 10),
      rateLimitWindowMs: config.rateLimitWindowMs || parseInt(process.env.WEBHOOK_RATE_LIMIT_WINDOW_MS || '60000', 10)
    };
    
    // Initialize rate limiting
    this.rateLimits = new Map();
  }
  
  /**
   * Check if webhooks are enabled
   * 
   * @returns {Boolean} Whether webhooks are enabled
   */
  isEnabled() {
    return this.config.enabled;
  }
  
  /**
   * Generate a webhook signature
   * 
   * @param {Object} payload - Webhook payload
   * @returns {String} Webhook signature
   */
  generateSignature(payload) {
    const hmac = crypto.createHmac('sha256', this.config.secret);
    hmac.update(JSON.stringify(payload));
    return hmac.digest('hex');
  }
  
  /**
   * Verify a webhook signature
   * 
   * @param {Object} payload - Webhook payload
   * @param {String} signature - Webhook signature
   * @returns {Boolean} Whether the signature is valid
   */
  verifySignature(payload, signature) {
    const expectedSignature = this.generateSignature(payload);
    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );
  }
  
  /**
   * Check rate limit for a webhook
   * 
   * @param {String} webhookId - Webhook ID
   * @returns {Boolean} Whether the rate limit is exceeded
   */
  checkRateLimit(webhookId) {
    const now = Date.now();
    const windowStart = now - this.config.rateLimitWindowMs;
    
    // Get or initialize rate limit data
    if (!this.rateLimits.has(webhookId)) {
      this.rateLimits.set(webhookId, []);
    }
    
    const requests = this.rateLimits.get(webhookId);
    
    // Remove expired requests
    const validRequests = requests.filter(timestamp => timestamp > windowStart);
    this.rateLimits.set(webhookId, validRequests);
    
    // Check if rate limit is exceeded
    if (validRequests.length >= this.config.rateLimitMax) {
      return true;
    }
    
    // Add current request
    validRequests.push(now);
    
    return false;
  }
  
  /**
   * Send a webhook
   * 
   * @param {String} endpoint - Webhook endpoint
   * @param {Object} payload - Webhook payload
   * @param {Object} options - Webhook options
   * @returns {Promise<Object>} Webhook response
   */
  async send(endpoint, payload, options = {}) {
    if (!this.isEnabled()) {
      logger.warn('Webhooks are disabled');
      return { success: false, message: 'Webhooks are disabled' };
    }
    
    const {
      method = 'POST',
      headers = {},
      timeout = this.config.timeout,
      retry = true
    } = options;
    
    const url = endpoint.startsWith('http') ? endpoint : `${this.config.baseUrl}${endpoint}`;
    
    // Generate signature
    const signature = this.generateSignature(payload);
    
    // Set headers
    const requestHeaders = {
      'Content-Type': 'application/json',
      'X-Webhook-Signature': signature,
      ...headers
    };
    
    // Send webhook with retry
    let attempts = 0;
    let lastError = null;
    
    while (attempts <= (retry ? this.config.retryAttempts : 0)) {
      try {
        if (attempts > 0) {
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay * attempts));
        }
        
        // Send request
        const response = await axios({
          method,
          url,
          data: payload,
          headers: requestHeaders,
          timeout
        });
        
        logger.info(`Webhook sent successfully to ${url}`, {
          endpoint,
          method,
          status: response.status
        });
        
        return {
          success: true,
          status: response.status,
          data: response.data
        };
      } catch (error) {
        lastError = error;
        
        logger.error(`Error sending webhook to ${url} (attempt ${attempts + 1}/${this.config.retryAttempts + 1})`, {
          endpoint,
          method,
          error: error.message,
          status: error.response?.status
        });
        
        attempts++;
      }
    }
    
    return {
      success: false,
      error: lastError.message,
      status: lastError.response?.status
    };
  }
  
  /**
   * Register a webhook handler
   * 
   * @param {Object} app - Express app
   * @param {String} path - Webhook path
   * @param {Function} handler - Webhook handler
   * @param {Object} options - Webhook options
   */
  registerHandler(app, path, handler, options = {}) {
    const {
      method = 'post',
      verifySignature = true,
      rateLimit = true
    } = options;
    
    // Register handler
    app[method.toLowerCase()](path, async (req, res) => {
      try {
        // Check if webhooks are enabled
        if (!this.isEnabled()) {
          return res.status(503).json({
            success: false,
            message: 'Webhooks are disabled'
          });
        }
        
        // Verify signature
        if (verifySignature && this.config.secret) {
          const signature = req.headers['x-webhook-signature'];
          
          if (!signature) {
            return res.status(401).json({
              success: false,
              message: 'Missing webhook signature'
            });
          }
          
          if (!this.verifySignature(req.body, signature)) {
            return res.status(401).json({
              success: false,
              message: 'Invalid webhook signature'
            });
          }
        }
        
        // Check rate limit
        if (rateLimit) {
          const webhookId = req.headers['x-webhook-id'] || req.ip;
          
          if (this.checkRateLimit(webhookId)) {
            return res.status(429).json({
              success: false,
              message: 'Rate limit exceeded'
            });
          }
        }
        
        // Handle webhook
        const result = await handler(req.body, req);
        
        // Send response
        return res.status(200).json({
          success: true,
          data: result
        });
      } catch (error) {
        logger.error(`Error handling webhook at ${path}`, {
          path,
          method,
          error: error.message
        });
        
        return res.status(500).json({
          success: false,
          message: 'Error handling webhook',
          error: error.message
        });
      }
    });
    
    logger.info(`Registered webhook handler at ${path}`, {
      path,
      method
    });
  }
}

// Create default webhook service
const defaultConfig = {
  enabled: process.env.DIRECT_WEBHOOKS_ENABLED === 'true',
  baseUrl: process.env.WEBHOOK_BASE_URL,
  secret: process.env.WEBHOOK_SECRET,
  timeout: parseInt(process.env.WEBHOOK_TIMEOUT || '30000', 10),
  retryAttempts: parseInt(process.env.WEBHOOK_RETRY_ATTEMPTS || '3', 10),
  retryDelay: parseInt(process.env.WEBHOOK_RETRY_DELAY || '1000', 10),
  rateLimitMax: parseInt(process.env.WEBHOOK_RATE_LIMIT_MAX || '100', 10),
  rateLimitWindowMs: parseInt(process.env.WEBHOOK_RATE_LIMIT_WINDOW_MS || '60000', 10)
};

const webhookService = new WebhookService(defaultConfig);

module.exports = {
  WebhookService,
  webhookService
};
