{"name": "spqr2", "version": "0.9.2", "description": "SPQR2 - Smart Process Query Response System - Ein intelligentes CRM+AI+Automatisierungssystem", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "lint": "eslint ."}, "dependencies": {"@anthropic-ai/tokenizer": "^0.0.4", "@supabase/supabase-js": "^2.49.4", "axios": "^1.8.4", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "gpt-3-encoder": "^1.1.4", "imap": "^0.8.19", "mailparser": "^3.7.2", "mongodb": "^5.1.0", "mongoose": "^7.8.6", "node-fetch": "^3.3.1", "nodemailer": "^6.10.1", "pg": "^8.15.0", "pgvector": "^0.2.0", "puppeteer": "^19.7.2", "socket.io": "^4.6.1", "uuid": "^11.1.0", "winston": "^3.17.0"}, "devDependencies": {"eslint": "^8.36.0", "jest": "^29.5.0", "nodemon": "^2.0.21"}, "engines": {"node": ">=16.0.0"}, "author": "speaqrAI", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/speaqrAI/SPQR2.git"}, "keywords": ["ai", "crm", "automation", "llm", "api-key-rotation", "resource-optimization"]}