/**
 * Custom Error Classes
 * 
 * This module defines custom error classes that can be used throughout the application
 * for more specific error handling and clearer error messages.
 */

class ValidationError extends Error {
  constructor(message) {
    super(message);
    this.name = "ValidationError";
    this.statusCode = 400;
  }
}

class AuthenticationError extends Error {
  constructor(message = "Authentication required") {
    super(message);
    this.name = "AuthenticationError";
    this.statusCode = 401;
  }
}

class UnauthorizedError extends AuthenticationError {
  constructor(message = "Unauthorized") {
    super(message);
    this.name = "UnauthorizedError";
    this.statusCode = 401;
  }
}

class AuthorizationError extends Error {
  constructor(message = "You do not have permission to perform this action") {
    super(message);
    this.name = "AuthorizationError";
    this.statusCode = 403;
  }
}

class NotFoundError extends Error {
  constructor(message = "Resource not found") {
    super(message);
    this.name = "NotFoundError";
    this.statusCode = 404;
  }
}

class ConflictError extends Error {
  constructor(message = "Resource already exists") {
    super(message);
    this.name = "ConflictError";
    this.statusCode = 409;
  }
}

class RateLimitError extends Error {
  constructor(message = "Rate limit exceeded") {
    super(message);
    this.name = "RateLimitError";
    this.statusCode = 429;
  }
}

class ExternalServiceError extends Error {
  constructor(serviceName, message = "External service error") {
    super();
    this.name = "ExternalServiceError";
    this.statusCode = 502;
    this.serviceName = serviceName;
  }
}

class DatabaseError extends Error {
  constructor(message = "Database error occurred") {
    super(message);
    this.name = "DatabaseError";
    this.statusCode = 500;
  }
}

/**
 * Create an API-friendly error response object
 * 
 * @param {Error} error - The error object
 * @returns {Object} Formatted error response
 */
function formatErrorResponse(error) {
  const statusCode = error.statusCode || 500;
  const errorType = error.name || "Error";
  
  return {
    status: "error",
    statusCode,
    error: {
      type: errorType,
      message: error.message,
      ...(process.env.NODE_ENV === "development" ? {
        stack: error.stack
      } : {})
    }
  };
}

module.exports = {
  ValidationError,
  AuthenticationError,
  UnauthorizedError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  ExternalServiceError,
  DatabaseError,
  formatErrorResponse
};
