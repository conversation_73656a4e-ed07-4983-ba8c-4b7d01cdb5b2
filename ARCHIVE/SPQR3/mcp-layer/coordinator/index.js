/**
 * MCP Coordinator
 *
 * Central orchestration layer for AI model management combining the capabilities
 * of the Model Registry, Dispatcher, and Resource Optimizer.
 */

const EventEmitter = require('events');
const logger = require('../utils/logger');
const modelRegistry = require('../registry');
const modelDispatcher = require('../dispatcher');
const resourceOptimizer = require('../optimizer');
const { v4: uuidv4 } = require('uuid');

class MCPCoordinator extends EventEmitter {
  constructor() {
    super();

    // Service state
    this.status = {
      ready: false,
      systemLoad: 1.0,
      queueLengths: {
        high: 0,
        medium: 0,
        low: 0,
        deferred: 0
      },
      activeModels: 0,
      activeProviders: new Set()
    };

    // Configure connections between components
    this.setupEventListeners();

    // Initialize and prepare systems
    this.initialize();

    logger.info('MCP Coordinator initialized');
  }

  /**
   * Setup event listeners for the various components
   */
  setupEventListeners() {
    // Listen for resource alerts from optimizer
    resourceOptimizer.on('resource-alert', alert => {
      this.handleResourceAlert(alert);
    });

    // Listen for model registration/updates from registry
    modelRegistry.on('model-registered', model => {
      this.status.activeModels++;
      this.status.activeProviders.add(model.provider);
      logger.info(`Model ${model.name} (${model.id}) registered and available`);
    });

    modelRegistry.on('model-updated', model => {
      logger.debug(`Model ${model.name} (${model.id}) updated`);
    });
  }

  /**
   * Initialize the coordinator and its dependencies
   */
  async initialize() {
    try {
      logger.info('Initializing MCP coordinator and dependencies...');

      // Get initial resource status
      const resourceStatus = await resourceOptimizer.getResourceStatus();
      this.status.systemLoad = resourceStatus.system.systemLoad;
      
      // Set queue lengths if available
      if (resourceStatus.queueStatus) {
        this.status.queueLengths = resourceStatus.queueStatus;
      }

      // Get initial model inventory
      const models = await modelRegistry.getModels();
      if (models && Array.isArray(models)) {
        this.status.activeModels = models.length;

        models.forEach(model => {
          if (model && model.provider) {
            this.status.activeProviders.add(model.provider);
          }
        });
      }

      this.status.ready = true;
      logger.info('MCP Coordinator is ready');
      this.emit('ready');
    } catch (error) {
      logger.error(`MCP Coordinator initialization failed: ${error.message}`);
      this.status.ready = false;
      this.emit('error', error);
    }
  }

  /**
   * Handle resource alerts from the optimizer
   * @param {Object} alert The resource alert object
   */
  handleResourceAlert(alert) {
    logger.warn(`Resource Alert: ${alert.type} - ${alert.message}`);

    // Update dispatcher settings based on alert
    switch (alert.type) {
      case 'high-load':
        modelDispatcher.loadFactor = alert.loadFactor || 1.5;
        modelDispatcher.throttleLevel = 2;
        break;
      case 'elevated-load':
        modelDispatcher.loadFactor = alert.loadFactor || 1.2;
        modelDispatcher.throttleLevel = 1;
        break;
      case 'low-resources':
        modelDispatcher.throttleLevel = 4;
        break;
      case 'token-limit':
        // If approaching limit prioritize cost
        modelDispatcher.costPriority = true;
        break;
      case 'cost-threshold':
        // If near limit increase throttling for that provider
        logger.warn(`Cost threshold reached for ${alert.provider}. Consider manual intervention.`);
        break;
      case 'prediction-alert':
        // If predicted to exceed limits proactively adjust
        modelDispatcher.costPriority = true;
        logger.warn(`Prediction alert for ${alert.provider}: ${alert.message}`);
        break;
      default:
        // Reset to normal if no specific alert
        modelDispatcher.loadFactor = 1.0;
        modelDispatcher.throttleLevel = 0;
        modelDispatcher.costPriority = false;
    }

    // Update internal status
    this.status.systemLoad = modelDispatcher.loadFactor;
  }

  /**
   * Invoke a model with automatic selection and resource management
   * @param {Object} request The request details
   * @returns {Promise<Object>} The model response
   */
  async invokeModel(request) {
    if (!this.status.ready) {
      throw new Error('MCP Coordinator is not ready');
    }

    const requestId = request.requestId || uuidv4();

    try {
      // Get current resource status
      const resourceStatus = await resourceOptimizer.getResourceStatus();

      // Select the best model using the dispatcher
      const selectedModel = await modelDispatcher.selectModel({
        ...request,
        requestId,
        resourceStatus
      });

      if (!selectedModel) {
        throw new Error('No suitable model found for the request');
      }

      // Check if the request should be queued or deferred
      const priority = request.priority || 'medium';
      const shouldQueue = this.shouldQueueRequest(priority, resourceStatus);

      if (shouldQueue) {
        logger.info(`Queueing request ${requestId} due to resource constraints or priority`);
        return resourceOptimizer.queueRequest({ ...request, requestId }, priority);
      }

      // Process the request immediately
      logger.debug(`Processing request ${requestId} with model ${selectedModel.name || selectedModel.id}`);
      const result = await modelDispatcher.processRequest(selectedModel, { ...request, requestId });

      // Track token usage if available
      if (result && result.tokenCount) {
        await resourceOptimizer.trackTokenUsage(selectedModel.provider, result.tokenCount);
      }

      // Update model stats if available
      if (modelRegistry.updateModelStats) {
        await modelRegistry.updateModelStats(selectedModel.id, {
          request: true,
          success: result.success,
          tokens: result.tokenCount,
          responseTime: result.processingTime
        });
      }

      return result;
    } catch (error) {
      logger.error(`Error invoking model for request ${requestId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Determine if a request should be queued based on priority and resources
   * @param {String} priority Request priority
   * @param {Object} resourceStatus Current resource status
   * @returns {Boolean} Whether the request should be queued
   */
  shouldQueueRequest(priority, resourceStatus) {
    const load = resourceStatus.system.systemLoad;

    if (priority === 'high') {
      // Only queue high priority if system load is extremely high
      return load > 0.95;
    } else if (priority === 'medium') {
      // Queue medium priority if load is high
      return load > 0.8;
    } else { // Low priority
      // Queue low priority if load is elevated
      return load > 0.6;
    }
  }

  /**
   * Get the current status of the MCP system
   * @returns {Promise<Object>} System status
   */
  async getStatus() {
    try {
      const resourceStatus = await resourceOptimizer.getResourceStatus();
      const models = await modelRegistry.getModels();

      this.status.systemLoad = resourceStatus.system.systemLoad;
      
      if (resourceStatus.queueStatus) {
        this.status.queueLengths = resourceStatus.queueStatus;
      }
      
      if (models && Array.isArray(models)) {
        this.status.activeModels = models.length;
        this.status.activeProviders.clear();
        models.forEach(model => {
          if (model && model.provider) {
            this.status.activeProviders.add(model.provider);
          }
        });
      }

      return {
        coordinatorStatus: this.status,
        resourceStatus: resourceStatus,
        modelCount: this.status.activeModels,
        providers: Array.from(this.status.activeProviders)
      };
    } catch (error) {
      logger.error(`Error getting MCP status: ${error.message}`);
      return { error: 'Failed to retrieve status' };
    }
  }

  /**
   * Shutdown the coordinator and its dependencies
   */
  async shutdown() {
    logger.info('Shutting down MCP Coordinator...');
    this.status.ready = false;

    // Shutdown components
    if (modelRegistry.shutdown) {
      await modelRegistry.shutdown();
    }
    
    if (resourceOptimizer.shutdown) {
      await resourceOptimizer.shutdown();
    }

    logger.info('MCP Coordinator shut down complete');
  }
}

module.exports = new MCPCoordinator();
