/**
 * Model Registry
 *
 * Central registry for all available AI models across different providers.
 * Maintains metadata about models such as capabilities, performance characteristics,
 * cost information, and historical performance metrics.
 *
 * Features:
 * - Dynamic model discovery and registration
 * - Performance tracking and benchmarking
 * - Model capability classification
 * - Version tracking and compatibility management
 * - Tag-based model filtering and discovery
 */

const EventEmitter = require('events');
const logger = require('../utils/logger');
const fs = require('fs').promises;
const path = require('path');
const Redis = require('ioredis');

class ModelRegistry extends EventEmitter {
  constructor() {
    super();

    // Models by ID
    this.models = new Map();

    // Cached queries
    this.modelQueryCache = new Map();

    // Tags by model ID
    this.modelTags = new Map();

    // Statistics by model ID
    this.modelStats = new Map();

    // Redis client for distributed data
    this.redisClient = null;

    // Configure caching
    this.cacheConfig = {
      enabled: true,
      ttl: 60 * 60 * 1000, // 1 hour cache TTL
      maxSize: 100 // Max number of cached queries
    };

    // Initialize Redis if available
    this.initRedis();

    // Model provider discovery
    this.providers = new Set(['openai', 'anthropic', 'ollama', 'localai']);

    // Load models from configuration
    this.loadInitialModels();

    // Set up periodic refresh of model information
    setInterval(() => this.refreshModelInformation(), 15 * 60 * 1000); // Every 15 minutes

    logger.info('Model Registry initialized');
  }

  /**
   * Initialize Redis connection if available
   */
  async initRedis() {
    if (process.env.REDIS_URL) {
      try {
        this.redisClient = new Redis(process.env.REDIS_URL);
        this.redisClient.on('error', (err) => {
          logger.error(`Redis connection error: ${err.message}`);
          this.redisClient = null;
        });
        logger.info('Connected to Redis for model registry data');

        // Load data from Redis
        await this.loadFromRedis();
      } catch (error) {
        logger.warn(`Failed to connect to Redis: ${error.message}. Using in-memory model registry.`);
        this.redisClient = null;
      }
    } else {
      logger.info('No REDIS_URL provided. Using in-memory model registry.');
    }
  }

  /**
   * Load model data from Redis
   */
  async loadFromRedis() {
    if (!this.redisClient) return;

    try {
      // Load models
      const modelIds = await this.redisClient.smembers('models:ids');

      for (const id of modelIds) {
        const modelData = await this.redisClient.hgetall(`model:${id}`);
        if (modelData && Object.keys(modelData).length > 0) {
          // Convert numeric strings to numbers
          ['costPerToken', 'maxTokens'].forEach(key => {
            if (modelData[key]) modelData[key] = parseFloat(modelData[key]);
          });

          // Parse JSON fields
          ['capabilities'].forEach(key => {
            if (modelData[key]) modelData[key] = JSON.parse(modelData[key]);
          });

          this.models.set(id, modelData);
        }
      }

      // Load tags
      for (const id of modelIds) {
        const tags = await this.redisClient.smembers(`model:${id}:tags`);
        if (tags && tags.length > 0) {
          this.modelTags.set(id, tags);
        }
      }

      // Load stats
      for (const id of modelIds) {
        const stats = await this.redisClient.hgetall(`model:${id}:stats`);
        if (stats && Object.keys(stats).length > 0) {
          // Convert numeric strings to numbers
          Object.keys(stats).forEach(key => {
            stats[key] = parseFloat(stats[key]);
          });
          this.modelStats.set(id, stats);
        }
      }

      logger.info(`Loaded ${this.models.size} models from Redis`);
    } catch (error) {
      logger.error(`Error loading data from Redis: ${error.message}`);
    }
  }

  /**
   * Load initial models from configuration
   */
  async loadInitialModels() {
    try {
      // Try to load from config file
      const configPath = path.join(__dirname, 'models-config.json');

      try {
        const configData = await fs.readFile(configPath, 'utf8');
        const models = JSON.parse(configData);

        for (const model of models) {
          await this.registerModel(model);
        }

        logger.info(`Loaded ${models.length} models from configuration file`);
      } catch (fileError) {
        logger.info('No model configuration file found, loading default models');
        await this.loadDefaultModels();
      }

      // Auto-discover available models from providers
      await this.discoverModels();
    } catch (error) {
      logger.error(`Error loading initial models: ${error.message}`);

      // Fall back to hardcoded default models
      await this.loadDefaultModels();
    }
  }

  /**
   * Load a set of default models
   */
  async loadDefaultModels() {
    // Default model definitions
    const defaultModels = [
      {
        id: 'gpt-4',
        name: 'GPT-4',
        provider: 'openai',
        costPerToken: 0.00006,
        maxTokens: 8192,
        capabilities: ['chat', 'reasoning', 'code'],
        latency: 'medium',
        active: true
      },
      {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        provider: 'openai',
        costPerToken: 0.000002,
        maxTokens: 4096,
        capabilities: ['chat', 'reasoning'],
        latency: 'low',
        active: true
      },
      {
        id: 'claude-3-opus',
        name: 'Claude 3 Opus',
        provider: 'anthropic',
        costPerToken: 0.00003,
        maxTokens: 200000,
        capabilities: ['chat', 'reasoning', 'code', 'vision'],
        latency: 'medium',
        active: true
      },
      {
        id: 'claude-3-sonnet',
        name: 'Claude 3 Sonnet',
        provider: 'anthropic',
        costPerToken: 0.00001,
        maxTokens: 200000,
        capabilities: ['chat', 'reasoning', 'code', 'vision'],
        latency: 'low',
        active: true
      },
      {
        id: 'claude-3-haiku',
        name: 'Claude 3 Haiku',
        provider: 'anthropic',
        costPerToken: 0.000002,
        maxTokens: 200000,
        capabilities: ['chat', 'reasoning', 'vision'],
        latency: 'low',
        active: true
      },
      {
        id: 'llama3',
        name: 'LLaMA 3',
        provider: 'ollama',
        costPerToken: 0,
        maxTokens: 8192,
        capabilities: ['chat', 'reasoning'],
        latency: 'medium',
        active: true
      }
    ];

    // Register default models
    for (const model of defaultModels) {
      await this.registerModel(model);
    }

    logger.info(`Loaded ${defaultModels.length} default models`);
  }

  /**
   * Discover available models from providers
   */
  async discoverModels() {
    logger.info('Discovering available models from providers');

    try {
      // Implementation would typically call provider APIs here
      // For now this is a placeholder for future provider-specific discovery

      const discoveredCount = 0;

      if (discoveredCount > 0) {
        logger.info(`Discovered ${discoveredCount} additional models`);
      } else {
        logger.debug('No additional models discovered');
      }
    } catch (error) {
      logger.error(`Error discovering models: ${error.message}`);
    }
  }

  /**
   * Refresh model information periodically
   */
  async refreshModelInformation() {
    logger.debug('Refreshing model information...');

    try {
      // Update model availability and capacity from providers
      let updatedCount = 0;

      for (const [id, model] of this.models.entries()) {
        // For actual implementation we would check with the provider
        // For now just ensure all models stay active
        if (!model.active) {
          model.active = true;
          await this.updateModel(id, { active: true });
          updatedCount++;
        }
      }

      if (updatedCount > 0) {
        logger.info(`Updated ${updatedCount} models during refresh`);
      }

      // Re-discover new models
      await this.discoverModels();
    } catch (error) {
      logger.error(`Error refreshing model information: ${error.message}`);
    }
  }

  /**
   * Register a new model in the registry
   * @param {Object} model The model definition
   * @returns {String} The model ID
   */
  async registerModel(model) {
    if (!model.id) {
      throw new Error('Model ID is required');
    }

    if (!model.provider) {
      throw new Error('Model provider is required');
    }

    // Add provider to known providers if new
    this.providers.add(model.provider);

    // Set defaults for optional fields
    const fullModel = {
      name: model.name || model.id,
      costPerToken: model.costPerToken || 0,
      maxTokens: model.maxTokens || 4096,
      capabilities: model.capabilities || ['chat'],
      latency: model.latency || 'medium',
      active: model.active !== undefined ? model.active : true,
      registeredAt: new Date().toISOString(),
      ...model
    };

    // Store in memory
    this.models.set(model.id, fullModel);

    // Store in Redis if available
    if (this.redisClient) {
      await this.redisClient.sadd('models:ids', model.id);

      // Store model data
      const modelData = { ...fullModel };

      // Convert arrays to JSON strings for Redis
      if (modelData.capabilities) {
        modelData.capabilities = JSON.stringify(modelData.capabilities);
      }

      await this.redisClient.hmset(`model:${model.id}`, modelData);
    }

    // Clear cache since registry has changed
    this.clearCache();

    logger.info(`Registered model ${model.name} (${model.id}) from ${model.provider}`);

    // Emit event for model registration
    this.emit('model-registered', fullModel);

    return model.id;
  }

  /**
   * Update an existing model in the registry
   * @param {String} id The model ID
   * @param {Object} updates The model updates
   * @returns {Boolean} Success status
   */
  async updateModel(id, updates) {
    if (!this.models.has(id)) {
      throw new Error(`Model ${id} not found`);
    }

    const model = this.models.get(id);
    const updatedModel = { ...model, ...updates, updatedAt: new Date().toISOString() };

    // Store in memory
    this.models.set(id, updatedModel);

    // Store in Redis if available
    if (this.redisClient) {
      // Convert arrays to JSON strings for Redis
      const redisUpdates = { ...updates };

      if (redisUpdates.capabilities) {
        redisUpdates.capabilities = JSON.stringify(redisUpdates.capabilities);
      }

      await this.redisClient.hmset(`model:${id}`, {
        ...redisUpdates,
        updatedAt: updatedModel.updatedAt
      });
    }

    // Clear cache since registry has changed
    this.clearCache();

    logger.debug(`Updated model ${id} with ${Object.keys(updates).join(', ')}`);

    // Emit event for model update
    this.emit('model-updated', updatedModel);

    return true;
  }

  /**
   * Deactivate a model in the registry
   * @param {String} id The model ID
   * @returns {Boolean} Success status
   */
  async deactivateModel(id) {
    if (!this.models.has(id)) {
      throw new Error(`Model ${id} not found`);
    }

    return this.updateModel(id, { active: false });
  }

  /**
   * Get a model by ID
   * @param {String} id The model ID
   * @returns {Object} The model definition
   */
  async getModel(id) {
    if (!this.models.has(id)) {
      return null;
    }

    return this.models.get(id);
  }

  /**
   * Get all models
   * @param {Boolean} activeOnly Only return active models
   * @returns {Array} Array of model definitions
   */
  async getModels(activeOnly = true) {
    const result = [];

    for (const model of this.models.values()) {
      if (!activeOnly || model.active) {
        result.push(model);
      }
    }

    return result;
  }

  /**
   * Find models matching query criteria
   * @param {Object} query Query parameters
   * @returns {Array} Array of matching models
   */
  async findModels(query = {}) {
    // Check if we have a cached result for this query
    const cacheKey = JSON.stringify(query);

    if (this.cacheConfig.enabled && this.modelQueryCache.has(cacheKey)) {
      const cachedResult = this.modelQueryCache.get(cacheKey);
      if (Date.now() - cachedResult.timestamp < this.cacheConfig.ttl) {
        return cachedResult.models;
      }
    }

    const result = [];

    for (const model of this.models.values()) {
      let matches = true;

      // Only include active models by default
      if (query.includeInactive !== true && !model.active) {
        continue;
      }

      // Filter by provider
      if (query.provider && model.provider !== query.provider) {
        matches = false;
      }

      // Filter by capability
      if (query.capability && !model.capabilities.includes(query.capability)) {
        matches = false;
      }

      // Filter by latency
      if (query.latency && model.latency !== query.latency) {
        matches = false;
      }

      // Filter by max cost
      if (query.maxCost !== undefined && model.costPerToken > query.maxCost) {
        matches = false;
      }

      // Filter by min tokens
      if (query.minTokens !== undefined && model.maxTokens < query.minTokens) {
        matches = false;
      }

      // Filter by tags
      if (query.tags && Array.isArray(query.tags) && query.tags.length > 0) {
        const modelTags = this.modelTags.get(model.id) || [];

        if (!query.tags.every(tag => modelTags.includes(tag))) {
          matches = false;
        }
      }

      if (matches) {
        result.push(model);
      }
    }

    // Cache the result
    if (this.cacheConfig.enabled) {
      this.modelQueryCache.set(cacheKey, {
        models: result,
        timestamp: Date.now()
      });

      // Trim cache if too large
      if (this.modelQueryCache.size > this.cacheConfig.maxSize) {
        // Remove oldest entries
        const entries = [...this.modelQueryCache.entries()];
        entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

        for (let i = 0; i < entries.length - this.cacheConfig.maxSize; i++) {
          this.modelQueryCache.delete(entries[i][0]);
        }
      }
    }

    return result;
  }

  /**
   * Add tags to a model
   * @param {String} id The model ID
   * @param {Array} tags The tags to add
   * @returns {Array} Updated tags
   */
  async addModelTags(id, tags) {
    if (!this.models.has(id)) {
      throw new Error(`Model ${id} not found`);
    }

    if (!Array.isArray(tags) || tags.length === 0) {
      return this.getModelTags(id);
    }

    // Get existing tags
    let modelTags = this.modelTags.get(id) || [];

    // Add new tags
    for (const tag of tags) {
      if (!modelTags.includes(tag)) {
        modelTags.push(tag);
      }
    }

    // Update in memory
    this.modelTags.set(id, modelTags);

    // Update in Redis if available
    if (this.redisClient) {
      if (tags.length > 0) {
        await this.redisClient.sadd(`model:${id}:tags`, ...tags);
      }
    }

    logger.debug(`Added tags to model ${id}: ${tags.join(', ')}`);

    return modelTags;
  }

  /**
   * Remove tags from a model
   * @param {String} id The model ID
   * @param {Array} tags The tags to remove
   * @returns {Array} Updated tags
   */
  async removeModelTags(id, tags) {
    if (!this.models.has(id)) {
      throw new Error(`Model ${id} not found`);
    }

    if (!Array.isArray(tags) || tags.length === 0) {
      return this.getModelTags(id);
    }

    // Get existing tags
    let modelTags = this.modelTags.get(id) || [];

    // Remove tags
    modelTags = modelTags.filter(tag => !tags.includes(tag));

    // Update in memory
    this.modelTags.set(id, modelTags);

    // Update in Redis if available
    if (this.redisClient) {
      if (tags.length > 0) {
        await this.redisClient.srem(`model:${id}:tags`, ...tags);
      }
    }

    logger.debug(`Removed tags from model ${id}: ${tags.join(', ')}`);

    return modelTags;
  }

  /**
   * Get tags for a model
   * @param {String} id The model ID
   * @returns {Array} Model tags
   */
  async getModelTags(id) {
    if (!this.models.has(id)) {
      throw new Error(`Model ${id} not found`);
    }

    // Get from memory
    return this.modelTags.get(id) || [];
  }

  /**
   * Update model stats
   * @param {String} id The model ID
   * @param {Object} stats The stats to update
   * @returns {Object} Updated stats
   */
  async updateModelStats(id, stats) {
    if (!this.models.has(id)) {
      throw new Error(`Model ${id} not found`);
    }

    // Get existing stats
    let modelStats = this.modelStats.get(id) || {
      requests: 0,
      successes: 0,
      failures: 0,
      totalTokens: 0,
      avgResponseTime: 0
    };

    // Update stats
    const updatedStats = { ...modelStats };

    if (stats.request) {
      updatedStats.requests = (updatedStats.requests || 0) + 1;
    }

    if (stats.success) {
      updatedStats.successes = (updatedStats.successes || 0) + 1;
    }

    if (stats.failure) {
      updatedStats.failures = (updatedStats.failures || 0) + 1;
    }

    if (stats.tokens) {
      updatedStats.totalTokens = (updatedStats.totalTokens || 0) + stats.tokens;
    }

    if (stats.responseTime) {
      const currentAvg = updatedStats.avgResponseTime || 0;
      const currentCount = updatedStats.requests || 1;
      updatedStats.avgResponseTime = (currentAvg * (currentCount - 1) + stats.responseTime) / currentCount;
    }

    // Add timestamp
    updatedStats.lastUpdated = new Date().toISOString();

    // Update in memory
    this.modelStats.set(id, updatedStats);

    // Update in Redis if available
    if (this.redisClient) {
      await this.redisClient.hmset(`model:${id}:stats`, updatedStats);
    }

    return updatedStats;
  }

  /**
   * Get model stats
   * @param {String} id The model ID
   * @returns {Object} Model stats
   */
  async getModelStats(id) {
    if (!this.models.has(id)) {
      throw new Error(`Model ${id} not found`);
    }

    // Get from memory
    return this.modelStats.get(id) || {
      requests: 0,
      successes: 0,
      failures: 0,
      totalTokens: 0,
      avgResponseTime: 0
    };
  }

  /**
   * Get model performance metrics
   * @param {String} id The model ID
   * @returns {Object} Performance metrics
   */
  async getModelPerformance(id) {
    if (!this.models.has(id)) {
      throw new Error(`Model ${id} not found`);
    }

    const model = await this.getModel(id);
    const stats = await this.getModelStats(id);

    const successRate = stats.requests > 0
      ? (stats.successes / stats.requests) * 100
      : 0;

    return {
      id: model.id,
      name: model.name,
      provider: model.provider,
      successRate,
      avgResponseTime: stats.avgResponseTime || 0,
      totalRequests: stats.requests || 0,
      totalTokens: stats.totalTokens || 0,
      costPerToken: model.costPerToken,
      estimatedCost: model.costPerToken * (stats.totalTokens || 0),
      lastUpdated: stats.lastUpdated || null
    };
  }

  /**
   * Clear the model query cache
   */
  clearCache() {
    this.modelQueryCache.clear();
    logger.debug('Cleared model query cache');
  }

  /**
   * Shutdown and cleanup
   */
  async shutdown() {
    logger.info('Shutting down Model Registry');

    // Close Redis connection if available
    if (this.redisClient) {
      await this.redisClient.quit();
    }
  }
}

module.exports = new ModelRegistry();
