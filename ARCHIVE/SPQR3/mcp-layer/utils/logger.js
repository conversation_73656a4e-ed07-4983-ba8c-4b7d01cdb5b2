/**
 * Simple Logger Module
 * 
 * Provides logging functionality for MCP components with configurable log levels.
 */

// Log levels
const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3
};

// Default configuration
const config = {
  level: LOG_LEVELS.INFO, // Default log level
  enableTimestamps: true,
  enableConsole: true,
  format: 'text' // 'text' or 'json'
};

/**
 * Format log timestamp
 * @returns {String} Formatted timestamp
 */
function getTimestamp() {
  const now = new Date();
  const timestamp = now.toISOString().replace('T', ' ').substr(0, 19);
  const ms = now.getMilliseconds().toString().padStart(3, '0');
  return `${timestamp}:${ms}`;
}

/**
 * Format log message
 * @param {String} level Log level
 * @param {String} message Log message
 * @returns {String} Formatted log message
 */
function formatLog(level, message) {
  const prefix = config.enableTimestamps ? `${getTimestamp()} ${level}: ` : `${level}: `;
  
  if (config.format === 'json') {
    return JSON.stringify({
      timestamp: config.enableTimestamps ? getTimestamp() : undefined,
      level,
      message
    });
  }
  
  return `${prefix}${message}`;
}

/**
 * Log error message
 * @param {String} message Message to log
 */
function error(message) {
  if (config.level >= LOG_LEVELS.ERROR && config.enableConsole) {
    console.error(formatLog('error', message));
  }
}

/**
 * Log warning message
 * @param {String} message Message to log
 */
function warn(message) {
  if (config.level >= LOG_LEVELS.WARN && config.enableConsole) {
    console.warn(formatLog('warn', message));
  }
}

/**
 * Log info message
 * @param {String} message Message to log
 */
function info(message) {
  if (config.level >= LOG_LEVELS.INFO && config.enableConsole) {
    console.info(formatLog('info', message));
  }
}

/**
 * Log debug message
 * @param {String} message Message to log
 */
function debug(message) {
  if (config.level >= LOG_LEVELS.DEBUG && config.enableConsole) {
    console.debug(formatLog('debug', message));
  }
}

/**
 * Configure logger
 * @param {Object} options Logger configuration options
 */
function configure(options = {}) {
  if (options.level !== undefined && LOG_LEVELS[options.level.toUpperCase()] !== undefined) {
    config.level = LOG_LEVELS[options.level.toUpperCase()];
  } else if (typeof options.level === 'number' && options.level >= 0 && options.level <= 3) {
    config.level = options.level;
  }
  
  if (options.enableTimestamps !== undefined) {
    config.enableTimestamps = !!options.enableTimestamps;
  }
  
  if (options.enableConsole !== undefined) {
    config.enableConsole = !!options.enableConsole;
  }
  
  if (options.format !== undefined && ['text', 'json'].includes(options.format)) {
    config.format = options.format;
  }
}

// Expose logger interface
module.exports = {
  error,
  warn,
  info,
  debug,
  configure,
  LOG_LEVELS
};
