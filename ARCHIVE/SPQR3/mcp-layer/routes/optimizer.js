/**
 * MCP Layer - Optimizer Routes
 * 
 * This file defines the routes for the Resource Optimizer component of the MCP Layer.
 * It provides endpoints for monitoring resource usage and optimization settings.
 */

const express = require('express');
const router = express.Router();
const resourceOptimizer = require('../optimizer');
const logger = require('../utils/logger');

/**
 * GET /api/optimizer/status
 * 
 * Returns the current status of the Resource Optimizer including resource usage metrics
 */
router.get('/status', async (req, res, next) => {
  try {
    const status = await resourceOptimizer.getStatus();
    
    res.status(200).json({
      status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error(`Error getting optimizer status: ${error.message}`, { error, requestId: req.id });
    next(error);
  }
});

/**
 * GET /api/optimizer/metrics
 * 
 * Returns detailed resource usage metrics
 */
router.get('/metrics', async (req, res, next) => {
  try {
    const metrics = await resourceOptimizer.getMetrics();
    
    res.status(200).json({
      metrics,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error(`Error getting optimizer metrics: ${error.message}`, { error, requestId: req.id });
    next(error);
  }
});

/**
 * GET /api/optimizer/token-usage
 * 
 * Returns token usage statistics across all models
 */
router.get('/token-usage', async (req, res, next) => {
  try {
    const tokenUsage = await resourceOptimizer.getTokenUsage();
    
    res.status(200).json({
      tokenUsage,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error(`Error getting token usage: ${error.message}`, { error, requestId: req.id });
    next(error);
  }
});

/**
 * POST /api/optimizer/settings
 * 
 * Updates resource optimizer settings
 */
router.post('/settings', async (req, res, next) => {
  try {
    const settings = req.body;
    
    // Validate settings
    if (!settings || Object.keys(settings).length === 0) {
      return res.status(400).json({
        error: {
          message: 'No settings provided',
          requestId: req.id
        }
      });
    }
    
    const updatedSettings = await resourceOptimizer.updateSettings(settings);
    
    res.status(200).json({
      settings: updatedSettings,
      message: 'Resource optimizer settings updated successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error(`Error updating optimizer settings: ${error.message}`, { error, requestId: req.id });
    next(error);
  }
});

/**
 * POST /api/optimizer/reset
 * 
 * Resets resource usage counters
 */
router.post('/reset', async (req, res, next) => {
  try {
    await resourceOptimizer.resetCounters();
    
    res.status(200).json({
      message: 'Resource usage counters reset successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error(`Error resetting optimizer counters: ${error.message}`, { error, requestId: req.id });
    next(error);
  }
});

module.exports = router;
