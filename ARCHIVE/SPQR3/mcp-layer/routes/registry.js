/**
 * MCP Layer - Registry Routes
 * 
 * This file defines the routes for the Model Registry component of the MCP Layer.
 * It provides endpoints for listing, adding, and managing models in the registry.
 */

const express = require('express');
const router = express.Router();
const modelRegistry = require('../registry');
const logger = require('../utils/logger');

/**
 * GET /api/registry/models
 * 
 * Returns a list of all models in the registry with their metadata
 */
router.get('/models', async (req, res, next) => {
  try {
    const models = await modelRegistry.getAllModels();
    
    res.status(200).json({
      models,
      count: models.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error(`Error getting models from registry: ${error.message}`, { error, requestId: req.id });
    next(error);
  }
});

/**
 * GET /api/registry/models/:id
 * 
 * Returns details for a specific model by ID
 */
router.get('/models/:id', async (req, res, next) => {
  try {
    const modelId = req.params.id;
    const model = await modelRegistry.getModelById(modelId);
    
    if (!model) {
      return res.status(404).json({
        error: {
          message: `Model with ID ${modelId} not found`,
          requestId: req.id
        }
      });
    }
    
    res.status(200).json({
      model,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error(`Error getting model ${req.params.id} from registry: ${error.message}`, { error, requestId: req.id });
    next(error);
  }
});

/**
 * POST /api/registry/models
 * 
 * Registers a new model in the registry
 */
router.post('/models', async (req, res, next) => {
  try {
    const modelData = req.body;
    
    // Validate required fields
    if (!modelData.name || !modelData.provider || !modelData.modelId) {
      return res.status(400).json({
        error: {
          message: 'Missing required fields: name, provider, and modelId are required',
          requestId: req.id
        }
      });
    }
    
    const newModel = await modelRegistry.registerModel(modelData);
    
    res.status(201).json({
      model: newModel,
      message: `Model ${newModel.name} registered successfully`,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error(`Error registering model: ${error.message}`, { error, requestId: req.id });
    next(error);
  }
});

/**
 * DELETE /api/registry/models/:id
 * 
 * Removes a model from the registry
 */
router.delete('/models/:id', async (req, res, next) => {
  try {
    const modelId = req.params.id;
    const result = await modelRegistry.removeModel(modelId);
    
    if (!result) {
      return res.status(404).json({
        error: {
          message: `Model with ID ${modelId} not found`,
          requestId: req.id
        }
      });
    }
    
    res.status(200).json({
      message: `Model ${modelId} removed successfully`,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error(`Error removing model ${req.params.id} from registry: ${error.message}`, { error, requestId: req.id });
    next(error);
  }
});

module.exports = router;
