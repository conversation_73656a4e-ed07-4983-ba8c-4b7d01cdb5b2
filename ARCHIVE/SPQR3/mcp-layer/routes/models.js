/**
 * Models API Routes
 * 
 * Endpoints for model management and information.
 */

const express = require('express');
const router = express.Router();
const logger = require('../utils/logger');
const modelRegistry = require('../registry');

/**
 * GET /api/models
 * 
 * Get all available models
 */
router.get('/', async (req, res, next) => {
  try {
    // Get active models by default, or all models if specified
    const showAll = req.query.all === 'true';
    const models = await modelRegistry.getModels(showAll);
    
    res.json({
      models,
      count: models.length,
      requestId: req.id
    });
  } catch (error) {
    logger.error(`Error getting models: ${error.message}`);
    next(error);
  }
});

/**
 * GET /api/models/:id
 * 
 * Get details for a specific model
 */
router.get('/:id', async (req, res, next) => {
  try {
    const model = await modelRegistry.getModel(req.params.id);
    
    if (!model) {
      return res.status(404).json({
        error: {
          message: `Model with ID ${req.params.id} not found`,
          requestId: req.id
        }
      });
    }
    
    res.json({
      model,
      requestId: req.id
    });
  } catch (error) {
    logger.error(`Error getting model ${req.params.id}: ${error.message}`);
    next(error);
  }
});

/**
 * POST /api/models
 * 
 * Register a new model
 */
router.post('/', async (req, res, next) => {
  try {
    // Basic validation
    if (!req.body.id || !req.body.provider) {
      return res.status(400).json({
        error: {
          message: 'Model ID and provider are required',
          requestId: req.id
        }
      });
    }
    
    // Register the model
    const model = await modelRegistry.registerModel(req.body);
    
    res.status(201).json({
      model,
      message: `Model ${model.id} registered successfully`,
      requestId: req.id
    });
  } catch (error) {
    logger.error(`Error registering model: ${error.message}`);
    next(error);
  }
});

/**
 * PUT /api/models/:id
 * 
 * Update a model
 */
router.put('/:id', async (req, res, next) => {
  try {
    // Check if model exists
    const existingModel = await modelRegistry.getModel(req.params.id);
    
    if (!existingModel) {
      return res.status(404).json({
        error: {
          message: `Model with ID ${req.params.id} not found`,
          requestId: req.id
        }
      });
    }
    
    // Update the model
    const updatedModel = await modelRegistry.updateModel(req.params.id, req.body);
    
    res.json({
      model: updatedModel,
      message: `Model ${updatedModel.id} updated successfully`,
      requestId: req.id
    });
  } catch (error) {
    logger.error(`Error updating model ${req.params.id}: ${error.message}`);
    next(error);
  }
});

/**
 * DELETE /api/models/:id
 * 
 * Deactivate a model
 */
router.delete('/:id', async (req, res, next) => {
  try {
    // Check if model exists
    const existingModel = await modelRegistry.getModel(req.params.id);
    
    if (!existingModel) {
      return res.status(404).json({
        error: {
          message: `Model with ID ${req.params.id} not found`,
          requestId: req.id
        }
      });
    }
    
    // Deactivate the model
    await modelRegistry.deactivateModel(req.params.id);
    
    res.json({
      message: `Model ${req.params.id} deactivated successfully`,
      requestId: req.id
    });
  } catch (error) {
    logger.error(`Error deactivating model ${req.params.id}: ${error.message}`);
    next(error);
  }
});

module.exports = router;
