/**
 * Invoke API Routes
 * 
 * Endpoints for model invocation and request processing.
 */

const express = require('express');
const router = express.Router();
const logger = require('../utils/logger');
const mcpCoordinator = require('../coordinator');

/**
 * POST /api/invoke
 * 
 * Invoke a model with automatic selection
 */
router.post('/', async (req, res, next) => {
  try {
    // Add request ID if not present
    if (!req.body.requestId) {
      req.body.requestId = req.id;
    }
    
    // Invoke model through the coordinator
    const result = await mcpCoordinator.invokeModel(req.body);
    
    // Return the model response
    res.json({
      ...result,
      requestId: req.id
    });
  } catch (error) {
    logger.error(`Error invoking model: ${error.message}`);
    next(error);
  }
});

/**
 * POST /api/invoke/:modelId
 * 
 * Invoke a specific model
 */
router.post('/:modelId', async (req, res, next) => {
  try {
    // Add request ID and model ID if not present
    if (!req.body.requestId) {
      req.body.requestId = req.id;
    }
    
    // Add model ID to the request
    req.body.modelId = req.params.modelId;
    
    // Invoke model through the coordinator
    const result = await mcpCoordinator.invokeModel(req.body);
    
    // Return the model response
    res.json({
      ...result,
      requestId: req.id
    });
  } catch (error) {
    logger.error(`Error invoking model ${req.params.modelId}: ${error.message}`);
    next(error);
  }
});

/**
 * GET /api/invoke/status/:requestId
 * 
 * Check the status of a previously submitted request
 */
router.get('/status/:requestId', async (req, res, next) => {
  try {
    // This would check the status of a queued request
    // For now, we'll return a placeholder response
    
    res.json({
      requestId: req.params.requestId,
      status: 'unknown',
      message: 'Request status checking not implemented yet',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error(`Error checking request status: ${error.message}`);
    next(error);
  }
});

module.exports = router;
