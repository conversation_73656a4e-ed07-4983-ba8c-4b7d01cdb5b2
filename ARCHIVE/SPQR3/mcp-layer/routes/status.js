/**
 * Status API Routes
 * 
 * Endpoints for system status and monitoring.
 */

const express = require('express');
const router = express.Router();
const logger = require('../utils/logger');
const mcpCoordinator = require('../coordinator');

/**
 * GET /api/status
 * 
 * Get the overall system status
 */
router.get('/', async (req, res, next) => {
  try {
    const status = await mcpCoordinator.getStatus();
    
    res.json({
      status,
      timestamp: new Date().toISOString(),
      requestId: req.id
    });
  } catch (error) {
    logger.error(`Error getting system status: ${error.message}`);
    next(error);
  }
});

/**
 * GET /api/status/health
 * 
 * Health check for all components
 */
router.get('/health', async (req, res, next) => {
  try {
    // Check basic status
    const isReady = mcpCoordinator.status && mcpCoordinator.status.ready;
    
    // Determine health status
    const health = {
      coordinator: isReady ? 'healthy' : 'degraded',
      registry: 'healthy', // Simplified for now
      dispatcher: 'healthy', // Simplified for now
      optimizer: 'healthy' // Simplified for now
    };
    
    // Set appropriate status code
    const statusCode = Object.values(health).every(status => status === 'healthy') ? 200 : 503;
    
    res.status(statusCode).json({
      status: statusCode === 200 ? 'healthy' : 'degraded',
      components: health,
      timestamp: new Date().toISOString(),
      requestId: req.id
    });
  } catch (error) {
    logger.error(`Error checking health: ${error.message}`);
    
    res.status(500).json({
      status: 'error',
      error: error.message,
      timestamp: new Date().toISOString(),
      requestId: req.id
    });
  }
});

/**
 * GET /api/status/metrics
 * 
 * Get system metrics for monitoring
 */
router.get('/metrics', async (req, res, next) => {
  try {
    // Get status from coordinator
    const status = await mcpCoordinator.getStatus();
    
    // Format metrics
    const metrics = {
      systemLoad: status.resourceStatus ? status.resourceStatus.system.systemLoad : 0,
      cpuLoad: status.resourceStatus ? status.resourceStatus.system.cpuLoad : 0,
      memoryUsage: status.resourceStatus ? status.resourceStatus.system.memoryUsage : 0,
      queueSizes: status.coordinatorStatus ? status.coordinatorStatus.queueLengths : {
        high: 0,
        medium: 0,
        low: 0,
        deferred: 0,
        total: 0
      },
      activeModels: status.modelCount || 0,
      activeProviders: status.providers || []
    };
    
    res.json({
      metrics,
      timestamp: new Date().toISOString(),
      requestId: req.id
    });
  } catch (error) {
    logger.error(`Error getting metrics: ${error.message}`);
    next(error);
  }
});

module.exports = router;
