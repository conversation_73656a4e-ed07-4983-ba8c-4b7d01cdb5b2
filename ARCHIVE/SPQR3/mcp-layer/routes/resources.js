/**
 * Resources API Routes
 * 
 * Endpoints for resource management and monitoring.
 */

const express = require('express');
const router = express.Router();
const logger = require('../utils/logger');
const resourceOptimizer = require('../optimizer');

/**
 * GET /api/resources/status
 * 
 * Get current resource usage status
 */
router.get('/status', async (req, res, next) => {
  try {
    const status = await resourceOptimizer.getResourceStatus();
    
    res.json({
      resourceStatus: status,
      timestamp: new Date().toISOString(),
      requestId: req.id
    });
  } catch (error) {
    logger.error(`Error getting resource status: ${error.message}`);
    next(error);
  }
});

/**
 * GET /api/resources/usage
 * 
 * Get token usage statistics
 */
router.get('/usage', async (req, res, next) => {
  try {
    // For now, return placeholder data since we have a simplified implementation
    res.json({
      usage: {
        total: 0,
        daily: resourceOptimizer.getDailyTokenUsage ? 
               resourceOptimizer.getDailyTokenUsage() : 0,
        monthly: resourceOptimizer.getMonthlyTokenUsage ? 
                resourceOptimizer.getMonthlyTokenUsage() : 0,
        byProvider: {}
      },
      timestamp: new Date().toISOString(),
      requestId: req.id
    });
  } catch (error) {
    logger.error(`Error getting token usage: ${error.message}`);
    next(error);
  }
});

/**
 * GET /api/resources/cost
 * 
 * Get cost tracking information
 */
router.get('/cost', async (req, res, next) => {
  try {
    // For now, return placeholder data since we have a simplified implementation
    res.json({
      cost: {
        total: 0,
        daily: resourceOptimizer.getDailyCost ? 
               resourceOptimizer.getDailyCost() : 0,
        monthly: resourceOptimizer.getMonthlyCost ? 
                 resourceOptimizer.getMonthlyCost() : 0,
        byProvider: {}
      },
      timestamp: new Date().toISOString(),
      requestId: req.id
    });
  } catch (error) {
    logger.error(`Error getting cost information: ${error.message}`);
    next(error);
  }
});

/**
 * POST /api/resources/track
 * 
 * Manually track token usage
 */
router.post('/track', async (req, res, next) => {
  try {
    const { provider, tokens, model, userId, costPerToken } = req.body;
    
    if (!provider || typeof tokens !== 'number') {
      return res.status(400).json({
        error: {
          message: 'Provider and token count are required',
          requestId: req.id
        }
      });
    }
    
    // Track token usage if the method exists
    if (resourceOptimizer.trackTokenUsage) {
      await resourceOptimizer.trackTokenUsage(provider, tokens, {
        model,
        userId,
        costPerToken
      });
      
      res.json({
        message: `Successfully tracked ${tokens} tokens for ${provider}`,
        requestId: req.id
      });
    } else {
      // Method doesn't exist in our simplified implementation
      res.json({
        message: 'Token tracking not implemented in this version',
        requestId: req.id
      });
    }
  } catch (error) {
    logger.error(`Error tracking tokens: ${error.message}`);
    next(error);
  }
});

module.exports = router;
