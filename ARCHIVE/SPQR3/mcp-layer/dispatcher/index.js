/**
 * Model Dispatcher
 *
 * Responsible for selecting and using the optimal AI model based on task
 * requirements and system resources. Works with the Model Registry to find
 * available models and the Resource Optimizer to manage resource usage.
 *
 * Features:
 * - Intelligent model selection based on task complexity and requirements
 * - Fallback mechanisms for model unavailability
 * - Load balancing across multiple model providers
 * - Cost optimization strategies
 * - Performance monitoring and feedback loop
 */

const logger = require('../utils/logger');
const modelRegistry = require('../registry');
const { v4: uuidv4 } = require('uuid');

class ModelDispatcher {
  constructor() {
    // Dispatcher configuration
    this.config = {
      defaultModel: 'gpt-3.5-turbo',
      fallbackEnabled: true,
      loadBalancingEnabled: true,
      costOptimizationEnabled: true,
      performanceTrackingEnabled: true,
      maxRetries: 2,
      requestTimeout: 30000 // 30 seconds
    };

    // Dispatcher state
    this.loadFactor = 1.0; // Multiplier for load balancing (adjusted by coordinator)
    this.throttleLevel = 0; // 0-4 where 0 is no throttling, 4 is max throttling
    this.costPriority = false; // Whether to prioritize cost over performance

    // Provider-specific adapters
    this.modelAdapters = new Map();

    // Initialize adapters
    this.initializeAdapters();

    logger.info('Model Dispatcher initialized');
  }

  /**
   * Initialize model adapters for different providers
   */
  initializeAdapters() {
    // Register adapters for different model providers
    this.registerAdapter('openai', require('./adapters/openai-adapter'));
    this.registerAdapter('anthropic', require('./adapters/anthropic-adapter'));
    this.registerAdapter('ollama', require('./adapters/ollama-adapter'));
    this.registerAdapter('localai', require('./adapters/localai-adapter'));

    // Add more adapters as needed

    logger.info(`Initialized ${this.modelAdapters.size} model adapters`);
  }

  /**
   * Register a model adapter for a provider
   * @param {String} provider The provider name
   * @param {Object} adapter The adapter implementation
   */
  registerAdapter(provider, adapter) {
    if (!adapter) {
      // Create a placeholder adapter if the actual one isn't available
      // This allows the system to function even if some adapters are missing
      adapter = {
        prepareRequest: (model, request) => request,
        processResponse: (model, response) => response,
        handleError: (model, error) => ({ error: error.message }),
        isAvailable: () => false
      };
      logger.warn(`Using placeholder adapter for ${provider}`);
    }

    this.modelAdapters.set(provider, adapter);
  }

  /**
   * Select the best model for a given request
   * @param {Object} request The request object
   * @returns {Promise<Object>} The selected model
   */
  async selectModel(request) {
    try {
      // Extract request parameters
      const { complexity, capability, priority, costConstraint } = request;

      // Build query for model registry
      const query = {
        // If complexity is specified, use it to filter models
        ...(complexity && {
          minTokens: this.getMinTokensForComplexity(complexity)
        }),

        // If specific capability is required, filter for it
        ...(capability && { capability }),

        // If cost constraint is specified, use it
        ...(costConstraint && { maxCost: costConstraint })
      };

      // Get matching models from registry
      let models = await modelRegistry.findModels(query);

      // If no models found, try with less restrictive query
      if (models.length === 0) {
        logger.warn(`No models found for initial query, trying less restrictive query`);

        // Remove capability constraint if present
        if (query.capability) {
          delete query.capability;
          models = await modelRegistry.findModels(query);
        }

        // If still no models, try without token constraint
        if (models.length === 0 && query.minTokens) {
          delete query.minTokens;
          models = await modelRegistry.findModels(query);
        }

        // Last resort: get all active models
        if (models.length === 0) {
          models = await modelRegistry.getModels(true);
        }
      }

      // If still no models, return null
      if (models.length === 0) {
        logger.error('No available models found');
        return null;
      }

      // Apply selection strategy based on request and system state
      const selectedModel = this.applySelectionStrategy(models, request);

      if (!selectedModel) {
        logger.error('Model selection strategy returned no model');
        return null;
      }

      logger.debug(`Selected model ${selectedModel.name} (${selectedModel.id}) for request`);
      return selectedModel;

    } catch (error) {
      logger.error(`Error selecting model: ${error.message}`);

      // Fallback to default model if available
      try {
        const defaultModel = await modelRegistry.getModel(this.config.defaultModel);
        if (defaultModel && defaultModel.active) {
          logger.warn(`Falling back to default model ${this.config.defaultModel}`);
          return defaultModel;
        }
      } catch (fallbackError) {
        logger.error(`Error getting default model: ${fallbackError.message}`);
      }

      return null;
    }
  }

  /**
   * Apply model selection strategy based on request and system state
   * @param {Array} models Array of available models
   * @param {Object} request The request object
   * @returns {Object} The selected model
   */
  applySelectionStrategy(models, request) {
    // Extract relevant request parameters
    const { priority, resourceStatus } = request;

    // Filter out models without adapters
    models = models.filter(model => this.modelAdapters.has(model.provider));

    if (models.length === 0) return null;

    // Different strategies based on system state and request

    // 1. If system is under high load, prioritize efficiency
    if (this.throttleLevel >= 3 || (resourceStatus && resourceStatus.system.systemLoad > 0.9)) {
      // Sort by lowest cost and lowest latency
      models.sort((a, b) => {
        // First by cost
        const costDiff = a.costPerToken - b.costPerToken;
        if (costDiff !== 0) return costDiff;

        // Then by latency
        const latencyMap = { low: 1, medium: 2, high: 3 };
        return latencyMap[a.latency] - latencyMap[b.latency];
      });

      return models[0];
    }

    // 2. If cost optimization is priority, select cheapest capable model
    if (this.costPriority || (request.costOptimize === true)) {
      // Sort by cost (cheapest first)
      models.sort((a, b) => a.costPerToken - b.costPerToken);
      return models[0];
    }

    // 3. For high priority requests, select best performance model
    if (priority === 'high' || priority === 'critical') {
      // Sort by capabilities (more is better) and then by latency (lower is better)
      models.sort((a, b) => {
        // First by number of capabilities
        const capDiff = b.capabilities.length - a.capabilities.length;
        if (capDiff !== 0) return capDiff;

        // Then by latency
        const latencyMap = { low: 1, medium: 2, high: 3 };
        return latencyMap[a.latency] - latencyMap[b.latency];
      });

      return models[0];
    }

    // 4. For medium priority, balance performance and cost
    if (priority === 'medium') {
      // Create a score based on capabilities, cost, and latency
      models.forEach(model => {
        const capabilityScore = model.capabilities.length / 5; // Normalize to ~0-1
        const costScore = 1 - (model.costPerToken / 0.0001); // Normalize to ~0-1, higher is better (cheaper)
        const latencyScore = { low: 1, medium: 0.6, high: 0.3 }[model.latency];

        model.balancedScore = (capabilityScore * 0.4) + (costScore * 0.4) + (latencyScore * 0.2);
      });

      // Sort by balanced score (higher is better)
      models.sort((a, b) => b.balancedScore - a.balancedScore);
      return models[0];
    }

    // 5. For low priority, prefer cheaper models
    if (priority === 'low') {
      // Sort primarily by cost, secondarily by latency
      models.sort((a, b) => {
        const costDiff = a.costPerToken - b.costPerToken;
        if (costDiff !== 0) return costDiff;

        const latencyMap = { low: 1, medium: 2, high: 3 };
        return latencyMap[a.latency] - latencyMap[b.latency];
      });

      return models[0];
    }

    // Default: return the first model
    return models[0];
  }

  /**
   * Get minimum tokens required for a given complexity level
   * @param {String} complexity The complexity level (low, medium, high)
   * @returns {Number} The minimum tokens required
   */
  getMinTokensForComplexity(complexity) {
    switch (complexity) {
      case 'high':
        return 8192;
      case 'medium':
        return 4096;
      case 'low':
      default:
        return 2048;
    }
  }

  /**
   * Process a request using the specified model
   * @param {Object} model The model to use
   * @param {Object} request The request object
   * @returns {Promise<Object>} The response
   */
  async processRequest(model, request) {
    if (!model) {
      throw new Error('Model is required for processing request');
    }

    const adapter = this.modelAdapters.get(model.provider);

    if (!adapter) {
      throw new Error(`No adapter available for provider ${model.provider}`);
    }

    // Check if adapter is available
    if (adapter.isAvailable && !adapter.isAvailable()) {
      throw new Error(`Adapter for ${model.provider} is not available`);
    }

    const requestId = request.requestId || uuidv4();
    let retryCount = 0;

    // Start timing
    const startTime = Date.now();

    try {
      // Prepare the request for the specific provider
      const preparedRequest = adapter.prepareRequest(model, request);

      // Add request metadata
      preparedRequest.requestId = requestId;
      preparedRequest.timestamp = new Date().toISOString();
      preparedRequest.model = model.id;

      logger.debug(`Processing request ${requestId} with model ${model.id} (${model.provider})`);

      // Process the request with retry logic
      let response;
      let error;

      for (retryCount = 0; retryCount <= this.config.maxRetries; retryCount++) {
        try {
          if (retryCount > 0) {
            logger.warn(`Retry ${retryCount}/${this.config.maxRetries} for request ${requestId}`);
          }

          // Call the model through the adapter
          response = await this.callModel(adapter, model, preparedRequest);
          error = null;
          break;
        } catch (err) {
          error = err;

          // If this is not a retryable error, break immediately
          if (!this.isRetryableError(err)) {
            logger.error(`Non-retryable error for request ${requestId}: ${err.message}`);
            break;
          }

          // If we've reached max retries, break
          if (retryCount === this.config.maxRetries) {
            logger.error(`Max retries reached for request ${requestId}`);
            break;
          }

          // Exponential backoff
          const backoffMs = Math.pow(2, retryCount) * 100;
          logger.debug(`Backing off for ${backoffMs}ms before retry`);
          await new Promise(resolve => setTimeout(resolve, backoffMs));
        }
      }

      // If we still have an error after retries, throw it
      if (error) {
        throw error;
      }

      // Process the response
      const processedResponse = adapter.processResponse(model, response);

      // Calculate processing time
      const processingTime = Date.now() - startTime;

      // Add metadata to response
      const result = {
        ...processedResponse,
        requestId,
        model: model.id,
        provider: model.provider,
        processingTime,
        retryCount,
        success: true,
        timestamp: new Date().toISOString()
      };

      logger.debug(`Request ${requestId} completed in ${processingTime}ms with ${retryCount} retries`);

      return result;

    } catch (error) {
      logger.error(`Error processing request ${requestId}: ${error.message}`);

      // Handle error through adapter if possible
      let errorResult;
      try {
        errorResult = adapter.handleError(model, error);
      } catch (handlerError) {
        logger.error(`Error in error handler: ${handlerError.message}`);
        errorResult = { error: error.message };
      }

      // Calculate processing time
      const processingTime = Date.now() - startTime;

      // Add metadata to error response
      const result = {
        ...errorResult,
        requestId,
        model: model.id,
        provider: model.provider,
        processingTime,
        retryCount,
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };

      return result;
    }
  }

  /**
   * Call a model through its adapter
   * @param {Object} adapter The provider adapter
   * @param {Object} model The model to use
   * @param {Object} request The prepared request
   * @returns {Promise<Object>} The raw response
   */
  async callModel(adapter, model, request) {
    // This is a placeholder for the actual implementation
    // In a real implementation, this would call the adapter's method to invoke the model

    if (!adapter.invokeModel) {
      throw new Error(`Adapter for ${model.provider} does not implement invokeModel`);
    }

    // Set up timeout
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Request timed out')), this.config.requestTimeout);
    });

    // Call the model with timeout
    return Promise.race([
      adapter.invokeModel(model, request),
      timeoutPromise
    ]);
  }

  /**
   * Determine if an error is retryable
   * @param {Error} error The error to check
   * @returns {Boolean} Whether the error is retryable
   */
  isRetryableError(error) {
    // Network errors are generally retryable
    if (error.code === 'ECONNRESET' ||
        error.code === 'ECONNREFUSED' ||
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout') ||
        error.message.includes('network') ||
        error.message.includes('connection')) {
      return true;
    }

    // Rate limiting errors are retryable
    if (error.status === 429 ||
        error.message.includes('rate limit') ||
        error.message.includes('too many requests')) {
      return true;
    }

    // Server errors are generally retryable
    if (error.status >= 500 && error.status < 600) {
      return true;
    }

    // Default to not retryable
    return false;
  }
}

module.exports = new ModelDispatcher();
