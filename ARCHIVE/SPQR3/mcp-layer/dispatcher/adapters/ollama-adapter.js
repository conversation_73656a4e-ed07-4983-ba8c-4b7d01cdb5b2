/**
 * Ollama Model Adapter
 * 
 * Adapter for locally running Ollama models (Llama, Mistral, etc.)
 */

const logger = require('../../utils/logger');

class OllamaAdapter {
  constructor() {
    this.available = true;
    this.baseUrl = process.env.OLLAMA_BASE_URL || 'http://localhost:11434';
    logger.info('Ollama adapter initialized');
  }

  /**
   * Prepare request for Ollama format
   * @param {Object} model Model definition
   * @param {Object} request Original request
   * @returns {Object} Prepared request for Ollama
   */
  prepareRequest(model, request) {
    logger.debug(`Preparing request for ${model.id}`);
    
    // Extract request parameters
    const { prompt, messages, systemPrompt, maxTokens, temperature } = request;
    
    // Create Ollama-formatted request
    let ollamaRequest;
    
    // Check if it's chat or completion
    if (messages || request.format === 'chat') {
      // Chat format
      ollamaRequest = {
        model: model.id,
        messages: messages || [
          ...(systemPrompt ? [{ role: 'system', content: systemPrompt }] : []),
          { role: 'user', content: prompt || "Hello" }
        ],
        options: {
          num_predict: maxTokens || 1024,
          temperature: temperature || 0.7,
        }
      };
    } else {
      // Completion format
      ollamaRequest = {
        model: model.id,
        prompt: prompt || "Hello",
        system: systemPrompt,
        options: {
          num_predict: maxTokens || 1024,
          temperature: temperature || 0.7,
        }
      };
    }
    
    // Add additional parameters if provided
    if (request.top_p) {
      ollamaRequest.options.top_p = request.top_p;
    }
    if (request.top_k) {
      ollamaRequest.options.top_k = request.top_k;
    }
    if (request.stop) {
      ollamaRequest.options.stop = Array.isArray(request.stop) ? request.stop : [request.stop];
    }
    
    return ollamaRequest;
  }
  
  /**
   * Process response from Ollama
   * @param {Object} model Model definition
   * @param {Object} response Raw Ollama response
   * @returns {Object} Processed response
   */
  processResponse(model, response) {
    logger.debug(`Processing response from ${model.id}`);
    
    // Default response format
    let processedResponse = {
      provider: 'ollama',
      model: model.id,
      result: null,
      rawResponse: response
    };
    
    // Process different response formats
    if (response.message && response.message.content) {
      // Chat completion response
      processedResponse.result = response.message.content;
    } else if (response.response) {
      // Regular completion response
      processedResponse.result = response.response;
    }
    
    // Add evaluation information if available
    if (response.eval_count || response.eval_duration) {
      processedResponse.metrics = {
        tokenCount: response.eval_count,
        generationTime: response.eval_duration,
        tokensPerSecond: response.eval_count > 0 && response.eval_duration > 0 
          ? response.eval_count / (response.eval_duration / **********) 
          : null
      };
    }
    
    return processedResponse;
  }
  
  /**
   * Handle error from Ollama
   * @param {Object} model Model definition
   * @param {Error} error Error object
   * @returns {Object} Error response
   */
  handleError(model, error) {
    logger.error(`Error with ${model.id}: ${error.message}`);
    
    // Format error response
    return {
      provider: 'ollama',
      model: model.id,
      error: error.message,
      errorCode: error.code || 'unknown',
      errorType: error.type || 'unknown'
    };
  }
  
  /**
   * Check if adapter is available
   * @returns {Boolean} Availability status
   */
  isAvailable() {
    return this.available;
  }
  
  /**
   * Invoke Ollama model (placeholder implementation)
   * @param {Object} model Model definition
   * @param {Object} request Prepared request
   * @returns {Promise<Object>} Ollama response
   */
  async invokeModel(model, request) {
    logger.info(`Invoking ${model.id} (simulated)`);
    
    // This is a placeholder implementation
    // In a real implementation, this would call the Ollama API
    
    // Simulate a realistic response with a delay
    await new Promise(resolve => setTimeout(resolve, 800));
    
    if (request.messages) {
      // Simulate chat completion response
      return {
        model: model.id,
        message: {
          role: 'assistant',
          content: `This is a simulated response from ${model.id}`
        },
        done: true,
        total_duration: 1200000000,
        load_duration: 400000000,
        eval_count: 150,
        eval_duration: 800000000
      };
    } else {
      // Simulate regular completion response
      return {
        model: model.id,
        response: `This is a simulated response from ${model.id}`,
        done: true,
        total_duration: **********,
        load_duration: 300000000,
        eval_count: 120,
        eval_duration: 700000000
      };
    }
  }
}

module.exports = new OllamaAdapter();
