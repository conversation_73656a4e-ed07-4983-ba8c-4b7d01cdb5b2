/**
 * OpenAI Model Adapter
 * 
 * Adapter for OpenAI models (GPT-3.5, GPT-4, etc.)
 */

const logger = require('../../utils/logger');

class OpenAIAdapter {
  constructor() {
    this.available = true;
    logger.info('OpenAI adapter initialized');
  }

  /**
   * Prepare request for OpenAI format
   * @param {Object} model Model definition
   * @param {Object} request Original request
   * @returns {Object} Prepared request for OpenAI
   */
  prepareRequest(model, request) {
    logger.debug(`Preparing request for ${model.id}`);
    
    // Extract request parameters
    const { prompt, messages, systemPrompt, maxTokens, temperature } = request;
    
    // Create OpenAI-formatted request
    const openaiRequest = {
      model: model.id,
      // For chat models
      messages: messages || (prompt ? [
        ...(systemPrompt ? [{ role: 'system', content: systemPrompt }] : []),
        { role: 'user', content: prompt }
      ] : []),
      // If messages is not provided and it's a completion model, use prompt
      ...((!messages && prompt && model.id.startsWith('text-')) && { prompt }),
      max_tokens: maxTokens || 1024,
      temperature: temperature || 0.7,
      // Add additional parameters from request if available
      ...(request.top_p && { top_p: request.top_p }),
      ...(request.frequency_penalty && { frequency_penalty: request.frequency_penalty }),
      ...(request.presence_penalty && { presence_penalty: request.presence_penalty }),
      ...(request.stop && { stop: request.stop })
    };
    
    return openaiRequest;
  }
  
  /**
   * Process response from OpenAI
   * @param {Object} model Model definition
   * @param {Object} response Raw OpenAI response
   * @returns {Object} Processed response
   */
  processResponse(model, response) {
    logger.debug(`Processing response from ${model.id}`);
    
    // Default response format
    let processedResponse = {
      provider: 'openai',
      model: model.id,
      result: null,
      rawResponse: response
    };
    
    // Process chat completion response
    if (response.choices && response.choices.length > 0) {
      if (response.choices[0].message) {
        // Chat completion
        processedResponse.result = response.choices[0].message.content;
      } else if (response.choices[0].text) {
        // Text completion
        processedResponse.result = response.choices[0].text;
      }
      
      // Add usage information if available
      if (response.usage) {
        processedResponse.usage = {
          promptTokens: response.usage.prompt_tokens,
          completionTokens: response.usage.completion_tokens,
          totalTokens: response.usage.total_tokens
        };
      }
    }
    
    return processedResponse;
  }
  
  /**
   * Handle error from OpenAI
   * @param {Object} model Model definition
   * @param {Error} error Error object
   * @returns {Object} Error response
   */
  handleError(model, error) {
    logger.error(`Error with ${model.id}: ${error.message}`);
    
    // Format error response
    return {
      provider: 'openai',
      model: model.id,
      error: error.message,
      errorCode: error.code || (error.response && error.response.status) || 'unknown',
      errorType: error.type || 'unknown'
    };
  }
  
  /**
   * Check if adapter is available
   * @returns {Boolean} Availability status
   */
  isAvailable() {
    return this.available;
  }
  
  /**
   * Invoke OpenAI model (placeholder implementation)
   * @param {Object} model Model definition
   * @param {Object} request Prepared request
   * @returns {Promise<Object>} OpenAI response
   */
  async invokeModel(model, request) {
    logger.info(`Invoking ${model.id} (simulated)`);
    
    // This is a placeholder implementation
    // In a real implementation, this would call the OpenAI API
    
    // Simulate a realistic response with a delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    if (request.messages) {
      // Simulate chat completion response
      return {
        id: 'chatcmpl-' + Math.random().toString(36).substring(2, 12),
        object: 'chat.completion',
        created: Math.floor(Date.now() / 1000),
        model: model.id,
        choices: [
          {
            message: {
              role: 'assistant',
              content: `This is a simulated response from ${model.id}`
            },
            finish_reason: 'stop',
            index: 0
          }
        ],
        usage: {
          prompt_tokens: 10,
          completion_tokens: 8,
          total_tokens: 18
        }
      };
    } else {
      // Simulate text completion response
      return {
        id: 'cmpl-' + Math.random().toString(36).substring(2, 12),
        object: 'text_completion',
        created: Math.floor(Date.now() / 1000),
        model: model.id,
        choices: [
          {
            text: `This is a simulated response from ${model.id}`,
            finish_reason: 'stop',
            index: 0
          }
        ],
        usage: {
          prompt_tokens: 5,
          completion_tokens: 8,
          total_tokens: 13
        }
      };
    }
  }
}

module.exports = new OpenAIAdapter();
