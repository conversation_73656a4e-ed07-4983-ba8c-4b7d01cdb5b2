/**
 * Anthropic Model Adapter
 * 
 * Adapter for Anthropic models (<PERSON>, <PERSON> 2, <PERSON> 3, etc.)
 */

const logger = require('../../utils/logger');

class AnthropicAdapter {
  constructor() {
    this.available = true;
    logger.info('Anthropic adapter initialized');
  }

  /**
   * Prepare request for Anthropic format
   * @param {Object} model Model definition
   * @param {Object} request Original request
   * @returns {Object} Prepared request for Anthropic
   */
  prepareRequest(model, request) {
    logger.debug(`Preparing request for ${model.id}`);
    
    // Extract request parameters
    const { prompt, messages, systemPrompt, maxTokens, temperature } = request;
    
    // Create Anthropic-formatted request
    let anthropicRequest;
    
    // Check if it's messages API (Claude 3) or classic API (Claude 1/2)
    if (model.id.includes('claude-3') && messages) {
      // Claude 3 Messages API format
      anthropicRequest = {
        model: model.id,
        messages: messages || [
          ...(systemPrompt ? [{ role: 'system', content: systemPrompt }] : []),
          { role: 'user', content: prompt || "Hello" }
        ],
        max_tokens: maxTokens || 1024,
        temperature: temperature || 0.7
      };
    } else {
      // Classic API format (Claude 1/2)
      anthropicRequest = {
        model: model.id,
        prompt: systemPrompt ? `${systemPrompt}\n\nHuman: ${prompt || "Hello"}\n\nAssistant:` : 
                              `Human: ${prompt || "Hello"}\n\nAssistant:`,
        max_tokens_to_sample: maxTokens || 1024,
        temperature: temperature || 0.7,
        stop_sequences: ["\n\nHuman:", "\n\nSystem:"]
      };
    }
    
    // Add additional parameters if available
    if (request.top_p) {
      anthropicRequest.top_p = request.top_p;
    }
    if (request.top_k) {
      anthropicRequest.top_k = request.top_k;
    }
    
    return anthropicRequest;
  }
  
  /**
   * Process response from Anthropic
   * @param {Object} model Model definition
   * @param {Object} response Raw Anthropic response
   * @returns {Object} Processed response
   */
  processResponse(model, response) {
    logger.debug(`Processing response from ${model.id}`);
    
    // Default response format
    let processedResponse = {
      provider: 'anthropic',
      model: model.id,
      result: null,
      rawResponse: response
    };
    
    // Process different response formats
    if (response.content && Array.isArray(response.content)) {
      // Claude 3 Messages API format
      const textContent = response.content
        .filter(item => item.type === 'text')
        .map(item => item.text)
        .join('\n');
        
      processedResponse.result = textContent;
    } else if (response.completion) {
      // Classic API format
      processedResponse.result = response.completion;
    }
    
    // Add usage information if available
    if (response.usage) {
      processedResponse.usage = {
        inputTokens: response.usage.input_tokens,
        outputTokens: response.usage.output_tokens,
        totalTokens: response.usage.input_tokens + response.usage.output_tokens
      };
    }
    
    return processedResponse;
  }
  
  /**
   * Handle error from Anthropic
   * @param {Object} model Model definition
   * @param {Error} error Error object
   * @returns {Object} Error response
   */
  handleError(model, error) {
    logger.error(`Error with ${model.id}: ${error.message}`);
    
    // Format error response
    return {
      provider: 'anthropic',
      model: model.id,
      error: error.message,
      errorCode: error.status || (error.response && error.response.status) || 'unknown',
      errorType: error.type || 'unknown'
    };
  }
  
  /**
   * Check if adapter is available
   * @returns {Boolean} Availability status
   */
  isAvailable() {
    return this.available;
  }
  
  /**
   * Invoke Anthropic model (placeholder implementation)
   * @param {Object} model Model definition
   * @param {Object} request Prepared request
   * @returns {Promise<Object>} Anthropic response
   */
  async invokeModel(model, request) {
    logger.info(`Invoking ${model.id} (simulated)`);
    
    // This is a placeholder implementation
    // In a real implementation, this would call the Anthropic API
    
    // Simulate a realistic response with a delay
    await new Promise(resolve => setTimeout(resolve, 600));
    
    if (model.id.includes('claude-3') && request.messages) {
      // Simulate Claude 3 Messages API response
      return {
        id: 'msg_' + Math.random().toString(36).substring(2, 12),
        type: 'message',
        role: 'assistant',
        model: model.id,
        content: [
          {
            type: 'text',
            text: `This is a simulated response from ${model.id}`
          }
        ],
        usage: {
          input_tokens: 15,
          output_tokens: 12,
        },
        stop_reason: 'end_turn'
      };
    } else {
      // Simulate classic API response
      return {
        completion: `This is a simulated response from ${model.id}`,
        stop_reason: 'stop_sequence',
        model: model.id,
        usage: {
          prompt_tokens: 12,
          completion_tokens: 10
        }
      };
    }
  }
}

module.exports = new AnthropicAdapter();
