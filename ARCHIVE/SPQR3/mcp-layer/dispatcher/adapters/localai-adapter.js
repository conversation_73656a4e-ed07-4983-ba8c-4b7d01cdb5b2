/**
 * LocalAI Model Adapter
 * 
 * Adapter for models running on LocalAI (compatible with OpenAI API)
 */

const logger = require('../../utils/logger');

class LocalAIAdapter {
  constructor() {
    this.available = true;
    this.baseUrl = process.env.LOCALAI_BASE_URL || 'http://localhost:8080';
    logger.info('LocalAI adapter initialized');
  }

  /**
   * Prepare request for LocalAI format (mostly compatible with OpenAI)
   * @param {Object} model Model definition
   * @param {Object} request Original request
   * @returns {Object} Prepared request for LocalAI
   */
  prepareRequest(model, request) {
    logger.debug(`Preparing request for ${model.id}`);
    
    // Extract request parameters
    const { prompt, messages, systemPrompt, maxTokens, temperature } = request;
    
    // Create LocalAI-formatted request (similar to OpenAI format)
    const localaiRequest = {
      model: model.id,
      // For chat models
      messages: messages || (prompt ? [
        ...(systemPrompt ? [{ role: 'system', content: systemPrompt }] : []),
        { role: 'user', content: prompt }
      ] : []),
      // If messages is not provided and it's a completion model, use prompt
      ...((!messages && prompt && model.id.includes('completion')) && { prompt }),
      max_tokens: maxTokens || 1024,
      temperature: temperature || 0.7,
      // Add additional parameters from request if available
      ...(request.top_p && { top_p: request.top_p }),
      ...(request.frequency_penalty && { frequency_penalty: request.frequency_penalty }),
      ...(request.presence_penalty && { presence_penalty: request.presence_penalty }),
      ...(request.stop && { stop: request.stop })
    };
    
    return localaiRequest;
  }
  
  /**
   * Process response from LocalAI
   * @param {Object} model Model definition
   * @param {Object} response Raw LocalAI response
   * @returns {Object} Processed response
   */
  processResponse(model, response) {
    logger.debug(`Processing response from ${model.id}`);
    
    // Default response format
    let processedResponse = {
      provider: 'localai',
      model: model.id,
      result: null,
      rawResponse: response
    };
    
    // Process response format (similar to OpenAI)
    if (response.choices && response.choices.length > 0) {
      if (response.choices[0].message) {
        // Chat completion
        processedResponse.result = response.choices[0].message.content;
      } else if (response.choices[0].text) {
        // Text completion
        processedResponse.result = response.choices[0].text;
      }
      
      // Add usage information if available
      if (response.usage) {
        processedResponse.usage = {
          promptTokens: response.usage.prompt_tokens,
          completionTokens: response.usage.completion_tokens,
          totalTokens: response.usage.total_tokens
        };
      }
    }
    
    return processedResponse;
  }
  
  /**
   * Handle error from LocalAI
   * @param {Object} model Model definition
   * @param {Error} error Error object
   * @returns {Object} Error response
   */
  handleError(model, error) {
    logger.error(`Error with ${model.id}: ${error.message}`);
    
    // Format error response
    return {
      provider: 'localai',
      model: model.id,
      error: error.message,
      errorCode: error.code || (error.response && error.response.status) || 'unknown',
      errorType: error.type || 'unknown'
    };
  }
  
  /**
   * Check if adapter is available
   * @returns {Boolean} Availability status
   */
  isAvailable() {
    return this.available;
  }
  
  /**
   * Invoke LocalAI model (placeholder implementation)
   * @param {Object} model Model definition
   * @param {Object} request Prepared request
   * @returns {Promise<Object>} LocalAI response
   */
  async invokeModel(model, request) {
    logger.info(`Invoking ${model.id} (simulated)`);
    
    // This is a placeholder implementation
    // In a real implementation, this would call the LocalAI API
    
    // Simulate a realistic response with a delay
    await new Promise(resolve => setTimeout(resolve, 700));
    
    if (request.messages) {
      // Simulate chat completion response
      return {
        id: 'chatcmpl-local-' + Math.random().toString(36).substring(2, 12),
        object: 'chat.completion',
        created: Math.floor(Date.now() / 1000),
        model: model.id,
        choices: [
          {
            message: {
              role: 'assistant',
              content: `This is a simulated response from ${model.id} running on LocalAI`
            },
            finish_reason: 'stop',
            index: 0
          }
        ],
        usage: {
          prompt_tokens: 10,
          completion_tokens: 12,
          total_tokens: 22
        }
      };
    } else {
      // Simulate text completion response
      return {
        id: 'cmpl-local-' + Math.random().toString(36).substring(2, 12),
        object: 'text_completion',
        created: Math.floor(Date.now() / 1000),
        model: model.id,
        choices: [
          {
            text: `This is a simulated response from ${model.id} running on LocalAI`,
            finish_reason: 'stop',
            index: 0
          }
        ],
        usage: {
          prompt_tokens: 5,
          completion_tokens: 12,
          total_tokens: 17
        }
      };
    }
  }
}

module.exports = new LocalAIAdapter();
