{"name": "spqr-mcp-layer", "version": "0.9.2", "description": "Model Context Protocol layer for SPQR AI CRM", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest"}, "dependencies": {"axios": "^1.6.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "ioredis": "^5.3.2", "joi": "^17.11.0", "morgan": "^1.10.0", "natural": "^6.8.0", "pg": "^8.11.3", "pino": "^8.16.2", "pino-pretty": "^10.2.3", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}