/**
 * MCP Layer Main Entry Point
 *
 * This file serves as the main entry point for the MCP (Model Context Protocol) layer
 * which manages AI models, optimizes resource usage, and coordinates model requests.
 */

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const logger = require('./utils/logger');
const { v4: uuidv4 } = require('uuid');

// Import MCP components
const modelRegistry = require('./registry');
const modelDispatcher = require('./dispatcher');
const resourceOptimizer = require('./optimizer');
const mcpCoordinator = require('./coordinator');

// Create Express app
const app = express();
const PORT = process.env.PORT || 3210;

// Request ID middleware
app.use((req, res, next) => {
  req.id = req.headers['x-request-id'] || uuidv4();
  res.setHeader('X-Request-ID', req.id);
  next();
});

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'mcp-layer',
    version: '1.0.0'
  });
});

// API routes
app.use('/api/models', require('./routes/models'));
app.use('/api/invoke', require('./routes/invoke'));
app.use('/api/resources', require('./routes/resources'));
app.use('/api/status', require('./routes/status'));
app.use('/api/registry', require('./routes/registry'));
app.use('/api/optimizer', require('./routes/optimizer'));

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error(`Error processing request ${req.id}: ${err.message}`);

  res.status(err.status || 500).json({
    error: {
      message: err.message,
      requestId: req.id
    }
  });
});

// Start server
const server = app.listen(PORT, () => {
  logger.info(`MCP Layer service running on port ${PORT}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM signal received: closing HTTP server');

  server.close(async () => {
    logger.info('HTTP server closed');

    // Shutdown MCP components
    await mcpCoordinator.shutdown();
    await resourceOptimizer.shutdown();
    await modelRegistry.shutdown();

    process.exit(0);
  });
});

module.exports = app;
