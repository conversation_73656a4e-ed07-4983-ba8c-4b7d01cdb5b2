#!/bin/bash

# SPQR3 Start Services Script v0.9.5
# This script starts all SPQR3 services and performs necessary checks

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if a command exists
command_exists() {
  command -v "$1" >/dev/null 2>&1
}

# Function to check if a directory exists, create if not
check_directory() {
  local dir=$1
  if [ ! -d "$dir" ]; then
    echo -e "${YELLOW}Creating directory $dir...${NC}"
    mkdir -p "$dir"
  fi
}

# Function to check if .env file exists
check_env_file() {
  if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
      echo -e "${YELLOW}Creating .env file from .env.example...${NC}"
      cp .env.example .env
      echo -e "${GREEN}✓ Created .env file${NC}"
      echo -e "${YELLOW}! Please edit the .env file and set your API keys and passwords${NC}"
    else
      echo -e "${RED}✗ .env.example file not found${NC}"
      return 1
    fi
  fi
  return 0
}

# Main function
main() {
  echo -e "${BLUE}========================================${NC}"
  echo -e "${BLUE}   SPQR3 v0.9.5 Service Starter         ${NC}"
  echo -e "${BLUE}========================================${NC}"
  echo ""

  # Check if Docker is running
  if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}Docker is not running. Please start Docker and try again.${NC}"
    exit 1
  fi

  # Check required directories
  echo -e "${YELLOW}Checking required directories...${NC}"
  check_directory "data/postgres"
  check_directory "data/redis"
  check_directory "data/vector-store"
  check_directory "data/ollama"
  check_directory "logs"

  # Check .env file
  echo -e "${YELLOW}Checking .env file...${NC}"
  if ! check_env_file; then
    echo -e "${RED}Failed to set up .env file. Please create it manually.${NC}"
    exit 1
  fi

  # Run fix script if available
  if [ -f "tools/fix-common-issues.sh" ]; then
    echo -e "${YELLOW}Running fix script to address common issues...${NC}"
    ./tools/fix-common-issues.sh
  fi

  # Start services with Docker Compose
  echo -e "${YELLOW}Starting services with Docker Compose...${NC}"
  if command_exists docker-compose; then
    docker-compose up -d
  else
    docker compose up -d
  fi

  # Wait for services to start
  echo -e "${YELLOW}Waiting for services to start...${NC}"
  sleep 5

  # Initialize database if script exists
  if [ -f "tools/initialize-database.sh" ]; then
    echo -e "${YELLOW}Initializing database...${NC}"
    ./tools/initialize-database.sh
  fi

  # Check container health if script exists
  if [ -f "tools/check-container-health.sh" ]; then
    echo -e "${YELLOW}Checking container health...${NC}"
    ./tools/check-container-health.sh
  fi

  echo -e "${GREEN}Services started.${NC}"
  echo -e "${YELLOW}You can now access the application at the following URLs:${NC}"
  echo -e "- Frontend: http://localhost"
  echo -e "- Backend API: http://localhost:3200"
  echo -e "- MCP Coordinator: http://localhost:3210"
  echo -e "- Task Manager: http://localhost:3220"
  echo -e "- Browser Automation: http://localhost:3230"
}

# Führe die Hauptfunktion aus
main
