# SPQR3 Comprehensive Installation Guide

This guide provides detailed instructions for installing and configuring SPQR3 on an Ubuntu server, including troubleshooting common issues.

## Table of Contents

1. [System Requirements](#system-requirements)
2. [Prerequisites Installation](#prerequisites-installation)
3. [SPQR3 Installation](#spqr3-installation)
4. [Configuration](#configuration)
5. [Starting the System](#starting-the-system)
6. [Verifying Installation](#verifying-installation)
7. [Troubleshooting](#troubleshooting)
8. [Maintenance](#maintenance)
9. [Updating](#updating)

## System Requirements

Ensure your server meets these minimum requirements:

- **Operating System**: Ubuntu 20.04 LTS or higher
- **CPU**: 2+ cores recommended
- **RAM**: 4+ GB recommended
- **Disk Space**: 20+ GB free space
- **Network**: Stable internet connection
- **Ports**: 80, 3200-3230, 5432, 6333, 6379, 11434 available

## Prerequisites Installation

### 1. Update System Packages

```bash
sudo apt update
sudo apt upgrade -y
```

### 2. Install Docker

```bash
# Install required packages
sudo apt install -y apt-transport-https ca-certificates curl software-properties-common

# Add Docker's official GPG key
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -

# Add Docker repository
sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"

# Update package database
sudo apt update

# Install Docker
sudo apt install -y docker-ce

# Add your user to the docker group (to run Docker without sudo)
sudo usermod -aG docker ${USER}

# Apply group changes (or log out and back in)
newgrp docker
```

### 3. Install Docker Compose

```bash
# Download Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.18.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# Make it executable
sudo chmod +x /usr/local/bin/docker-compose

# Verify installation
docker-compose --version
```

### 4. Install Git

```bash
sudo apt install -y git
```

### 5. Install Additional Tools

```bash
sudo apt install -y curl wget netcat net-tools
```

## SPQR3 Installation

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/spqr3.git
cd spqr3
```

### 2. Run Diagnostic Tool

Before proceeding, run the diagnostic tool to check if your system meets all requirements:

```bash
./tools/diagnose-installation.sh
```

Review the output and address any issues before continuing.

### 3. Fix Common Issues

Run the fix script to address common issues:

```bash
./tools/fix-common-issues.sh
```

## Configuration

### 1. Set Up Environment Variables

```bash
# Copy example environment file
cp .env.example .env

# Edit the environment file
nano .env
```

Configure at least these essential variables:

- `POSTGRES_PASSWORD`: A secure password for the PostgreSQL database
- `JWT_SECRET`: A secure random string for JWT authentication
- `CLAUDE_API_KEY`: Your Claude API key (if using Claude)
- `OPENAI_API_KEY`: Your OpenAI API key (if using OpenAI)

### 2. Create Data Directories

```bash
mkdir -p data/postgres
mkdir -p data/redis
mkdir -p data/vector-store
mkdir -p data/ollama
```

### 3. Build the Frontend

```bash
./build-frontend.sh
```

## Starting the System

### 1. Start All Services

```bash
docker compose up -d
```

This command starts all services in detached mode.

### 2. Initialize the Database

The database schema should be automatically initialized when the PostgreSQL container starts. If you need to manually initialize it:

```bash
# Wait for PostgreSQL to be ready
sleep 10

# Create pgvector extension
docker exec spqr-postgres psql -U postgres -c "CREATE EXTENSION IF NOT EXISTS vector;"

# Import schema
cat sql/schema.sql | docker exec -i spqr-postgres psql -U postgres -d spqr2

# Import test data (optional)
cat sql/test-data.sql | docker exec -i spqr-postgres psql -U postgres -d spqr2
```

## Verifying Installation

### 1. Check Container Status

```bash
docker compose ps
```

All containers should show status "Up" and health status "healthy" (if health checks are configured).

### 2. Check Service Health

```bash
# Check backend API health
curl http://localhost:3200/health

# Check MCP coordinator health
curl http://localhost:3210/health

# Check task manager health
curl http://localhost:3220/health

# Check browser automation health
curl http://localhost:3230/health
```

### 3. Access the Web Interface

Open a web browser and navigate to:

```
http://your-server-ip
```

Replace `your-server-ip` with your server's IP address.

## Troubleshooting

### Common Issues and Solutions

#### 1. Docker Containers Not Starting

**Symptoms**: Containers show status "Exited" or "Created" but not "Up"

**Solutions**:

- Check container logs:
  ```bash
  docker logs spqr-core
  ```

- Check for port conflicts:
  ```bash
  netstat -tuln | grep 3200
  ```

- Check for disk space issues:
  ```bash
  df -h
  ```

#### 2. Missing Module Errors

**Symptoms**: Logs show "Cannot find module" errors

**Solution**:

Run the fix script:
```bash
./tools/fix-common-issues.sh
```

#### 3. Database Connection Issues

**Symptoms**: Services can't connect to the database

**Solutions**:

- Check if PostgreSQL container is running:
  ```bash
  docker ps | grep postgres
  ```

- Check PostgreSQL logs:
  ```bash
  docker logs spqr-postgres
  ```

- Verify database credentials in `.env` file match what's in `docker-compose.yml`

- Try connecting to the database manually:
  ```bash
  docker exec -it spqr-postgres psql -U postgres
  ```

#### 4. Frontend Not Loading

**Symptoms**: Web interface shows blank page or doesn't load

**Solutions**:

- Check frontend container logs:
  ```bash
  docker logs spqr-frontend
  ```

- Rebuild the frontend:
  ```bash
  ./build-frontend.sh
  ```

- Check if the frontend container is running:
  ```bash
  docker ps | grep frontend
  ```

#### 5. Network Issues Between Containers

**Symptoms**: Services can't communicate with each other

**Solutions**:

- Check if all containers are on the same network:
  ```bash
  docker network inspect spqr-network
  ```

- Try restarting the Docker daemon:
  ```bash
  sudo systemctl restart docker
  ```

#### 6. Permission Issues

**Symptoms**: "Permission denied" errors in logs

**Solutions**:

- Check ownership of data directories:
  ```bash
  ls -la data/
  ```

- Fix permissions:
  ```bash
  sudo chown -R 1000:1000 data/
  ```

### Advanced Troubleshooting

#### 1. Restart Individual Services

```bash
docker compose restart spqr-core
```

#### 2. Rebuild Services

```bash
docker compose build spqr-core
docker compose up -d spqr-core
```

#### 3. Reset the System

```bash
# Stop all containers
docker compose down

# Remove volumes (WARNING: This will delete all data)
docker compose down -v

# Start fresh
docker compose up -d
```

## Maintenance

### 1. Backing Up Data

```bash
# Create backup directory
mkdir -p backups

# Backup PostgreSQL data
docker exec spqr-postgres pg_dump -U postgres spqr2 > backups/spqr2_db_$(date +%Y%m%d).sql

# Backup vector store data
tar -czf backups/vector_store_$(date +%Y%m%d).tar.gz data/vector-store/
```

### 2. Viewing Logs

```bash
# View logs for all services
docker compose logs

# View logs for a specific service
docker compose logs spqr-core

# Follow logs in real-time
docker compose logs -f spqr-core
```

### 3. Stopping the System

```bash
# Stop all services
docker compose down

# Stop all services and remove volumes (WARNING: This will delete all data)
docker compose down -v
```

## Updating

### 1. Update the Repository

```bash
git pull
```

### 2. Rebuild Services

```bash
# Rebuild the frontend
./build-frontend.sh

# Rebuild and restart all services
docker compose down
docker compose build
docker compose up -d
```

### 3. Check for Database Migrations

Check if there are any new SQL files in the `sql` directory that need to be applied:

```bash
# Apply new migrations
cat sql/migrations/new_migration.sql | docker exec -i spqr-postgres psql -U postgres -d spqr2
```
