/**
 * SPQR Main Application
 * 
 * This is the main entry point for the SPQR application.
 * It integrates all components including the Research System, Browser Automation,
 * and the new Workflow Automation functionality.
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const path = require('path');
const { db } = require('./shared/database');
const logger = require('./shared/utils/logger');
const errorHandler = require('./shared/middleware/errorHandler');
const requestLogger = require('./shared/middleware/requestLogger');
const authMiddleware = require('./shared/middleware/authMiddleware');
const { createWorkflowRoutes } = require('./browser-automation/routes/workflowRoutes');
const { WorkflowController } = require('./browser-automation/controllers/WorkflowController');

// Import routes from different components
const researchRoutes = require('./research-system/api/routes');
const browserAutomationRoutes = require('./browser-automation/routes');
const webhookRoutes = require('./shared/webhooks');

// Import routes
const userRoutes = require('./routes/users');
const eventRoutes = require('./routes/events');
const crmRoutes = require('./routes/crm');
const ragRoutes = require('./routes/rag');
const notificationRoutes = require('./routes/notifications');
const aiRoutes = require('./routes/ai');
const telephonyRoutes = require('./routes/telephony');
const agentRoutes = require('./routes/agents');
const connectionRoutes = require('./routes/connections');
const metricsRoutes = require('./routes/metrics');
const auditRoutes = require('./routes/audit'); // New audit routes
const browserRoutes = require('./routes/browserRoutes'); // Browser automation routes
const pushRoutes = require('./routes/push'); // Push notification routes
const systemStatusRoutes = require('./routes/systemStatus'); // System status routes

// Create Express app
const app = express();

// Middleware
app.use(helmet()); // Security headers
app.use(cors()); // CORS support
app.use(compression()); // Compress responses
app.use(express.json({ limit: '50mb' })); // Parse JSON bodies
app.use(express.urlencoded({ extended: true, limit: '50mb' })); // Parse URL-encoded bodies
app.use(requestLogger); // Log requests

// Initialize database
db.initialize()
  .then(() => {
    logger.info('Database initialized successfully');
  })
  .catch((err) => {
    logger.error('Failed to initialize database', { error: err.message });
    process.exit(1);
  });

// Static files
app.use(express.static(path.join(__dirname, 'public')));

// API routes
app.use('/api/research', researchRoutes);
app.use('/api/browser-automation', browserAutomationRoutes);
// Workflow routes
const workflowController = new WorkflowController({ db });
app.use('/api/workflows', createWorkflowRoutes({ workflowController }));

// Import system status routes
const systemStatusRoutes = require('./routes/systemStatus'); // System status routes

// Apply bias detection middleware globally to AI responses
// app.use('/api/ai', biasDetectionService.createBiasDetectionMiddleware({
//   analyzeOnly: false,
//   applyMitigation: true,
//   logResults: true
// }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

// System status endpoint (public, no auth required)
app.use('/api/system-status', systemStatusRoutes); // Mount the system status routes

// Installation wizard endpoint
app.get('/installation-wizard', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>SPQR Installation Wizard</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        h1 {
          color: #2c3e50;
          border-bottom: 2px solid #3498db;
          padding-bottom: 10px;
        }
        .container {
          background-color: #f9f9f9;
          border: 1px solid #ddd;
          border-radius: 5px;
          padding: 20px;
          margin-bottom: 20px;
        }
        button {
          background-color: #3498db;
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 5px;
          cursor: pointer;
          font-size: 16px;
          margin-top: 10px;
        }
        button:hover {
          background-color: #2980b9;
        }
      </style>
    </head>
    <body>
      <h1>SPQR Installation Wizard</h1>

      <div class="container">
        <h2>Welcome to SPQR</h2>
        <p>This is a simplified installation wizard for the SPQR system.</p>
        <p>In a real implementation, this would guide you through the process of setting up your SPQR system.</p>
        <p>For now, you can consider the system installed and ready to use.</p>

        <h3>System Information</h3>
        <ul>
          <li>Core API: http://localhost:3200</li>
          <li>Admin Dashboard: http://localhost:3000</li>
        </ul>

        <button onclick="window.location.href='/health'">Check System Health</button>
      </div>
    </body>
    </html>
  `);
});

// PWA Manifest shortcut
app.get('/manifest.json', (req, res) => {
  res.sendFile(path.join(__dirname, '../../frontend/public/manifest.json'));
});

// Service worker special routing
app.get('/serviceWorker.js', (req, res) => {
  res.sendFile(path.join(__dirname, '../../frontend/src/serviceWorker.js'));
});

// Apply error handler middleware
app.use(errorHandler);

// Start server
const PORT = process.env.PORT || 3000;
const server = app.listen(PORT, () => {
  logger.info(`SPQR server listening on port ${PORT}`);
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM signal received: closing HTTP server');
  server.close(() => {
    logger.info('HTTP server closed');
    db.close()
      .then(() => {
        logger.info('Database connections closed');
        process.exit(0);
      })
      .catch((err) => {
        logger.error('Error closing database connections', { error: err.message });
        process.exit(1);
      });
  });
});

module.exports = { app, db };
