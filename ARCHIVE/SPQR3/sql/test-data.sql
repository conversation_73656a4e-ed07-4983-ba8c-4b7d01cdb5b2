-- SPQR2 Test Data (v0.9.4)

-- Insert admin user (password: admin123)
INSERT INTO users (username, email, password_hash, first_name, last_name, role)
VALUES ('admin', '<EMAIL>', '$2b$10$X7VYVy1Z5Z5Z5Z5Z5Z5Z5OX7VYVy1Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z', 'Admin', 'User', 'admin');

-- Insert test user (password: user123)
INSERT INTO users (username, email, password_hash, first_name, last_name, role)
VALUES ('user', '<EMAIL>', '$2b$10$X7VYVy1Z5Z5Z5Z5Z5Z5Z5OX7VYVy1Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z', 'Test', 'User', 'user');

-- Insert test tools
INSERT INTO tools (name, description, type, config, created_by)
VALUES 
  ('Web Search', 'Search the web for information', 'web_search', '{"engine": "google", "max_results": 5}', 1),
  ('Email Sender', 'Send emails to recipients', 'email', '{"smtp_server": "smtp.example.com", "port": 587}', 1),
  ('PDF Extractor', 'Extract text from PDF documents', 'document', '{"ocr_enabled": true}', 1),
  ('Weather API', 'Get weather information', 'api', '{"api_key": "demo_key", "units": "metric"}', 2);

-- Insert test applications
INSERT INTO applications (name, description, script, config, created_by)
VALUES 
  ('Customer Onboarding', 'Automate customer onboarding process', 'async function run(context) {\n  // Application logic here\n  return { success: true };\n}', '{"timeout": 300, "max_retries": 3}', 1),
  ('Data Analyzer', 'Analyze customer data for insights', 'async function run(context) {\n  // Analysis logic here\n  return { insights: [] };\n}', '{"data_source": "postgres", "batch_size": 100}', 1),
  ('Report Generator', 'Generate weekly reports', 'async function run(context) {\n  // Report generation logic\n  return { report_url: "" };\n}', '{"format": "pdf", "schedule": "weekly"}', 2);

-- Insert test tasks
INSERT INTO tasks (name, description, type, status, config, schedule, created_by)
VALUES 
  ('Daily Backup', 'Backup database daily', 'backup', 'active', '{"target": "database", "retention_days": 7}', '0 0 * * *', 1),
  ('Weekly Report', 'Generate weekly reports', 'report', 'active', '{"format": "pdf", "recipients": ["<EMAIL>"]}', '0 9 * * 1', 1),
  ('Data Sync', 'Synchronize data with external system', 'sync', 'paused', '{"source": "api", "destination": "database"}', '*/30 * * * *', 2);

-- Insert test task executions
INSERT INTO task_executions (task_id, status, result, error, started_at, completed_at)
VALUES 
  (1, 'completed', '{"files_backed_up": 150, "total_size_mb": 1024}', NULL, NOW() - INTERVAL '1 day', NOW() - INTERVAL '23 hours'),
  (1, 'completed', '{"files_backed_up": 152, "total_size_mb": 1056}', NULL, NOW() - INTERVAL '2 days', NOW() - INTERVAL '1 day 23 hours'),
  (2, 'failed', NULL, 'Connection timeout', NOW() - INTERVAL '7 days', NOW() - INTERVAL '7 days'),
  (3, 'completed', '{"records_synced": 1250}', NULL, NOW() - INTERVAL '12 hours', NOW() - INTERVAL '11 hours 45 minutes');

-- Insert test browser sessions
INSERT INTO browser_sessions (user_id, status, browser_id, config)
VALUES 
  (1, 'active', 'browser-1', '{"headless": false, "viewport": {"width": 1920, "height": 1080}}'),
  (2, 'closed', 'browser-2', '{"headless": true, "viewport": {"width": 1366, "height": 768}}');

-- Insert test API keys
INSERT INTO api_keys (name, key_type, api_key, is_active, created_by)
VALUES 
  ('OpenAI API Key', 'openai', 'sk-demo-key-openai', TRUE, 1),
  ('Google API Key', 'google', 'AIza-demo-key-google', TRUE, 1),
  ('Weather API Key', 'weather', 'demo-key-weather', FALSE, 2);

-- Insert test LLM providers
INSERT INTO llm_providers (name, provider_type, config, is_active)
VALUES 
  ('OpenAI', 'openai', '{"api_base": "https://api.openai.com/v1", "organization_id": ""}', TRUE),
  ('Anthropic', 'anthropic', '{"api_base": "https://api.anthropic.com"}', TRUE),
  ('Local Ollama', 'ollama', '{"api_base": "http://localhost:11434"}', TRUE);

-- Insert test LLM models
INSERT INTO llm_models (provider_id, name, model_id, capabilities, config, is_active)
VALUES 
  (1, 'GPT-4', 'gpt-4', '{"chat": true, "embeddings": false, "max_tokens": 8192}', '{"temperature": 0.7}', TRUE),
  (1, 'GPT-3.5 Turbo', 'gpt-3.5-turbo', '{"chat": true, "embeddings": false, "max_tokens": 4096}', '{"temperature": 0.7}', TRUE),
  (2, 'Claude 3 Opus', 'claude-3-opus-20240229', '{"chat": true, "embeddings": false, "max_tokens": 200000}', '{"temperature": 0.7}', TRUE),
  (3, 'Llama 3', 'llama3', '{"chat": true, "embeddings": true, "max_tokens": 4096}', '{"temperature": 0.7}', TRUE);

-- Insert test vector collections
INSERT INTO vector_collections (name, description, config, created_by)
VALUES 
  ('Documentation', 'Product documentation', '{"dimension": 1536, "metric": "cosine"}', 1),
  ('Knowledge Base', 'Internal knowledge base', '{"dimension": 1536, "metric": "cosine"}', 1);

-- Insert test vector documents (without embeddings for simplicity)
INSERT INTO vector_documents (collection_id, document_id, content, metadata)
VALUES 
  (1, 'doc-1', 'SPQR2 is an advanced AI-powered platform for intelligent customer relationship management.', '{"title": "Introduction", "section": "overview"}'),
  (1, 'doc-2', 'The system includes a Natural Language Process Builder for creating automation workflows.', '{"title": "Features", "section": "automation"}'),
  (2, 'kb-1', 'To configure the API connection, navigate to Settings > Integrations > API Keys.', '{"category": "setup", "tags": ["api", "configuration"]}'),
  (2, 'kb-2', 'Common error: If you see "Connection refused", check that the service is running.', '{"category": "troubleshooting", "tags": ["errors", "connection"]}');
