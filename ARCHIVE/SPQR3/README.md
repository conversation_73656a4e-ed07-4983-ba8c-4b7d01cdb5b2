# SPQR3: Smart Process Query & Response (v0.9.5)

SPQR3 is an AI-powered system for intelligent Customer Relationship Management and automated communication. It combines advanced AI technologies with a user-friendly interface to help businesses improve their customer relationships and automate their communication processes.

## Features

- **AI-powered Communication**: Automated creation and personalization of customer communications.
- **Intelligent Task Management**: Automatic categorization, prioritization, and assignment of tasks.
- **Knowledge Management**: Centralized storage and retrieval of customer-relevant information.
- **Integrations**: Seamless integration with existing systems and tools.
- **Analytics and Reporting**: Insights into customer interactions and behavior.

## Architecture

SPQR3 uses a combination of microservices architecture for the backend and a modern Single-Page Application (SPA) for the frontend. This architecture enables scalability, maintainability, and a clear separation of responsibilities.

```mermaid
graph TD
    User --> Frontend[SPQR3 Frontend]
    Frontend --> BackendAPI[SPQR3 Backend API]
    BackendAPI --> SPQRSystem[SPQR3 Core System]
    SPQRSystem --> LLMModule[LLM Module]
    SPQRSystem --> ToolRegistry[Tool Registry]
    SPQRSystem --> AgentSystem[Agent System]
    SPQRSystem --> ApplicationRegistry[Application Registry]
    SPQRSystem --> Database[PostgreSQL]
    LLMModule --> LLMProviders[External/Local LLMs]
    ToolRegistry --> ExternalServices[External Services/APIs]
    BackendAPI --> TaskManager[Task Manager Service]
    BackendAPI --> BrowserAutomation[Browser Automation Service]
    BackendAPI --> MCPCoordinator[MCP Coordinator Service]
    Database --> VectorStore[Vector Store (Qdrant)]
```

## Technology Stack

- **Backend**: Node.js, Express.js, PostgreSQL, Redis, Docker
- **Frontend**: React, TypeScript, Shadcn UI, TanStack Query, Tailwind CSS, Vite
- **AI**: LLM Integration (Claude, OpenAI, local LLMs), Qdrant for vector storage
- **Deployment**: Docker Compose, Nginx

## Prerequisites

- Node.js (v18 or higher)
- Docker and Docker Compose
- PostgreSQL (optional, can also be provided via Docker)
- Redis (optional, can also be provided via Docker)
- API keys for LLM providers (Claude, OpenAI, etc.)

## Installation

For a quick installation:

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/spqr3.git
   cd spqr3
   ```

2. Run the start script (which handles configuration, building, and starting services):
   ```bash
   ./start-services.sh
   ```

3. Access the application:
   ```text
   http://localhost
   ```

For a detailed installation guide with troubleshooting information, see:
- [INSTALLATION_GUIDE.md](INSTALLATION_GUIDE.md) - Comprehensive installation instructions
- [TROUBLESHOOTING.md](TROUBLESHOOTING.md) - Detailed troubleshooting guide

### Manual Installation Steps

If you prefer to install manually:

1. Configure environment variables:
   ```bash
   cp .env.example .env
   # Edit the .env file and add your API keys and configurations
   ```

2. Create required directories:
   ```bash
   mkdir -p data/postgres data/redis data/vector-store data/ollama logs
   ```

3. Build the frontend:
   ```bash
   ./build-frontend.sh
   ```

4. Start with Docker Compose:
   ```bash
   docker compose up -d
   ```

5. Initialize the database:
   ```bash
   ./tools/initialize-database.sh
   ```

6. Check container health:
   ```bash
   ./tools/check-container-health.sh
   ```

## Development

### Backend Development

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the development server:
   ```bash
   npm run dev
   ```

### Frontend Development

1. Change to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

## Documentation

Comprehensive documentation can be found in the `cline_docs/` directory:

- `projectRoadmap.md`: Overview of project goals and features
- `techStack.md`: Detailed information about the technology stack
- `codebaseSummary.md`: Summary of the codebase and its components
- `currentTask.md`: Current tasks and next steps
- `progress.md`: Progress and status of the project
- `systemPatterns.md`: Architecture and design patterns
- `productContext.md`: Product context and value proposition
- `activeContext.md`: Active context and decisions

## License

This project is licensed under the MIT License. See the `LICENSE` file for details.

## Contributors

- Your Name - [GitHub Profile](https://github.com/yourusername)

## Contact

For questions or suggestions, please contact [your email address](mailto:<EMAIL>).
