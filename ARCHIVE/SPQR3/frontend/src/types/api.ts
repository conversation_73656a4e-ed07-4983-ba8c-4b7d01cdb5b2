
export interface ApiConnection {
  id: string;
  name: string;
  apiKey: string;
  type: string;
  endpoint: string;
  status: 'connected' | 'disconnected' | 'error';
  lastSync: string;
  metadata?: Record<string, any>;
}

export interface ApiEvent {
  id: string;
  connectionId: string;
  type: string;
  content: string;
  timestamp: string;
  processed: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

export interface AutomatedTask {
  id: string;
  name: string;
  schedule: string;
  lastRun?: string;
  nextRun?: string;
  status: 'idle' | 'running' | 'completed' | 'failed';
  connectionIds: string[];
}
