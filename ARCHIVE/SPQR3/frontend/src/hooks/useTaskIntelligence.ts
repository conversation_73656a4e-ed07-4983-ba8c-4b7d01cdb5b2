
import { useState, useEffect } from 'react';
import { taskIntelligence } from '@/services/TaskIntelligenceService';
import { AutomatedTask } from '@/types/api';

export function useTaskIntelligence(tasks: AutomatedTask[]) {
  const [taskInsights, setTaskInsights] = useState<Record<string, {
    estimatedDuration: number;
    successProbability: number;
    recommendedSchedule?: string;
  }>>({});

  useEffect(() => {
    // Generate insights for all tasks
    const insights: Record<string, any> = {};
    
    tasks.forEach(task => {
      insights[task.id] = {
        estimatedDuration: taskIntelligence.predictTaskDuration(task.id),
        successProbability: taskIntelligence.getTaskSuccessProbability(task.id),
      };
    });
    
    // Get schedule recommendations
    const scheduleRecommendations = taskIntelligence.recommendOptimalSchedule();
    
    // Merge recommendations with insights
    Object.entries(scheduleRecommendations).forEach(([taskId, schedule]) => {
      if (insights[taskId]) {
        insights[taskId].recommendedSchedule = schedule;
      }
    });
    
    setTaskInsights(insights);
  }, [tasks]);

  // Function to record task execution result
  const recordTaskExecution = (
    taskId: string, 
    connectionIds: string[], 
    startTime: Date, 
    endTime: Date, 
    status: 'completed' | 'failed'
  ) => {
    taskIntelligence.addTaskExecution(taskId, connectionIds, startTime, endTime, status);
  };

  return {
    taskInsights,
    recordTaskExecution
  };
}
