
import React, { useState, useEffect } from 'react';
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { ApiEvent } from '@/types/api';

const EventNotifications = () => {
  const [events, setEvents] = useState<ApiEvent[]>([]);
  const [unprocessedCount, setUnprocessedCount] = useState(0);

  // Simulate loading events from an API
  useEffect(() => {
    const sampleEvents: ApiEvent[] = [
      {
        id: '1',
        connectionId: '1', // Salesforce
        type: 'customer_update',
        content: 'Customer #1245 updated their contact information',
        timestamp: new Date(Date.now() - 10 * 60000).toISOString(), // 10 mins ago
        processed: false,
        priority: 'medium'
      },
      {
        id: '2',
        connectionId: '3', // Slack
        type: 'message',
        content: 'New message in #sales channel requesting product information',
        timestamp: new Date(Date.now() - 25 * 60000).toISOString(), // 25 mins ago
        processed: false,
        priority: 'high'
      },
      {
        id: '3',
        connectionId: '2', // HubSpot
        type: 'lead_created',
        content: 'New lead created for Enterprise client Acme Corp',
        timestamp: new Date(Date.now() - 60 * 60000).toISOString(), // 1 hour ago
        processed: false,
        priority: 'urgent'
      },
      {
        id: '4',
        connectionId: '1', // Salesforce
        type: 'opportunity_update',
        content: 'Sales opportunity #5678 moved to negotiation stage',
        timestamp: new Date(Date.now() - 3 * 60 * 60000).toISOString(), // 3 hours ago
        processed: true,
        priority: 'medium'
      },
      {
        id: '5',
        connectionId: '4', // Gmail
        type: 'email',
        content: 'Received response from client regarding project proposal',
        timestamp: new Date(Date.now() - 5 * 60 * 60000).toISOString(), // 5 hours ago
        processed: true,
        priority: 'low'
      }
    ];
    
    setEvents(sampleEvents);
    
    // Count unprocessed events
    const unprocessed = sampleEvents.filter(event => !event.processed).length;
    setUnprocessedCount(unprocessed);
    
    // Simulate receiving new events periodically
    const interval = setInterval(() => {
      if (Math.random() > 0.7) { // 30% chance of new event
        const newEvent: ApiEvent = {
          id: Date.now().toString(),
          connectionId: ['1', '2', '3', '4'][Math.floor(Math.random() * 4)], // Random connection
          type: ['customer_update', 'message', 'lead_created', 'opportunity_update', 'email'][Math.floor(Math.random() * 5)],
          content: `New event from connected API at ${new Date().toLocaleTimeString()}`,
          timestamp: new Date().toISOString(),
          processed: false,
          priority: ['low', 'medium', 'high', 'urgent'][Math.floor(Math.random() * 4)] as 'low' | 'medium' | 'high' | 'urgent'
        };
        
        setEvents(prev => [newEvent, ...prev]);
        setUnprocessedCount(prev => prev + 1);
        toast.info(`New event received: ${newEvent.content}`);
      }
    }, 60000); // Check every minute
    
    return () => clearInterval(interval);
  }, []);

  const markAsProcessed = (eventId: string) => {
    setEvents(events.map(event => 
      event.id === eventId 
        ? { ...event, processed: true } 
        : event
    ));
    
    setUnprocessedCount(prev => Math.max(0, prev - 1));
    toast.success("Event marked as processed");
  };

  const markAllAsProcessed = () => {
    if (events.length === 0) return;
    
    setEvents(events.map(event => ({ ...event, processed: true })));
    setUnprocessedCount(0);
    toast.success("All events marked as processed");
  };

  const connectionNames: Record<string, string> = {
    '1': 'Salesforce',
    '2': 'HubSpot',
    '3': 'Slack',
    '4': 'Gmail'
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-amber-100 text-amber-800';
      case 'medium': return 'bg-blue-100 text-blue-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins !== 1 ? 's' : ''} ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    if (diffDays < 7) return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    
    return date.toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <h2 className="text-2xl font-bold flex items-center gap-2">
              Event Notifications
              {unprocessedCount > 0 && (
                <Badge variant="destructive" className="ml-2">
                  {unprocessedCount} Unprocessed
                </Badge>
              )}
            </h2>
            <p className="text-muted-foreground mt-1">
              Real-time events from connected APIs and services
            </p>
          </div>
          <Button 
            onClick={markAllAsProcessed} 
            variant="outline" 
            className="mt-2 md:mt-0"
            disabled={unprocessedCount === 0}
          >
            Mark All as Processed
          </Button>
        </div>
        
        <ScrollArea className="h-[500px]">
          {events.length === 0 ? (
            <p className="text-muted-foreground text-center py-8">
              No events received yet. Connect more APIs to start receiving events.
            </p>
          ) : (
            <div className="space-y-4">
              {events.map((event) => (
                <div 
                  key={event.id} 
                  className={`p-4 border rounded-lg transition-colors ${!event.processed ? 'border-l-4 border-l-blue-500' : ''}`}
                >
                  <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-2">
                    <div className="flex items-center gap-2">
                      <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(event.priority)}`}>
                        {event.priority.charAt(0).toUpperCase() + event.priority.slice(1)}
                      </span>
                      <span className="font-medium">
                        {connectionNames[event.connectionId] || 'Unknown API'}
                      </span>
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {formatTimestamp(event.timestamp)}
                    </span>
                  </div>
                  <p className="my-2">{event.content}</p>
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-xs px-2 py-1 bg-secondary rounded-full">
                      {event.type.replace('_', ' ')}
                    </span>
                    {!event.processed && (
                      <Button 
                        size="sm" 
                        variant="ghost" 
                        onClick={() => markAsProcessed(event.id)}
                      >
                        Mark as Processed
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </Card>
    </div>
  );
};

export default EventNotifications;
