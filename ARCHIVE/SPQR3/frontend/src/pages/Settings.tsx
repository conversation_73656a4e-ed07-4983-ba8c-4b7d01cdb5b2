
import React, { useState } from 'react';
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { ScrollArea } from "@/components/ui/scroll-area";

const Settings = () => {
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [emailAlerts, setEmailAlerts] = useState(true);
  const [soundAlerts, setSoundAlerts] = useState(false);
  const [apiRefreshInterval, setApiRefreshInterval] = useState(15);
  const [theme, setTheme] = useState('system');
  const [companyName, setCompanyName] = useState('Acme Corporation');
  const [adminEmail, setAdminEmail] = useState('<EMAIL>');
  
  const saveGeneral = () => {
    toast.success("General settings saved successfully");
  };
  
  const saveNotifications = () => {
    toast.success("Notification settings saved successfully");
  };
  
  const saveApi = () => {
    toast.success("API settings saved successfully");
  };
  
  const saveAppearance = () => {
    toast.success("Appearance settings saved successfully");
  };

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Settings</h2>
      
      <Card className="p-6">
        <Tabs defaultValue="general" className="w-full">
          <TabsList className="grid grid-cols-4 mb-6">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
            <TabsTrigger value="api">API Settings</TabsTrigger>
            <TabsTrigger value="appearance">Appearance</TabsTrigger>
          </TabsList>
          
          <ScrollArea className="h-[500px] pr-4">
            <TabsContent value="general">
              <h3 className="text-lg font-semibold mb-4">General Settings</h3>
              
              <div className="space-y-6">
                <div className="grid gap-3">
                  <Label htmlFor="companyName">Company Name</Label>
                  <Input
                    id="companyName"
                    value={companyName}
                    onChange={(e) => setCompanyName(e.target.value)}
                  />
                </div>
                
                <div className="grid gap-3">
                  <Label htmlFor="adminEmail">Admin Email</Label>
                  <Input
                    id="adminEmail"
                    type="email"
                    value={adminEmail}
                    onChange={(e) => setAdminEmail(e.target.value)}
                  />
                </div>
                
                <Separator />
                
                <div className="grid gap-3">
                  <Label htmlFor="timezone">Timezone</Label>
                  <select 
                    id="timezone"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  >
                    <option value="UTC">UTC</option>
                    <option value="EST">Eastern Time (EST)</option>
                    <option value="CST">Central Time (CST)</option>
                    <option value="MST">Mountain Time (MST)</option>
                    <option value="PST">Pacific Time (PST)</option>
                  </select>
                </div>
                
                <div className="grid gap-3">
                  <Label htmlFor="language">Language</Label>
                  <select 
                    id="language"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  >
                    <option value="en">English</option>
                    <option value="es">Spanish</option>
                    <option value="fr">French</option>
                    <option value="de">German</option>
                    <option value="ja">Japanese</option>
                  </select>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch id="dataCollection" defaultChecked />
                  <Label htmlFor="dataCollection">Enable anonymous usage statistics</Label>
                </div>
                
                <Button onClick={saveGeneral}>Save General Settings</Button>
              </div>
            </TabsContent>
            
            <TabsContent value="notifications">
              <h3 className="text-lg font-semibold mb-4">Notification Settings</h3>
              
              <div className="space-y-6">
                <div className="flex items-center space-x-2">
                  <Switch 
                    id="notificationsEnabled" 
                    checked={notificationsEnabled}
                    onCheckedChange={setNotificationsEnabled}
                  />
                  <Label htmlFor="notificationsEnabled">Enable notifications</Label>
                </div>
                
                <div className="pl-6 space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch 
                      id="emailAlerts" 
                      checked={emailAlerts}
                      onCheckedChange={setEmailAlerts}
                      disabled={!notificationsEnabled}
                    />
                    <Label htmlFor="emailAlerts" className={!notificationsEnabled ? "text-muted-foreground" : ""}>
                      Email alerts
                    </Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Switch 
                      id="soundAlerts" 
                      checked={soundAlerts}
                      onCheckedChange={setSoundAlerts}
                      disabled={!notificationsEnabled}
                    />
                    <Label htmlFor="soundAlerts" className={!notificationsEnabled ? "text-muted-foreground" : ""}>
                      Sound alerts
                    </Label>
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-4">
                  <h4 className="font-medium">Notification Types</h4>
                  
                  <div className="flex items-center space-x-2">
                    <Switch id="apiEvents" defaultChecked disabled={!notificationsEnabled} />
                    <Label htmlFor="apiEvents" className={!notificationsEnabled ? "text-muted-foreground" : ""}>
                      API Connection Events
                    </Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Switch id="taskNotifications" defaultChecked disabled={!notificationsEnabled} />
                    <Label htmlFor="taskNotifications" className={!notificationsEnabled ? "text-muted-foreground" : ""}>
                      Task Completions & Failures
                    </Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Switch id="systemAlerts" defaultChecked disabled={!notificationsEnabled} />
                    <Label htmlFor="systemAlerts" className={!notificationsEnabled ? "text-muted-foreground" : ""}>
                      System Alerts
                    </Label>
                  </div>
                </div>
                
                <Button onClick={saveNotifications} disabled={!notificationsEnabled}>
                  Save Notification Settings
                </Button>
              </div>
            </TabsContent>
            
            <TabsContent value="api">
              <h3 className="text-lg font-semibold mb-4">API Connection Settings</h3>
              
              <div className="space-y-6">
                <div className="space-y-3">
                  <Label htmlFor="apiRefreshInterval">API Refresh Interval (minutes)</Label>
                  <Input
                    id="apiRefreshInterval"
                    type="number"
                    min="5"
                    value={apiRefreshInterval}
                    onChange={(e) => setApiRefreshInterval(parseInt(e.target.value))}
                  />
                </div>
                
                <Separator />
                
                <div className="space-y-4">
                  <h4 className="font-medium">Connection Defaults</h4>
                  
                  <div className="flex items-center space-x-2">
                    <Switch id="autoConnect" defaultChecked />
                    <Label htmlFor="autoConnect">Auto-connect on startup</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Switch id="persistConnections" defaultChecked />
                    <Label htmlFor="persistConnections">Persist connections between sessions</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Switch id="encryptCredentials" defaultChecked />
                    <Label htmlFor="encryptCredentials">Encrypt stored credentials</Label>
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-3">
                  <Label htmlFor="apiTimeout">API Timeout (seconds)</Label>
                  <Input
                    id="apiTimeout"
                    type="number"
                    min="5"
                    defaultValue="30"
                  />
                </div>
                
                <div className="space-y-3">
                  <Label htmlFor="maxRetries">Max Retry Attempts</Label>
                  <Input
                    id="maxRetries"
                    type="number"
                    min="0"
                    defaultValue="3"
                  />
                </div>
                
                <Button onClick={saveApi}>Save API Settings</Button>
              </div>
            </TabsContent>
            
            <TabsContent value="appearance">
              <h3 className="text-lg font-semibold mb-4">Appearance Settings</h3>
              
              <div className="space-y-6">
                <div className="space-y-3">
                  <Label>Theme</Label>
                  <div className="flex flex-wrap gap-4">
                    <Button 
                      variant={theme === 'light' ? "default" : "outline"} 
                      onClick={() => setTheme('light')}
                    >
                      Light
                    </Button>
                    <Button 
                      variant={theme === 'dark' ? "default" : "outline"} 
                      onClick={() => setTheme('dark')}
                    >
                      Dark
                    </Button>
                    <Button 
                      variant={theme === 'system' ? "default" : "outline"} 
                      onClick={() => setTheme('system')}
                    >
                      System
                    </Button>
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-3">
                  <Label>Accent Color</Label>
                  <div className="flex flex-wrap gap-3">
                    <div className="w-8 h-8 rounded-full bg-blue-500 cursor-pointer hover:ring-2 hover:ring-offset-2 hover:ring-blue-500"></div>
                    <div className="w-8 h-8 rounded-full bg-purple-500 cursor-pointer hover:ring-2 hover:ring-offset-2 hover:ring-purple-500"></div>
                    <div className="w-8 h-8 rounded-full bg-pink-500 cursor-pointer hover:ring-2 hover:ring-offset-2 hover:ring-pink-500"></div>
                    <div className="w-8 h-8 rounded-full bg-amber-500 cursor-pointer hover:ring-2 hover:ring-offset-2 hover:ring-amber-500"></div>
                    <div className="w-8 h-8 rounded-full bg-green-500 cursor-pointer hover:ring-2 hover:ring-offset-2 hover:ring-green-500"></div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch id="animationsEnabled" defaultChecked />
                  <Label htmlFor="animationsEnabled">Enable animations</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch id="compactMode" />
                  <Label htmlFor="compactMode">Compact mode</Label>
                </div>
                
                <Button onClick={saveAppearance}>Save Appearance Settings</Button>
              </div>
            </TabsContent>
          </ScrollArea>
        </Tabs>
      </Card>
    </div>
  );
};

export default Settings;
