import RagMemory from '@/components/RagMemory';
import ApiConnections from '@/components/ApiConnections';
import Dashboard from '@/components/Dashboard';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON> } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Settings, Bell, Calendar, BarChart, User, BookText } from 'lucide-react';

const Index = () => {
  return (
    <div className="min-h-screen bg-background">
      <header className="border-b shadow-sm">
        <div className="container mx-auto py-4 px-4 flex justify-between items-center">
          <h1 className="text-2xl font-bold">AI-Powered CRM</h1>
          <nav className="hidden md:flex space-x-4">
            <Link to="/tasks">
              <Button variant="ghost" size="sm">
                <Calendar className="h-4 w-4 mr-2" />
                Tasks
              </Button>
            </Link>
            <Link to="/notifications">
              <Button variant="ghost" size="sm">
                <Bell className="h-4 w-4 mr-2" />
                Notifications
              </Button>
            </Link>
            <Link to="/analytics">
              <Button variant="ghost" size="sm">
                <BarChart className="h-4 w-4 mr-2" />
                Analytics
              </Button>
            </Link>
            <Link to="/settings">
              <Button variant="ghost" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </Link>
            <Link to="/knowledge">
              <Button variant="ghost" size="sm">
                <BookText className="h-4 w-4 mr-2" />
                Knowledge
              </Button>
            </Link>
            <Link to="/account">
              <Button variant="outline" size="sm">
                <User className="h-4 w-4 mr-2" />
                Account
              </Button>
            </Link>
          </nav>
        </div>
      </header>
      
      <main className="container mx-auto py-8 px-4">
        <div className="md:hidden flex flex-wrap gap-2 mb-6">
          <Link to="/tasks" className="flex-1">
            <Button variant="outline" className="w-full">
              <Calendar className="h-4 w-4 mr-2" />
              Tasks
            </Button>
          </Link>
          <Link to="/notifications" className="flex-1">
            <Button variant="outline" className="w-full">
              <Bell className="h-4 w-4 mr-2" />
              Notifications
            </Button>
          </Link>
          <Link to="/analytics" className="flex-1">
            <Button variant="outline" className="w-full">
              <BarChart className="h-4 w-4 mr-2" />
              Analytics
            </Button>
          </Link>
          <Link to="/settings" className="flex-1">
            <Button variant="outline" className="w-full">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </Link>
          <Link to="/knowledge" className="flex-1">
            <Button variant="outline" className="w-full">
              <BookText className="h-4 w-4 mr-2" />
              Knowledge
            </Button>
          </Link>
          <Link to="/account" className="flex-1">
            <Button variant="outline" className="w-full">
              <User className="h-4 w-4 mr-2" />
              Account
            </Button>
          </Link>
        </div>
        
        <Tabs defaultValue="dashboard" className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-8">
            <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
            <TabsTrigger value="connections">API Connections</TabsTrigger>
            <TabsTrigger value="memory">RAG Memory</TabsTrigger>
          </TabsList>
          
          <TabsContent value="dashboard">
            <Dashboard />
          </TabsContent>
          
          <TabsContent value="connections">
            <ApiConnections />
          </TabsContent>
          
          <TabsContent value="memory">
            <RagMemory />
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
};

export default Index;
