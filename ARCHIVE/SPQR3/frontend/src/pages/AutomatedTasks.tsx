
import React, { useState, useEffect } from 'react';
import { toast } from "sonner";
import { AutomatedTask } from '@/types/api';
import { TaskForm } from '@/components/tasks/TaskForm';
import { TaskList } from '@/components/tasks/TaskList';
import { useTaskIntelligence } from '@/hooks/useTaskIntelligence';

const AutomatedTasks = () => {
  const [tasks, setTasks] = useState<AutomatedTask[]>([
    {
      id: '1',
      name: 'Daily Sales Data Sync',
      schedule: '0 0 * * *', // midnight every day
      lastRun: new Date(Date.now() - 86400000).toISOString(), // yesterday
      nextRun: new Date(Date.now() + 86400000).toISOString(), // tomorrow
      status: 'idle',
      connectionIds: ['1', '2']
    },
    {
      id: '2',
      name: 'Weekly Customer Report',
      schedule: '0 9 * * 1', // 9 AM every Monday
      lastRun: new Date(Date.now() - 604800000).toISOString(), // last week
      nextRun: new Date(Date.now() + 604800000).toISOString(), // next week
      status: 'completed',
      connectionIds: ['1']
    },
    {
      id: '3',
      name: 'Monthly Analytics Processing',
      schedule: '0 0 1 * *', // midnight on first day of month
      lastRun: new Date(Date.now() - 2592000000).toISOString(), // last month
      nextRun: new Date(Date.now() + 2592000000).toISOString(), // next month
      status: 'idle',
      connectionIds: ['2', '3']
    }
  ]);

  const connections = [
    { id: '1', name: 'Salesforce CRM' },
    { id: '2', name: 'HubSpot' },
    { id: '3', name: 'Slack' },
    { id: '4', name: 'Gmail' }
  ];

  // Get task intelligence insights
  const { taskInsights, recordTaskExecution } = useTaskIntelligence(tasks);

  const handleAddTask = (name: string, schedule: string, connectionIds: string[]) => {
    if (!name.trim() || connectionIds.length === 0) {
      toast.error("Task name and at least one connection are required");
      return;
    }

    const getCronExpression = () => {
      switch (schedule) {
        case 'daily': return '0 0 * * *';
        case 'weekly': return '0 0 * * 0';
        case 'monthly': return '0 0 1 * *';
        default: return schedule;
      }
    };

    const newTask: AutomatedTask = {
      id: Date.now().toString(),
      name,
      schedule: getCronExpression(),
      lastRun: undefined,
      nextRun: new Date(Date.now() + 86400000).toISOString(),
      status: 'idle',
      connectionIds
    };

    setTasks([...tasks, newTask]);
    toast.success(`Task "${name}" created successfully`);
  };

  const handleRunTask = (taskId: string) => {
    toast.info("Running task...");
    
    // Record start time
    const startTime = new Date();
    
    // Simulate task running
    setTimeout(() => {
      const endTime = new Date();
      const status = Math.random() > 0.2 ? 'completed' : 'failed';
      
      // Update task status
      setTasks(tasks.map(task => 
        task.id === taskId ? {
          ...task,
          status: status as 'completed' | 'failed' | 'idle' | 'running',
          lastRun: endTime.toISOString(),
          nextRun: new Date(Date.now() + 86400000).toISOString()
        } : task
      ));
      
      // Record this execution in our intelligence system
      const taskToUpdate = tasks.find(t => t.id === taskId);
      if (taskToUpdate) {
        recordTaskExecution(
          taskId,
          taskToUpdate.connectionIds,
          startTime,
          endTime,
          status as 'completed' | 'failed'
        );
      }
      
      if (status === 'completed') {
        toast.success("Task completed successfully");
      } else {
        toast.error("Task failed to complete");
      }
    }, Math.random() * 3000 + 1000); // Random duration between 1-4 seconds
  };

  const handleDeleteTask = (taskId: string) => {
    setTasks(tasks.filter(task => task.id !== taskId));
    toast.success("Task deleted successfully");
  };

  return (
    <div className="space-y-6">
      <TaskForm 
        onSubmit={handleAddTask}
        connections={connections}
      />
      <TaskList
        tasks={tasks}
        connections={connections}
        onRunTask={handleRunTask}
        onDeleteTask={handleDeleteTask}
        taskInsights={taskInsights}
      />
    </div>
  );
};

export default AutomatedTasks;
