
import React, { useState } from 'react';
import { Card } from "@/components/ui/card";
import { 
  <PERSON><PERSON><PERSON>, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  Line<PERSON>hart, 
  Line,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Legend
} from 'recharts';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";

const UserAnalytics = () => {
  const [timeRange, setTimeRange] = useState('week');

  // Sample data - in a real app this would come from an API
  const engagementData = {
    week: [
      { day: 'Mon', users: 120, actions: 340 },
      { day: 'Tue', users: 145, actions: 380 },
      { day: 'Wed', users: 132, actions: 310 },
      { day: 'Thu', users: 165, actions: 420 },
      { day: 'Fri', users: 155, actions: 390 },
      { day: 'Sat', users: 90, actions: 220 },
      { day: 'Sun', users: 85, actions: 200 }
    ],
    month: [
      { day: 'Week 1', users: 820, actions: 2400 },
      { day: 'Week 2', users: 932, actions: 2600 },
      { day: 'Week 3', users: 901, actions: 2300 },
      { day: 'Week 4', users: 934, actions: 2800 }
    ],
    year: [
      { day: 'Jan', users: 2400, actions: 7000 },
      { day: 'Feb', users: 2210, actions: 6500 },
      { day: 'Mar', users: 2290, actions: 6800 },
      { day: 'Apr', users: 2500, actions: 7200 },
      { day: 'May', users: 2800, actions: 8300 },
      { day: 'Jun', users: 2650, actions: 7900 },
      { day: 'Jul', users: 2790, actions: 8100 },
      { day: 'Aug', users: 3000, actions: 8700 },
      { day: 'Sep', users: 2860, actions: 8200 },
      { day: 'Oct', users: 2920, actions: 8400 },
      { day: 'Nov', users: 3100, actions: 8900 },
      { day: 'Dec', users: 3200, actions: 9100 }
    ]
  };

  const userSourceData = [
    { name: 'Direct', value: 40 },
    { name: 'Email', value: 30 },
    { name: 'Social', value: 20 },
    { name: 'Referral', value: 10 }
  ];

  const userActionData = [
    { name: 'View', value: 45 },
    { name: 'Edit', value: 30 },
    { name: 'Create', value: 15 },
    { name: 'Delete', value: 10 }
  ];

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];
  
  const getUserData = () => {
    return engagementData[timeRange as keyof typeof engagementData];
  };

  const statistics = [
    { label: 'Total Users', value: '3,245', change: '+12%', positive: true },
    { label: 'Active Users', value: '2,871', change: '+8%', positive: true },
    { label: 'Avg. Session', value: '8.5 min', change: '+15%', positive: true },
    { label: 'Bounce Rate', value: '24%', change: '-3%', positive: true }
  ];

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
        <h2 className="text-2xl font-bold">User Analytics</h2>
        <div className="mt-2 md:mt-0">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Time Range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">Last 7 Days</SelectItem>
              <SelectItem value="month">Last Month</SelectItem>
              <SelectItem value="year">This Year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {statistics.map((stat, idx) => (
          <Card key={idx} className="p-4">
            <div className="text-sm text-muted-foreground">{stat.label}</div>
            <div className="text-2xl font-bold mt-1">{stat.value}</div>
            <div className={`text-xs mt-1 ${stat.positive ? 'text-green-600' : 'text-red-600'}`}>
              {stat.change} since last {timeRange}
            </div>
          </Card>
        ))}
      </div>
      
      <Card className="p-6">
        <h3 className="text-xl font-bold mb-4">User Engagement</h3>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={getUserData()}
              margin={{
                top: 5,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="day" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="users" stroke="#8884d8" activeDot={{ r: 8 }} />
              <Line type="monotone" dataKey="actions" stroke="#82ca9d" />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </Card>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-xl font-bold mb-4">User Sources</h3>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={userSourceData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({name, percent}) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {userSourceData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Legend />
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </Card>
        
        <Card className="p-6">
          <h3 className="text-xl font-bold mb-4">User Actions</h3>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={userActionData}
                margin={{
                  top: 5,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="value" fill="#8884d8">
                  {userActionData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default UserAnalytics;
