
import React from 'react';
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { BookText, BookOpen } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import type { Memory } from '@/types/rag';

const Knowledge = () => {
  const [memories, setMemories] = React.useState<Memory[]>([]);
  const [searchQuery, setSearchQuery] = React.useState('');

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="container mx-auto space-y-6">
        <h1 className="text-2xl font-bold">Knowledge Base</h1>
        
        <Card className="p-6">
          <Tabs defaultValue="browse" className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="browse">
                <BookOpen className="h-4 w-4 mr-2" />
                Browse Knowledge
              </TabsTrigger>
              <TabsTrigger value="add">
                <BookText className="h-4 w-4 mr-2" />
                Add Knowledge
              </TabsTrigger>
            </TabsList>
            
            <ScrollArea className="h-[600px]">
              <TabsContent value="browse" className="space-y-4">
                <div className="flex gap-4">
                  <Input
                    placeholder="Search knowledge base..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="flex-1"
                  />
                  <Button variant="outline">Search</Button>
                </div>
                
                <div className="grid gap-4">
                  {memories.length === 0 ? (
                    <Card className="p-6 text-center text-muted-foreground">
                      No knowledge entries found. Add some knowledge to get started!
                    </Card>
                  ) : (
                    memories.map((memory) => (
                      <Card key={memory.id} className="p-4 space-y-2">
                        <div className="font-medium">{memory.category}</div>
                        <p className="text-sm text-muted-foreground">{memory.content}</p>
                        <div className="text-xs text-muted-foreground">
                          Added: {new Date(memory.timestamp).toLocaleDateString()}
                        </div>
                      </Card>
                    ))
                  )}
                </div>
              </TabsContent>
              
              <TabsContent value="add" className="space-y-4">
                <form className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Category</label>
                    <select className="w-full p-2 border rounded-md">
                      <option value="customer">Customer Information</option>
                      <option value="process">Process Knowledge</option>
                      <option value="knowledge">General Knowledge</option>
                    </select>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Content</label>
                    <textarea 
                      className="w-full min-h-[200px] p-2 border rounded-md" 
                      placeholder="Enter knowledge content here..."
                    />
                  </div>
                  
                  <Button type="submit" className="w-full">
                    Add to Knowledge Base
                  </Button>
                </form>
              </TabsContent>
            </ScrollArea>
          </Tabs>
        </Card>
      </div>
    </div>
  );
};

export default Knowledge;
