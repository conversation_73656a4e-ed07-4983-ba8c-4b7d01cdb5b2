
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface AdaptiveSystemContextProps {
  systemLearningEnabled: boolean;
  toggleSystemLearning: () => void;
  systemInsights: {
    tasksOptimized: number;
    suggestionsGenerated: number;
    systemEfficiency: number; // 0-1 scale
    lastUpdated: Date | null;
  };
}

const AdaptiveSystemContext = createContext<AdaptiveSystemContextProps | undefined>(undefined);

export const AdaptiveSystemProvider = ({ children }: { children: ReactNode }) => {
  const [systemLearningEnabled, setSystemLearningEnabled] = useState(true);
  const [systemInsights, setSystemInsights] = useState({
    tasksOptimized: 0,
    suggestionsGenerated: 0,
    systemEfficiency: 0.7, // Starting baseline
    lastUpdated: null as Date | null
  });

  // Simulate the system learning over time
  useEffect(() => {
    if (!systemLearningEnabled) return;
    
    const interval = setInterval(() => {
      setSystemInsights(prev => {
        // Simulate gradual improvement as system learns
        const newEfficiency = Math.min(0.98, prev.systemEfficiency + Math.random() * 0.02);
        
        // Occasionally generate new task optimizations
        const newTasksOptimized = Math.random() > 0.7 
          ? prev.tasksOptimized + 1 
          : prev.tasksOptimized;
          
        // Occasionally generate new suggestions
        const newSuggestionsGenerated = Math.random() > 0.8
          ? prev.suggestionsGenerated + 1
          : prev.suggestionsGenerated;
          
        return {
          tasksOptimized: newTasksOptimized,
          suggestionsGenerated: newSuggestionsGenerated,
          systemEfficiency: newEfficiency,
          lastUpdated: new Date()
        };
      });
    }, 30000); // Every 30 seconds
    
    return () => clearInterval(interval);
  }, [systemLearningEnabled]);

  const toggleSystemLearning = () => {
    setSystemLearningEnabled(prev => !prev);
  };

  return (
    <AdaptiveSystemContext.Provider
      value={{
        systemLearningEnabled,
        toggleSystemLearning,
        systemInsights
      }}
    >
      {children}
    </AdaptiveSystemContext.Provider>
  );
};

export const useAdaptiveSystem = () => {
  const context = useContext(AdaptiveSystemContext);
  if (context === undefined) {
    throw new Error('useAdaptiveSystem must be used within an AdaptiveSystemProvider');
  }
  return context;
};
