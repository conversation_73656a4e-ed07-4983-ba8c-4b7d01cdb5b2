
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { ApiConnection } from '@/types/api';

interface ApiInsights {
  responseTime: number;
  successRate: number;
  errorPatterns: string[];
  lastAnalyzed: Date | null;
}

interface ApiIntelligenceState {
  insights: Record<string, ApiInsights>;
  isLearningEnabled: boolean;
}

interface ApiIntelligenceContextProps {
  connectionInsights: Record<string, ApiInsights>;
  isLearningEnabled: boolean;
  toggleLearning: () => void;
  recordApiResponse: (connectionId: string, success: boolean, responseTime: number, error?: string) => void;
}

const ApiIntelligenceContext = createContext<ApiIntelligenceContextProps | undefined>(undefined);

export const ApiIntelligenceProvider = ({ children }: { children: ReactNode }) => {
  const [state, setState] = useState<ApiIntelligenceState>({
    insights: {},
    isLearningEnabled: true,
  });

  const toggleLearning = () => {
    setState(prev => ({ ...prev, isLearningEnabled: !prev.isLearningEnabled }));
  };

  const recordApiResponse = (connectionId: string, success: boolean, responseTime: number, error?: string) => {
    if (!state.isLearningEnabled) return;

    setState(prev => {
      const currentInsights = prev.insights[connectionId] || {
        responseTime: 0,
        successRate: 1,
        errorPatterns: [],
        lastAnalyzed: null,
      };

      // Update response time (rolling average)
      const newResponseTime = currentInsights.responseTime === 0
        ? responseTime
        : (currentInsights.responseTime * 0.7 + responseTime * 0.3);

      // Update success rate
      const successCount = currentInsights.successRate * 10;
      const newSuccessRate = (successCount + (success ? 1 : 0)) / (10 + 1);

      // Update error patterns
      const errorPatterns = [...currentInsights.errorPatterns];
      if (error && !errorPatterns.includes(error)) {
        errorPatterns.push(error);
        if (errorPatterns.length > 5) errorPatterns.shift();
      }

      return {
        ...prev,
        insights: {
          ...prev.insights,
          [connectionId]: {
            responseTime: newResponseTime,
            successRate: newSuccessRate,
            errorPatterns,
            lastAnalyzed: new Date(),
          },
        },
      };
    });
  };

  return (
    <ApiIntelligenceContext.Provider
      value={{
        connectionInsights: state.insights,
        isLearningEnabled: state.isLearningEnabled,
        toggleLearning,
        recordApiResponse,
      }}
    >
      {children}
    </ApiIntelligenceContext.Provider>
  );
};

export const useApiIntelligence = () => {
  const context = useContext(ApiIntelligenceContext);
  if (!context) {
    throw new Error('useApiIntelligence must be used within ApiIntelligenceProvider');
  }
  return context;
};
