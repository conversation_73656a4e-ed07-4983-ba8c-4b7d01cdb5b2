
import { AutomatedTask } from '@/types/api';

// Track task execution metrics for machine learning
interface TaskExecutionMetric {
  taskId: string;
  startTime: string;
  endTime: string;
  status: 'completed' | 'failed';
  duration: number; // in milliseconds
  connectionIds: string[];
}

class TaskIntelligenceService {
  private taskMetrics: TaskExecutionMetric[] = [];
  private taskPatterns: Record<string, { avgDuration: number; successRate: number }> = {};

  // Add execution data to our learning system
  addTaskExecution(
    taskId: string,
    connectionIds: string[],
    startTime: Date,
    endTime: Date,
    status: 'completed' | 'failed'
  ): void {
    const duration = endTime.getTime() - startTime.getTime();
    
    this.taskMetrics.push({
      taskId,
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      status,
      duration,
      connectionIds
    });
    
    // Update patterns based on new data
    this.analyzePatterns();
  }

  // Analyze execution patterns to improve predictions
  private analyzePatterns(): void {
    const taskGroups: Record<string, TaskExecutionMetric[]> = {};
    
    // Group metrics by taskId
    this.taskMetrics.forEach(metric => {
      if (!taskGroups[metric.taskId]) {
        taskGroups[metric.taskId] = [];
      }
      taskGroups[metric.taskId].push(metric);
    });
    
    // Calculate patterns for each task
    Object.entries(taskGroups).forEach(([taskId, metrics]) => {
      const totalExecutions = metrics.length;
      const successfulExecutions = metrics.filter(m => m.status === 'completed').length;
      const successRate = totalExecutions > 0 ? successfulExecutions / totalExecutions : 0;
      
      // Calculate average duration of successful executions
      const successfulMetrics = metrics.filter(m => m.status === 'completed');
      const avgDuration = successfulMetrics.length > 0 
        ? successfulMetrics.reduce((sum, metric) => sum + metric.duration, 0) / successfulMetrics.length
        : 0;
      
      this.taskPatterns[taskId] = { avgDuration, successRate };
    });
  }

  // Predict estimated completion time for a task
  predictTaskDuration(taskId: string): number {
    return this.taskPatterns[taskId]?.avgDuration || 60000; // Default to 1 minute if no data
  }
  
  // Get success probability for a task
  getTaskSuccessProbability(taskId: string): number {
    return this.taskPatterns[taskId]?.successRate || 0.5; // Default 50% if no data
  }
  
  // Recommend optimal time to run tasks based on historical performance
  recommendOptimalSchedule(): Record<string, string> {
    // Simple implementation - could be enhanced with more sophisticated algorithms
    const recommendations: Record<string, string> = {};
    
    // For now, just recommend based on success rate patterns
    Object.keys(this.taskPatterns).forEach(taskId => {
      // This is a placeholder logic - in real system, we'd use more sophisticated time-series analysis
      recommendations[taskId] = '0 0 * * *'; // Default daily at midnight
    });
    
    return recommendations;
  }
}

// Singleton instance for global access
export const taskIntelligence = new TaskIntelligenceService();
