
import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card } from "@/components/ui/card";

interface TaskFormProps {
  onSubmit: (name: string, schedule: string, connectionIds: string[]) => void;
  connections: Array<{ id: string; name: string }>;
}

export const TaskForm = ({ onSubmit, connections }: TaskFormProps) => {
  const [name, setName] = useState('');
  const [schedule, setSchedule] = useState('daily');
  const [selectedConnections, setSelectedConnections] = useState<string[]>([]);

  const scheduleOptions = [
    { value: 'daily', label: 'Daily' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'monthly', label: 'Monthly' },
    { value: 'custom', label: 'Custom Cron' }
  ];

  const handleSubmit = () => {
    onSubmit(name, schedule, selectedConnections);
    setName('');
    setSchedule('daily');
    setSelectedConnections([]);
  };

  const toggleConnection = (connectionId: string) => {
    setSelectedConnections(prev => 
      prev.includes(connectionId)
        ? prev.filter(id => id !== connectionId)
        : [...prev, connectionId]
    );
  };

  return (
    <Card className="p-6">
      <h2 className="text-2xl font-bold mb-6">Automated Tasks</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <label className="block text-sm font-medium mb-1">Task Name</label>
          <Input
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="e.g., Daily Sales Report"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Schedule</label>
          <Select value={schedule} onValueChange={setSchedule}>
            <SelectTrigger>
              <SelectValue placeholder="Select schedule" />
            </SelectTrigger>
            <SelectContent>
              {scheduleOptions.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div className="mb-4">
        <label className="block text-sm font-medium mb-1">API Connections</label>
        <div className="flex flex-wrap gap-2">
          {connections.map(connection => (
            <Button
              key={connection.id}
              type="button"
              variant={selectedConnections.includes(connection.id) ? "default" : "outline"}
              className="mb-2"
              onClick={() => toggleConnection(connection.id)}
            >
              {connection.name}
            </Button>
          ))}
        </div>
      </div>
      
      <Button onClick={handleSubmit} className="w-full md:w-auto">Create Task</Button>
    </Card>
  );
};
