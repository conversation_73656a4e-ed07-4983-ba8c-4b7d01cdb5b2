
import React from 'react';
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { AutomatedTask } from '@/types/api';
import { TaskItem } from './TaskItem';

interface TaskListProps {
  tasks: AutomatedTask[];
  connections: Array<{ id: string; name: string }>;
  onRunTask: (taskId: string) => void;
  onDeleteTask: (taskId: string) => void;
  taskInsights?: Record<string, {
    estimatedDuration: number;
    successProbability: number;
    recommendedSchedule?: string;
  }>;
}

export const TaskList = ({ tasks, connections, onRunTask, onDeleteTask, taskInsights }: TaskListProps) => {
  return (
    <Card className="p-6">
      <h3 className="text-xl font-semibold mb-4">Task Schedule</h3>
      <ScrollArea className="h-[400px]">
        {tasks.length === 0 ? (
          <p className="text-muted-foreground text-center py-8">
            No tasks scheduled yet. Create your first task above.
          </p>
        ) : (
          <div className="space-y-4">
            {tasks.map((task) => (
              <TaskItem
                key={task.id}
                task={task}
                connections={connections}
                onRun={onRunTask}
                onDelete={onDeleteTask}
                insights={taskInsights?.[task.id]}
              />
            ))}
          </div>
        )}
      </ScrollArea>
    </Card>
  );
};
