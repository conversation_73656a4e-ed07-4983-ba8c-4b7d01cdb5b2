
import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Calendar, Clock, BarChart } from 'lucide-react';
import { AutomatedTask } from '@/types/api';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface TaskItemProps {
  task: AutomatedTask;
  connections: Array<{ id: string; name: string }>;
  onRun: (taskId: string) => void;
  onDelete: (taskId: string) => void;
  insights?: {
    estimatedDuration?: number;
    successProbability?: number;
    recommendedSchedule?: string;
  };
}

export const TaskItem = ({ task, connections, onRun, onDelete, insights }: TaskItemProps) => {
  // Format duration to human readable format
  const formatDuration = (ms: number): string => {
    if (!ms) return 'Unknown';
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    
    if (minutes < 1) return `${seconds} seconds`;
    if (minutes < 60) return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
    
    const hours = Math.floor(minutes / 60);
    return `${hours} hour${hours !== 1 ? 's' : ''} ${minutes % 60} min`;
  };

  // Format probability as percentage
  const formatProbability = (probability?: number): string => {
    if (probability === undefined) return 'N/A';
    return `${Math.round(probability * 100)}%`;
  };

  return (
    <div className="p-4 border rounded-lg">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-3">
        <div>
          <h4 className="font-medium">{task.name}</h4>
          <p className="text-sm flex items-center gap-1 text-muted-foreground">
            <Calendar className="h-4 w-4" /> {formatSchedule(task.schedule)}
          </p>
        </div>
        <div className="flex mt-2 md:mt-0">
          <Button 
            variant="secondary" 
            size="sm" 
            onClick={() => onRun(task.id)}
            className="mr-2"
          >
            Run Now
          </Button>
          <Button 
            variant="destructive" 
            size="sm" 
            onClick={() => onDelete(task.id)}
          >
            Delete
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
        <div className="flex items-center gap-2">
          <span className="flex items-center">
            <Clock className="h-4 w-4 mr-1" /> Last Run:
          </span>
          <span className="text-muted-foreground">
            {task.lastRun 
              ? new Date(task.lastRun).toLocaleString()
              : 'Never run'}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <span className="flex items-center">
            <Clock className="h-4 w-4 mr-1" /> Next Run:
          </span>
          <span className="text-muted-foreground">
            {task.nextRun 
              ? new Date(task.nextRun).toLocaleString()
              : 'Not scheduled'}
          </span>
        </div>
      </div>
      
      {insights && (
        <div className="mt-3 pt-3 border-t">
          <h5 className="text-xs font-medium mb-2 flex items-center">
            <BarChart className="h-3 w-3 mr-1" /> Task Insights
          </h5>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center">
                    <span>Est. Duration:</span>
                    <span className="ml-1 font-medium">
                      {formatDuration(insights.estimatedDuration || 0)}
                    </span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Estimated time to complete based on historical runs</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center">
                    <span>Success Rate:</span>
                    <span className="ml-1 font-medium">
                      {formatProbability(insights.successProbability)}
                    </span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Historical success rate for this task</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      )}
      
      <div className="mt-2 flex justify-between text-xs">
        <span className={`px-2 py-1 rounded-full ${
          task.status === 'completed' ? 'bg-green-100 text-green-800' :
          task.status === 'running' ? 'bg-blue-100 text-blue-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {task.status.charAt(0).toUpperCase() + task.status.slice(1)}
        </span>
        <div className="text-muted-foreground">
          Connected APIs: {task.connectionIds.map(id => {
            const connection = connections.find(c => c.id === id);
            return connection ? connection.name : id;
          }).join(', ')}
        </div>
      </div>
    </div>
  );
};

const formatSchedule = (cronExpression: string) => {
  if (cronExpression === '0 0 * * *') return 'Daily at midnight';
  if (cronExpression === '0 0 * * 0') return 'Weekly on Sunday';
  if (cronExpression === '0 0 1 * *') return 'Monthly on the 1st';
  return cronExpression;
};
