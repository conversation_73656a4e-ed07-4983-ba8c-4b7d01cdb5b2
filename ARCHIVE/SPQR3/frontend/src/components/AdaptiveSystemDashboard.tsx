
import React from 'react';
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useAdaptiveSystem } from "@/contexts/AdaptiveSystemContext";
import { <PERSON>, TrendingUp, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";

export const AdaptiveSystemDashboard = () => {
  const { systemLearningEnabled, systemInsights, toggleSystemLearning } = useAdaptiveSystem();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">System Intelligence</h2>
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">
            Learning Mode: {systemLearningEnabled ? 'Active' : 'Paused'}
          </span>
          <button
            onClick={toggleSystemLearning}
            className={`px-4 py-2 rounded-md transition-colors ${
              systemLearningEnabled
                ? 'bg-primary text-primary-foreground'
                : 'bg-secondary text-secondary-foreground'
            }`}
          >
            {systemLearningEnabled ? 'Pause Learning' : 'Resume Learning'}
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4 flex flex-col space-y-2">
          <div className="flex items-center justify-between">
            <h3 className="font-medium">System Efficiency</h3>
            <Brain className="h-4 w-4 text-primary" />
          </div>
          <Progress value={systemInsights.systemEfficiency * 100} className="my-2" />
          <p className="text-2xl font-bold">{(systemInsights.systemEfficiency * 100).toFixed(1)}%</p>
          <p className="text-sm text-muted-foreground">Overall system performance</p>
        </Card>

        <Card className="p-4 flex flex-col space-y-2">
          <div className="flex items-center justify-between">
            <h3 className="font-medium">Tasks Optimized</h3>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </div>
          <p className="text-2xl font-bold">{systemInsights.tasksOptimized}</p>
          <p className="text-sm text-muted-foreground">Automatically improved workflows</p>
        </Card>

        <Card className="p-4 flex flex-col space-y-2">
          <div className="flex items-center justify-between">
            <h3 className="font-medium">Insights Generated</h3>
            <Zap className="h-4 w-4 text-yellow-500" />
          </div>
          <p className="text-2xl font-bold">{systemInsights.suggestionsGenerated}</p>
          <p className="text-sm text-muted-foreground">AI-powered suggestions</p>
        </Card>

        <Card className="p-4 flex flex-col space-y-2">
          <div className="flex items-center justify-between">
            <h3 className="font-medium">Last Update</h3>
            <BarChart className="h-4 w-4 text-blue-500" />
          </div>
          <p className="text-2xl font-bold">
            {systemInsights.lastUpdated
              ? new Date(systemInsights.lastUpdated).toLocaleTimeString()
              : 'Never'}
          </p>
          <p className="text-sm text-muted-foreground">System learning timestamp</p>
        </Card>
      </div>
    </div>
  );
};
