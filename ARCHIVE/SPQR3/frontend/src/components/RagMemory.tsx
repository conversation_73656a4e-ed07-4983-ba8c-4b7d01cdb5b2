
import React, { useState } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from "@/components/ui/select";
import { toast } from "sonner";
import { Memory } from '@/types/rag';

const RagMemory = () => {
  const [memories, setMemories] = useState<Memory[]>([]);
  const [newMemory, setNewMemory] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [category, setCategory] = useState<'customer' | 'process' | 'knowledge'>('knowledge');

  const addMemory = () => {
    if (!newMemory.trim()) {
      toast.error("Memory content cannot be empty");
      return;
    }

    const memory: Memory = {
      id: Date.now().toString(),
      content: newMemory,
      timestamp: new Date().toISOString(),
      category,
    };

    setMemories([...memories, memory]);
    setNewMemory('');
    toast.success("Memory added successfully");
  };

  const searchMemories = () => {
    // Simple text search for now - will be enhanced with embeddings later
    if (!searchQuery.trim()) {
      toast.error("Please enter a search query");
      return;
    }
    
    const results = memories.filter(memory => 
      memory.content.toLowerCase().includes(searchQuery.toLowerCase())
    );
    toast.info(`Found ${results.length} matching memories`);
    
    // Highlight the results temporarily
    const highlightedMemories = memories.map(memory => ({
      ...memory,
      highlighted: memory.content.toLowerCase().includes(searchQuery.toLowerCase())
    }));
    
    // This is just for UI feedback - in a real app you'd maintain a separate search results state
    setMemories(highlightedMemories as Memory[]);
    setTimeout(() => {
      setMemories(memories);
    }, 2000);
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <h2 className="text-2xl font-bold mb-6">RAG Memory System</h2>
        
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="md:col-span-3">
              <Input
                value={newMemory}
                onChange={(e) => setNewMemory(e.target.value)}
                placeholder="Add new memory..."
                className="w-full"
              />
            </div>
            <div className="md:col-span-1">
              <Select 
                value={category} 
                onValueChange={(value) => setCategory(value as 'customer' | 'process' | 'knowledge')}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="customer">Customer</SelectItem>
                  <SelectItem value="process">Process</SelectItem>
                  <SelectItem value="knowledge">Knowledge</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <Button onClick={addMemory} className="w-full md:w-auto">Add Memory</Button>
          
          <div className="border-t pt-4 mt-4">
            <h3 className="text-lg font-medium mb-3">Search Memory Database</h3>
            <div className="flex gap-2">
              <Input
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search memories..."
                className="flex-1"
                onKeyDown={(e) => e.key === 'Enter' && searchMemories()}
              />
              <Button onClick={searchMemories} variant="secondary">Search</Button>
            </div>
          </div>
        </div>
      </Card>

      <Card className="p-6">
        <h3 className="text-xl font-semibold mb-4">Stored Memories</h3>
        <ScrollArea className="h-[300px]">
          {memories.length === 0 ? (
            <p className="text-muted-foreground text-center py-8">
              No memories stored yet. Add your first memory above.
            </p>
          ) : (
            <div className="space-y-2">
              {memories.map((memory) => (
                <div 
                  key={memory.id} 
                  className={`p-3 rounded-lg ${
                    (memory as any).highlighted 
                      ? 'bg-primary/20 border border-primary' 
                      : 'bg-secondary'
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <p className="text-sm">{memory.content}</p>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      memory.category === 'customer' ? 'bg-blue-100 text-blue-800' :
                      memory.category === 'process' ? 'bg-amber-100 text-amber-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {memory.category}
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {new Date(memory.timestamp).toLocaleString()}
                  </p>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </Card>
    </div>
  );
};

export default RagMemory;
