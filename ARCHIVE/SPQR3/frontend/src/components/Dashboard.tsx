import React from 'react';
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell } from 'recharts';
import { AdaptiveSystemDashboard } from './AdaptiveSystemDashboard';

const Dashboard = () => {
  // Sample data - in a real app this would come from API connections
  const activityData = [
    { name: 'Email', count: 42 },
    { name: 'Slack', count: 78 },
    { name: 'Salesforce', count: 34 },
    { name: '<PERSON><PERSON>', count: 23 },
    { name: 'GitHub', count: 56 },
  ];
  
  const pieData = [
    { name: 'Customer Requests', value: 35 },
    { name: 'Internal Processes', value: 45 },
    { name: 'Automated Tasks', value: 20 },
  ];
  
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28'];

  const recentEvents = [
    { id: 1, source: 'Slack', event: 'New high-priority thread in #sales channel', time: '5 minutes ago' },
    { id: 2, source: 'Email', event: 'Customer support <NAME_EMAIL>', time: '12 minutes ago' },
    { id: 3, source: 'Salesforce', event: 'New opportunity created worth $15,000', time: '42 minutes ago' },
    { id: 4, source: 'Jira', event: 'Ticket CRM-423 status changed to "In Review"', time: '1 hour ago' },
    { id: 5, source: 'GitHub', event: 'Pull request merged to main branch', time: '2 hours ago' },
  ];

  const automatedTasks = [
    { id: 1, task: 'Daily sales data synchronization', status: 'Completed', time: '4:00 AM' },
    { id: 2, task: 'Customer feedback analysis', status: 'Running', time: 'In progress' },
    { id: 3, task: 'Weekly performance report', status: 'Scheduled', time: 'Tomorrow 6:00 AM' },
    { id: 4, task: 'Email digest preparation', status: 'Waiting', time: '5:00 PM' },
  ];

  return (
    <div className="space-y-8">
      <AdaptiveSystemDashboard />
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Activity Overview */}
        <Card className="p-6 md:col-span-2">
          <h2 className="text-xl font-bold mb-4">Activity Overview</h2>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={activityData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#3f51b5" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </Card>

        {/* Task Distribution */}
        <Card className="p-6">
          <h2 className="text-xl font-bold mb-4">Task Distribution</h2>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={80}
                  fill="#8884d8"
                  paddingAngle={5}
                  dataKey="value"
                  label={({name, percent}) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </Card>

        {/* Recent Events */}
        <Card className="p-6 md:col-span-2">
          <h2 className="text-xl font-bold mb-4">Recent Events</h2>
          <div className="space-y-4">
            {recentEvents.map((event) => (
              <div key={event.id} className="flex items-start border-b pb-3">
                <div className="bg-primary/10 p-2 rounded-full text-primary text-xs font-bold mr-3">
                  {event.source.substring(0, 2).toUpperCase()}
                </div>
                <div className="flex-grow">
                  <p className="font-medium">{event.event}</p>
                  <div className="flex justify-between text-sm text-muted-foreground mt-1">
                    <span>{event.source}</span>
                    <span>{event.time}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Automated Tasks */}
        <Card className="p-6">
          <h2 className="text-xl font-bold mb-4">Automated Tasks</h2>
          <div className="space-y-4">
            {automatedTasks.map((task) => (
              <div key={task.id} className="border-b pb-3">
                <p className="font-medium">{task.task}</p>
                <div className="flex justify-between text-sm mt-1">
                  <span className={
                    task.status === 'Completed' ? 'text-green-500' : 
                    task.status === 'Running' ? 'text-blue-500' :
                    task.status === 'Waiting' ? 'text-amber-500' : 'text-gray-500'
                  }>
                    {task.status}
                  </span>
                  <span className="text-muted-foreground">{task.time}</span>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
