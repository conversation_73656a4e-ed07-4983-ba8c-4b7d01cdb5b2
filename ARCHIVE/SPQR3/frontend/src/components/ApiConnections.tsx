import React, { useState } from 'react';
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Brain, AlertTriangle } from 'lucide-react';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue,
  
} from "@/components/ui/select";
import { ApiConnection } from '@/types/api';
import { useApiIntelligence } from '@/contexts/ApiIntelligenceContext';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

const ApiConnections = () => {
  const [connections, setConnections] = useState<ApiConnection[]>([]);
  const [name, setName] = useState('');
  const [apiKey, setApiKey] = useState('');
  const [apiType, setApiType] = useState('');
  const [endpoint, setEndpoint] = useState('');
  const { connectionInsights, recordApiResponse } = useApiIntelligence();

  const addConnection = () => {
    if (!name.trim() || !apiKey.trim() || !apiType || !endpoint.trim()) {
      toast.error("All fields are required");
      return;
    }

    const newConnection: ApiConnection = {
      id: Date.now().toString(),
      name,
      apiKey: apiKey,
      type: apiType,
      endpoint,
      status: 'connected',
      lastSync: new Date().toISOString(),
    };

    setConnections([...connections, newConnection]);
    
    // Reset form
    setName('');
    setApiKey('');
    setApiType('');
    setEndpoint('');
    
    toast.success(`${name} API connection added successfully`);
  };

  const testConnection = async (id: string) => {
    const connection = connections.find(conn => conn.id === id);
    if (!connection) return;

    toast.info("Testing connection...");
    const startTime = Date.now();

    try {
      const response = await fetch(connection.endpoint, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${connection.apiKey}`,
        },
      });

      const responseTime = Date.now() - startTime;
      const success = response.ok;

      recordApiResponse(
        id, 
        success, 
        responseTime, 
        success ? undefined : `HTTP ${response.status}`
      );

      if (success) {
        toast.success("Connection test successful");
      } else {
        toast.error(`Connection test failed: ${response.statusText}`);
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      recordApiResponse(id, false, responseTime, error instanceof Error ? error.message : 'Unknown error');
      toast.error("Connection test failed");
    }
  };

  const syncData = (id: string) => {
    toast.info("Syncing data...");
    // In a real app, this would trigger a data sync process
    setTimeout(() => {
      const updatedConnections = connections.map(conn => 
        conn.id === id ? {...conn, lastSync: new Date().toISOString()} : conn
      );
      setConnections(updatedConnections);
      toast.success("Data synchronized successfully");
    }, 2000);
  };

  const connectionTypes = [
    { value: 'email', label: 'Email (SMTP/IMAP)' },
    { value: 'slack', label: 'Slack' },
    { value: 'salesforce', label: 'Salesforce' },
    { value: 'zendesk', label: 'Zendesk' },
    { value: 'hubspot', label: 'HubSpot' },
    { value: 'jira', label: 'Jira' },
    { value: 'github', label: 'GitHub' },
    { value: 'custom', label: 'Custom REST API' },
  ];

  const renderConnectionInsights = (connection: ApiConnection) => {
    const insights = connectionInsights[connection.id];
    if (!insights) return null;

    return (
      <div className="mt-2 text-xs text-muted-foreground">
        <div className="flex items-center gap-2">
          <Brain className="h-3 w-3" />
          <span>Response Time: {insights.responseTime.toFixed(0)}ms</span>
          <span>•</span>
          <span>Success Rate: {(insights.successRate * 100).toFixed(1)}%</span>
          {insights.errorPatterns.length > 0 && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <AlertTriangle className="h-3 w-3 text-yellow-500" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Recent errors:</p>
                  <ul className="list-disc pl-4">
                    {insights.errorPatterns.map((error, i) => (
                      <li key={i}>{error}</li>
                    ))}
                  </ul>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <h2 className="text-2xl font-bold mb-6">API Connections</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium mb-1">Connection Name</label>
            <Input
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="e.g., Slack Workspace"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">API Type</label>
            <Select value={apiType} onValueChange={setApiType}>
              <SelectTrigger>
                <SelectValue placeholder="Select API type" />
              </SelectTrigger>
              <SelectContent>
                {connectionTypes.map(type => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">API Key / Token</label>
            <Input
              type="password" 
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="Enter API key or token"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">API Endpoint</label>
            <Input
              value={endpoint}
              onChange={(e) => setEndpoint(e.target.value)}
              placeholder="https://api.example.com/v1"
            />
          </div>
        </div>
        <Button onClick={addConnection} className="w-full md:w-auto">Add Connection</Button>
      </Card>

      <Card className="p-6">
        <h3 className="text-xl font-semibold mb-4">Connected APIs</h3>
        <ScrollArea className="h-[300px]">
          {connections.length === 0 ? (
            <p className="text-muted-foreground text-center py-8">
              No API connections added yet. Connect your first API above.
            </p>
          ) : (
            <div className="space-y-4">
              {connections.map((conn) => (
                <div key={conn.id} className="p-4 border rounded-lg">
                  <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-2">
                    <div>
                      <h4 className="font-medium">{conn.name}</h4>
                      <p className="text-sm text-muted-foreground">{conn.type} • {conn.endpoint}</p>
                    </div>
                    <div className="flex mt-2 md:mt-0">
                      <Button 
                        variant="secondary" 
                        size="sm" 
                        onClick={() => testConnection(conn.id)}
                        className="mr-2"
                      >
                        Test
                      </Button>
                      <Button 
                        size="sm" 
                        onClick={() => syncData(conn.id)}
                      >
                        Sync Now
                      </Button>
                    </div>
                  </div>
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Status: <span className="text-green-500 font-medium">{conn.status}</span></span>
                    <span>Last sync: {new Date(conn.lastSync).toLocaleString()}</span>
                  </div>
                  {renderConnectionInsights(conn)}
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </Card>
    </div>
  );
};

export default ApiConnections;
