
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import AutomatedTasks from "./pages/AutomatedTasks";
import EventNotifications from "./pages/EventNotifications";
import UserAnalytics from "./pages/UserAnalytics";
import Settings from "./pages/Settings";
import Knowledge from "./pages/Knowledge";
import Account from "./pages/Account";
import { AdaptiveSystemProvider } from "./contexts/AdaptiveSystemContext";
import { ApiIntelligenceProvider } from "./contexts/ApiIntelligenceContext";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AdaptiveSystemProvider>
      <ApiIntelligenceProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/tasks" element={<AutomatedTasks />} />
              <Route path="/notifications" element={<EventNotifications />} />
              <Route path="/analytics" element={<UserAnalytics />} />
              <Route path="/settings" element={<Settings />} />
              <Route path="/knowledge" element={<Knowledge />} />
              <Route path="/account" element={<Account />} />
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </ApiIntelligenceProvider>
    </AdaptiveSystemProvider>
  </QueryClientProvider>
);

export default App;
