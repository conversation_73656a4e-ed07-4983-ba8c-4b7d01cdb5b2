# SPQR3 Installationsanleitung

Diese Anleitung führt Sie durch den Prozess der Installation und Einrichtung des SPQR3-Systems auf einem Ubuntu-Server.

## Voraussetzungen

Stellen Si<PERSON> sicher, dass Ihr Server die folgenden Voraussetzungen erfüllt:

- Ubuntu 20.04 LTS oder höher
- Mindestens 4 GB RAM
- Mindestens 20 GB freier Festplattenspeicher
- Docker und Docker Compose installiert
- Git installiert
- Internetzugang für das Herunterladen von Abhängigkeiten und Docker-Images

## 1. Docker und Docker Compose installieren

Falls Docker und Docker Compose noch nicht installiert sind, können Sie sie mit den folgenden Befehlen installieren:

```bash
# Docker installieren
sudo apt update
sudo apt install -y apt-transport-https ca-certificates curl software-properties-common
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -
sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"
sudo apt update
sudo apt install -y docker-ce

# Docker ohne sudo ausführen (optional)
sudo usermod -aG docker ${USER}
su - ${USER}

# Docker Compose installieren
sudo curl -L "https://github.com/docker/compose/releases/download/v2.18.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

## 2. SPQR3 herunterladen

Klonen Sie das SPQR3-Repository auf Ihren Server:

```bash
git clone https://github.com/yourusername/spqr3.git
cd spqr3
```

## 3. Umgebungsvariablen konfigurieren

Kopieren Sie die Beispiel-Umgebungsvariablendatei und passen Sie sie an Ihre Bedürfnisse an:

```bash
cp .env.example .env
nano .env
```

Konfigurieren Sie mindestens die folgenden Variablen:

- `POSTGRES_PASSWORD`: Ein sicheres Passwort für die PostgreSQL-Datenbank
- `JWT_SECRET`: Ein sicherer Schlüssel für die JWT-Authentifizierung
- `CLAUDE_API_KEY`: Ihr Claude API-Schlüssel (falls verwendet)
- `OPENAI_API_KEY`: Ihr OpenAI API-Schlüssel (falls verwendet)

## 4. Frontend bauen

Bauen Sie das Frontend mit dem bereitgestellten Skript:

```bash
./build-frontend.sh
```

## 5. System starten

Starten Sie das SPQR3-System mit Docker Compose:

```bash
docker compose up -d
```

Dieser Befehl startet alle erforderlichen Dienste im Hintergrund.

## 6. Systemstatus überprüfen

Überprüfen Sie, ob alle Dienste erfolgreich gestartet wurden:

```bash
docker compose ps
```

Alle Dienste sollten den Status "Up" haben.

## 7. Auf die Anwendung zugreifen

Sie können nun auf die SPQR3-Anwendung über einen Webbrowser zugreifen:

```
http://your-server-ip:3000
```

Ersetzen Sie `your-server-ip` durch die IP-Adresse Ihres Servers.

## 8. Systemprotokolle anzeigen

Um die Protokolle aller Dienste anzuzeigen:

```bash
docker compose logs
```

Um die Protokolle eines bestimmten Dienstes anzuzeigen:

```bash
docker compose logs [service-name]
```

Ersetzen Sie `[service-name]` durch den Namen des Dienstes, z.B. `spqr-core`, `frontend`, usw.

## 9. System stoppen

Um das SPQR3-System zu stoppen:

```bash
docker compose down
```

Um das System zu stoppen und alle Daten zu löschen (Vorsicht!):

```bash
docker compose down -v
```

## 10. System aktualisieren

Um das SPQR3-System auf die neueste Version zu aktualisieren:

```bash
git pull
./build-frontend.sh
docker compose down
docker compose up -d
```

## Fehlerbehebung

### Dienste starten nicht

Überprüfen Sie die Protokolle des betroffenen Dienstes:

```bash
docker compose logs [service-name]
```

### Datenbank-Verbindungsprobleme

Stellen Sie sicher, dass die Datenbank-Umgebungsvariablen korrekt konfiguriert sind und dass der PostgreSQL-Dienst läuft:

```bash
docker compose ps postgres
docker compose logs postgres
```

### Frontend-Verbindungsprobleme

Stellen Sie sicher, dass die API-Basis-URL im Frontend korrekt konfiguriert ist und dass der Backend-Dienst läuft:

```bash
docker compose ps spqr-core
docker compose logs spqr-core
```

### LLM-Verbindungsprobleme

Stellen Sie sicher, dass die LLM-API-Schlüssel korrekt konfiguriert sind und dass der LLM-Dienst läuft:

```bash
docker compose logs spqr-core
```

## Weitere Ressourcen

- [SPQR3 Dokumentation](./cline_docs/)
- [SPQR3 README](./README.md)
- [Docker Dokumentation](https://docs.docker.com/)
- [Docker Compose Dokumentation](https://docs.docker.com/compose/)
