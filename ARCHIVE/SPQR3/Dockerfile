# Builder Stage
FROM node:20-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
# No build step in this project, but if there was, it would go here, e.g. RUN npm run build

# Runner Stage
FROM node:20-alpine AS runner
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/src ./src
COPY --from=builder /app/public ./public
COPY --from=builder /app/server.js ./

# Umgebungsvariablen für die Verschlüsselung
ENV ENCRYPTION_KEY=spqr_secure_encryption_key_123

# Umgebungsvariablen für den Onboarding-Prozess
ENV ONBOARDING_ENABLED=true

CMD ["node", "server.js"]
