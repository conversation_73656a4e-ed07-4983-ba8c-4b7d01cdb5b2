/**
 * OAuth-Routen für SPQR
 * 
 * Diese Routen ermöglichen die Interaktion mit dem OAuth-Authentifizierungsprozess.
 */

const express = require('express');
const router = express.Router();

module.exports = (spqr) => {
  const oauthManager = spqr.oauthManager;
  
  /**
   * GET /api/oauth/providers
   * Gibt eine Liste aller verfügbaren OAuth-Provider zurück
   */
  router.get('/providers', async (req, res) => {
    try {
      const providers = Object.values(oauthManager.providers).map(provider => ({
        id: provider.id,
        name: provider.name,
        description: provider.description,
        isConfigured: !!provider.clientId && !!provider.clientSecret
      }));
      
      res.json({ providers });
    } catch (error) {
      console.error('Fehler beim Abrufen der OAuth-Provider:', error);
      res.status(500).json({ error: 'Interner Serverfehler' });
    }
  });
  
  /**
   * GET /api/oauth/auth/:providerId
   * Startet den OAuth-Authentifizierungsprozess für einen Provider
   */
  router.get('/auth/:providerId', async (req, res) => {
    try {
      const { providerId } = req.params;
      const { scope } = req.query;
      
      const options = {};
      if (scope) {
        options.scope = scope;
      }
      
      const authInfo = await oauthManager.startAuthFlow(providerId, options);
      
      res.json(authInfo);
    } catch (error) {
      console.error(`Fehler beim Starten des OAuth-Flows für ${req.params.providerId}:`, error);
      res.status(500).json({ error: 'Interner Serverfehler' });
    }
  });
  
  /**
   * GET /api/oauth/callback/:providerId
   * Verarbeitet den OAuth-Callback für einen Provider
   */
  router.get('/callback/:providerId', async (req, res) => {
    try {
      const { code, state } = req.query;
      
      if (!code || !state) {
        return res.status(400).json({ error: 'Ungültige Anfrage' });
      }
      
      const result = await oauthManager.handleCallback(code, state);
      
      // Leite zur Erfolgsseite weiter
      res.redirect('/oauth/success');
    } catch (error) {
      console.error('Fehler bei der Verarbeitung des OAuth-Callbacks:', error);
      
      // Leite zur Fehlerseite weiter
      res.redirect('/oauth/error');
    }
  });
  
  /**
   * GET /api/oauth/status/:providerId
   * Gibt den Authentifizierungsstatus für einen Provider zurück
   */
  router.get('/status/:providerId', async (req, res) => {
    try {
      const { providerId } = req.params;
      
      const hasToken = await oauthManager.hasValidToken(providerId);
      
      res.json({
        providerId,
        isAuthenticated: hasToken
      });
    } catch (error) {
      console.error(`Fehler beim Abrufen des OAuth-Status für ${req.params.providerId}:`, error);
      res.status(500).json({ error: 'Interner Serverfehler' });
    }
  });
  
  /**
   * DELETE /api/oauth/token/:providerId
   * Widerruft ein Token für einen Provider
   */
  router.delete('/token/:providerId', async (req, res) => {
    try {
      const { providerId } = req.params;
      
      const success = await oauthManager.revokeToken(providerId);
      
      if (success) {
        res.json({ success: true, message: `Token für ${providerId} widerrufen` });
      } else {
        res.status(500).json({ success: false, message: `Token für ${providerId} konnte nicht widerrufen werden` });
      }
    } catch (error) {
      console.error(`Fehler beim Widerrufen des OAuth-Tokens für ${req.params.providerId}:`, error);
      res.status(500).json({ error: 'Interner Serverfehler' });
    }
  });
  
  return router;
};
