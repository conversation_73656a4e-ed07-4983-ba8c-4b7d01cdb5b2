/**
 * Onboarding-Routen für SPQR
 *
 * Diese Routen ermöglichen die Interaktion mit dem Onboarding-Prozess.
 */

const express = require('express');
const router = express.Router();

module.exports = (spqr) => {
  const onboardingManager = spqr.onboardingManager;

  /**
   * GET /api/onboarding/status
   * Gibt den aktuellen Onboarding-Status zurück
   */
  router.get('/status', async (req, res) => {
    try {
      const status = onboardingManager.getOnboardingStatus();
      res.json(status);
    } catch (error) {
      console.error('Fehler beim Abrufen des Onboarding-Status:', error);
      res.status(500).json({ error: 'Interner Serverfehler' });
    }
  });

  /**
   * GET /api/onboarding/dialog/:serviceId
   * Generiert einen KI-gestützten Dialog für einen Dienst
   */
  router.get('/dialog/:serviceId', async (req, res) => {
    try {
      const { serviceId } = req.params;
      const dialog = await onboardingManager.generateOnboardingDialog(serviceId);
      res.json(dialog);
    } catch (error) {
      console.error(`Fehler beim Generieren des Dialogs für ${req.params.serviceId}:`, error);
      res.status(500).json({ error: 'Interner Serverfehler' });
    }
  });

  /**
   * POST /api/onboarding/config/:serviceId
   * Speichert die Konfiguration eines Dienstes
   */
  router.post('/config/:serviceId', async (req, res) => {
    try {
      const { serviceId } = req.params;
      const config = req.body;

      const result = await onboardingManager.saveServiceConfig(serviceId, config);

      if (result.success) {
        res.json(result);
      } else {
        res.status(400).json(result);
      }
    } catch (error) {
      console.error(`Fehler beim Speichern der Konfiguration für ${req.params.serviceId}:`, error);
      res.status(500).json({ error: 'Interner Serverfehler' });
    }
  });

  /**
   * POST /api/onboarding/detect-email-settings
   * Erkennt automatisch die E-Mail-Einstellungen basierend auf der E-Mail-Adresse
   */
  router.post('/detect-email-settings', async (req, res) => {
    try {
      const { email } = req.body;

      if (!email) {
        return res.status(400).json({ error: 'E-Mail-Adresse ist erforderlich' });
      }

      // Verwende den E-Mail-Agent, um die Einstellungen zu erkennen
      const emailAgent = spqr.getAgent('email');
      if (!emailAgent) {
        return res.status(500).json({ error: 'E-Mail-Agent nicht verfügbar' });
      }

      const settings = await emailAgent.detectEmailSettings(email);
      res.json(settings);
    } catch (error) {
      console.error('Fehler bei der E-Mail-Einstellungserkennung:', error);
      res.status(500).json({ error: 'Interner Serverfehler' });
    }
  });

  /**
   * POST /api/onboarding/test-email-connection
   * Testet die Verbindung zu einem E-Mail-Server
   */
  router.post('/test-email-connection', async (req, res) => {
    try {
      const config = req.body;

      if (!config.user || !config.password || !config.imapHost || !config.smtpHost) {
        return res.status(400).json({ error: 'Unvollständige Konfiguration' });
      }

      // Verwende den E-Mail-Agent, um die Verbindung zu testen
      const emailAgent = spqr.getAgent('email');
      if (!emailAgent) {
        return res.status(500).json({ error: 'E-Mail-Agent nicht verfügbar' });
      }

      const result = await emailAgent.testEmailConnection(config);
      res.json(result);
    } catch (error) {
      console.error('Fehler beim E-Mail-Verbindungstest:', error);
      res.status(500).json({ error: 'Interner Serverfehler' });
    }
  });

  /**
   * GET /api/onboarding/config/:serviceId
   * Gibt die Konfiguration eines Dienstes zurück
   */
  router.get('/config/:serviceId', async (req, res) => {
    try {
      const { serviceId } = req.params;
      const config = await onboardingManager.getServiceConfig(serviceId);
      res.json(config);
    } catch (error) {
      console.error(`Fehler beim Abrufen der Konfiguration für ${req.params.serviceId}:`, error);
      res.status(500).json({ error: 'Interner Serverfehler' });
    }
  });

  /**
   * PUT /api/onboarding/step/:stepId
   * Setzt den aktuellen Onboarding-Schritt
   */
  router.put('/step/:stepId', async (req, res) => {
    try {
      const { stepId } = req.params;
      const result = await onboardingManager.setCurrentStep(stepId);

      if (result.success) {
        res.json(result);
      } else {
        res.status(400).json(result);
      }
    } catch (error) {
      console.error(`Fehler beim Setzen des Schritts ${req.params.stepId}:`, error);
      res.status(500).json({ error: 'Interner Serverfehler' });
    }
  });

  /**
   * POST /api/onboarding/complete/:stepId
   * Markiert einen Schritt als abgeschlossen
   */
  router.post('/complete/:stepId', async (req, res) => {
    try {
      const { stepId } = req.params;
      const result = await onboardingManager.completeStep(stepId);

      if (result.success) {
        res.json(result);
      } else {
        res.status(400).json(result);
      }
    } catch (error) {
      console.error(`Fehler beim Abschließen des Schritts ${req.params.stepId}:`, error);
      res.status(500).json({ error: 'Interner Serverfehler' });
    }
  });

  return router;
};
