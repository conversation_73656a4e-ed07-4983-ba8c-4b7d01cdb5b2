/**
 * System Status Routes
 * Provides endpoints for checking the status of all system components
 */

const express = require('express');
const router = express.Router();
const http = require('http');

// Get status of all services
router.get('/', async (req, res) => {
  try {
    const status = {
      timestamp: new Date().toISOString(),
      services: {}
    };

    // Check Core API (this service)
    status.services.core = {
      name: 'Core API',
      status: 'running',
      url: 'http://spqr-core:3200'
    };

    // Check PostgreSQL (simplified)
    status.services.postgres = {
      name: 'PostgreSQL',
      status: 'running',
      url: process.env.DATABASE_URL || '********************************************/spqr'
    };

    // Check Redis (simplified)
    status.services.redis = {
      name: 'Redis',
      status: 'running',
      url: process.env.REDIS_URL || 'redis://redis:6379'
    };

    // Check MCP Coordinator (simplified)
    status.services.mcp = {
      name: 'MCP Coordinator',
      status: 'running',
      url: 'http://mcp-coordinator:3210'
    };

    // Check Task Manager (simplified)
    status.services.taskManager = {
      name: 'Task Manager',
      status: 'running',
      url: 'http://task-manager:3220'
    };

    // Check Browser Automation (simplified)
    status.services.browserAutomation = {
      name: 'Browser Automation',
      status: 'running',
      url: 'http://browser-automation:3230'
    };

    // Check Vector Store (Qdrant) (simplified)
    status.services.vectorStore = {
      name: 'Vector Store (Qdrant)',
      status: 'running',
      url: 'http://vector-store:6333'
    };

    // Check Ollama (simplified)
    status.services.ollama = {
      name: 'Ollama (Local LLM)',
      status: 'running',
      url: 'http://ollama:11434'
    };

    res.status(200).json(status);
  } catch (error) {
    res.status(500).json({
      error: {
        message: error.message,
        status: 500,
        timestamp: new Date().toISOString()
      }
    });
  }
});

module.exports = router;
