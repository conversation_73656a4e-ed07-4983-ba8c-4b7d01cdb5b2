const express = require('express');
const router = express.Router();
const { createSPQR2System } = require('../spqr2-system');

// Initialize SPQR2 system (assuming it can be initialized once and reused)
// In a real application, this might be managed differently,
// perhaps initialized in app.js and passed to the routes.
// For this fix, we'll initialize it here for simplicity.
let spqr2System;

async function initializeSystem() {
  if (!spqr2System) {
    spqr2System = createSPQR2System({});
    await spqr2System.initialize();
    console.log('SPQR2 System initialized in ai.js'); // Log for debugging
  }
}

// Initialize the system when this module is loaded
initializeSystem().catch(console.error);

// Route to handle LLM generate requests
router.post('/llm/generate', async (req, res) => {
  try {
    if (!spqr2System) {
      // Attempt to re-initialize if not already
      await initializeSystem();
      if (!spqr2System) {
         throw new Error("SPQR2 System failed to initialize.");
      }
    }

    const { prompt, taskType, userId, userRole, sessionId, maxTokens, temperature, context } = req.body;

    // Call the SPQR2 system's processMessage function
    // The structure of the task object might need adjustment based on
    // what spqr2System.processMessage expects.
    const task = {
      type: taskType || 'chat', // Default to chat task type
      prompt: prompt,
      userId: userId,
      userRole: userRole,
      sessionId: sessionId,
      maxTokens: maxTokens,
      temperature: temperature,
      context: context
      // Add other relevant fields expected by processMessage
    };

    // Assuming processMessage handles the LLM interaction and returns a result object
    const result = await spqr2System.processMessage(task);

    // The structure of the result object needs to match what the frontend expects (GenerateResponseData)
    // Based on frontend/src/services/chatService.ts:
    // id: string; content: string; taskType: string; model: string; provider: string; tokenUsage: { prompt: number; completion: number; total: number; }; processingTime: number;
    // The result from spqr2System.processMessage might need mapping.
    // For now, let's assume result has a 'content' field and we can mock other fields.
    const responseData = {
        id: result.id || 'generated-id-' + Date.now(), // Assuming result has an id or generate one
        content: result.content || 'Error: Could not get content from SPQR2 system.', // Assuming result has content
        taskType: result.taskType || task.type,
        model: result.model || 'unknown',
        provider: result.provider || 'unknown',
        tokenUsage: result.tokenUsage || { prompt: 0, completion: 0, total: 0 },
        processingTime: result.processingTime || 0
    };


    res.json(responseData);
  } catch (error) {
    console.error('Error processing /llm/generate:', error);
    // Send an error response that the frontend can handle
    res.status(500).json({
      id: 'error-' + Date.now(),
      content: 'An error occurred while generating the response.', // Generic error message
      taskType: req.body.taskType || 'chat',
      model: 'error',
      provider: 'error',
      tokenUsage: { prompt: 0, completion: 0, total: 0 },
      processingTime: 0
    });
  }
});

// TODO: Implement /chat/sessions routes (GET, POST, DELETE, PATCH)
// These routes would interact with the Memory System component of SPQR2 if available,
// or potentially manage sessions in a database directly if the Memory System
// is not exposed via spqr2-system.js in v0.9.
// For now, we'll add placeholder routes that return mock data or errors.

router.get('/chat/sessions/:sessionId', (req, res) => {
    console.warn('/chat/sessions/:sessionId endpoint not fully implemented in ai.js');
    // Return mock session data or an error
    res.status(501).json({ message: 'GET /chat/sessions/:sessionId not implemented' });
});

router.post('/chat/sessions', (req, res) => {
    console.warn('/chat/sessions endpoint not fully implemented in ai.js');
     // Return mock session data or an error
    res.status(501).json({ message: 'POST /chat/sessions not implemented' });
});

router.delete('/chat/sessions/:sessionId', (req, res) => {
    console.warn('DELETE /chat/sessions/:sessionId endpoint not fully implemented in ai.js');
     // Return success or error
    res.status(501).json({ message: 'DELETE /chat/sessions/:sessionId not implemented' });
});

router.patch('/chat/sessions/:sessionId', (req, res) => {
    console.warn('PATCH /chat/sessions/:sessionId endpoint not fully implemented in ai.js');
     // Return updated session or error
    res.status(501).json({ message: 'PATCH /chat/sessions/:sessionId not implemented' });
});


module.exports = router;
