/**
 * Prozess-Routen für SPQR
 * 
 * Diese Routen ermöglichen die Interaktion mit dem Prozess-Builder.
 */

const express = require('express');
const router = express.Router();

module.exports = (spqr) => {
  const processBuilder = spqr.processBuilder;
  
  /**
   * GET /api/processes
   * Gibt eine Liste aller Prozesse zurück
   */
  router.get('/', async (req, res) => {
    try {
      const processes = await processBuilder.getProcesses();
      res.json(processes);
    } catch (error) {
      console.error('Fehler beim Abrufen der Prozesse:', error);
      res.status(500).json({ error: 'Interner Serverfehler' });
    }
  });
  
  /**
   * GET /api/processes/:id
   * Gibt einen Prozess zurück
   */
  router.get('/:id', async (req, res) => {
    try {
      const { id } = req.params;
      const process = await processBuilder.getProcess(id);
      
      if (!process) {
        return res.status(404).json({ error: 'Prozess nicht gefunden' });
      }
      
      res.json(process);
    } catch (error) {
      console.error(`Fehler beim Abrufen des Prozesses ${req.params.id}:`, error);
      res.status(500).json({ error: 'Interner Serverfehler' });
    }
  });
  
  /**
   * POST /api/processes
   * Erstellt einen neuen Prozess
   */
  router.post('/', async (req, res) => {
    try {
      const { name, description } = req.body;
      
      const process = await processBuilder.createProcess({
        name,
        description
      });
      
      res.status(201).json(process);
    } catch (error) {
      console.error('Fehler beim Erstellen des Prozesses:', error);
      res.status(500).json({ error: 'Interner Serverfehler' });
    }
  });
  
  /**
   * PUT /api/processes/:id
   * Aktualisiert einen Prozess
   */
  router.put('/:id', async (req, res) => {
    try {
      const { id } = req.params;
      const processData = req.body;
      
      const process = await processBuilder.updateProcess(id, processData);
      
      if (!process) {
        return res.status(404).json({ error: 'Prozess nicht gefunden' });
      }
      
      res.json(process);
    } catch (error) {
      console.error(`Fehler beim Aktualisieren des Prozesses ${req.params.id}:`, error);
      res.status(500).json({ error: 'Interner Serverfehler' });
    }
  });
  
  /**
   * DELETE /api/processes/:id
   * Löscht einen Prozess
   */
  router.delete('/:id', async (req, res) => {
    try {
      const { id } = req.params;
      
      const success = await processBuilder.deleteProcess(id);
      
      if (!success) {
        return res.status(404).json({ error: 'Prozess nicht gefunden' });
      }
      
      res.status(204).end();
    } catch (error) {
      console.error(`Fehler beim Löschen des Prozesses ${req.params.id}:`, error);
      res.status(500).json({ error: 'Interner Serverfehler' });
    }
  });
  
  /**
   * POST /api/processes/:id/execute
   * Führt einen Prozess aus
   */
  router.post('/:id/execute', async (req, res) => {
    try {
      const { id } = req.params;
      const { input } = req.body || {};
      
      const result = await processBuilder.executeProcess(id, input);
      
      res.json(result);
    } catch (error) {
      console.error(`Fehler beim Ausführen des Prozesses ${req.params.id}:`, error);
      res.status(500).json({ error: 'Interner Serverfehler' });
    }
  });
  
  /**
   * POST /api/processes/duplicate
   * Dupliziert einen Prozess
   */
  router.post('/duplicate', async (req, res) => {
    try {
      const { id } = req.body;
      
      const process = await processBuilder.duplicateProcess(id);
      
      if (!process) {
        return res.status(404).json({ error: 'Prozess nicht gefunden' });
      }
      
      res.status(201).json(process);
    } catch (error) {
      console.error(`Fehler beim Duplizieren des Prozesses:`, error);
      res.status(500).json({ error: 'Interner Serverfehler' });
    }
  });
  
  return router;
};
