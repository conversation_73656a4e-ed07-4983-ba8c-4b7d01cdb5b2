# SPQR2 Environment Configuration v0.9.4

# Server Configuration
NODE_ENV=production

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRATION=1d

# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=spqr2
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
DATABASE_URL=********************************************/spqr2

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379

# Vector Store Configuration
VECTOR_STORE_HOST=vector-store
VECTOR_STORE_PORT=6333

# Ollama Configuration
OLLAMA_HOST=ollama
OLLAMA_PORT=11434
OLLAMA_DEFAULT_MODEL=llama3

# Port Configuration
BACKEND_PORT=3200
MCP_PORT=3210
TASK_MANAGER_PORT=3220
BROWSER_AUTOMATION_PORT=3230
FRONTEND_PORT=80

# OpenAI Configuration (optional)
# OPENAI_API_KEY=your_openai_api_key
# OPENAI_EMBEDDING_MODEL=text-embedding-ada-002
# OPENAI_COMPLETION_MODEL=gpt-4-turbo-preview

# Claude API Configuration (optional)
# CLAUDE_API_KEY=your_claude_api_key
# CLAUDE_MODEL=claude-3-opus-20240229

# LLM Configuration
DEFAULT_LLM_PROVIDER=ollama
MULTI_LLM_ENABLED=true
LOCAL_LLM_ENABLED=true

# Logging
LOG_LEVEL=info

# System Configuration
DIRECT_WEBHOOKS_ENABLED=true
WEBHOOK_RATE_LIMIT_MAX=100
WEBHOOK_RATE_LIMIT_WINDOW_MS=60000
