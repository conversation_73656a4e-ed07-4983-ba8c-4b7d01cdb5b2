version: "3.8"

services:
  # PostgreSQL Database with pgvector extension
  postgres:
    image: pgvector/pgvector:pg14
    container_name: spqr-postgres
    restart: unless-stopped
    ports:
      - "5432:5432"
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
      - ./sql:/docker-entrypoint-initdb.d
    environment:
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_DB=spqr2
    networks:
      - spqr-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  # Redis for token tracking caching and task management
  redis:
    image: redis:alpine
    container_name: spqr-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - ./data/redis:/data
    command: redis-server --appendonly yes
    networks:
      - spqr-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  # Ollama for Local LLMs
  ollama:
    image: ollama/ollama
    container_name: spqr-ollama
    ports:
      - "11434:11434"
    volumes:
      - ./data/ollama:/root/.ollama
    networks:
      - spqr-network
    healthcheck:
      test: ["CMD-SHELL", "wget -q --spider http://localhost:11434/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # SPQR Core API Service
  spqr-core:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: spqr-core
    restart: unless-stopped
    ports:
      - "3200:3200"
    environment:
      - NODE_ENV=production
      - PORT=3200
      - DATABASE_URL=********************************************/spqr2
      - JWT_SECRET=
      - JWT_EXPIRATION=1d
      # Direct Webhook Configuration
      - DIRECT_WEBHOOKS_ENABLED=true
      - WEBHOOK_RATE_LIMIT_MAX=100
      - WEBHOOK_RATE_LIMIT_WINDOW_MS=60000
      - LOG_LEVEL=info
      - VECTOR_STORE_URL=http://vector-store:6333
      # Redis Configuration
      - REDIS_URL=redis://redis:6379
      # LLM Provider Configuration
      - DEFAULT_LLM_PROVIDER=local-llm
      - MULTI_LLM_ENABLED=true
      # Claude AI
      - CLAUDE_API_KEY=************************************************************************************************************
      - CLAUDE_MODEL=claude-3-opus-20240229
      # OpenAI
      - OPENAI_API_KEY=********************************************************************************************************************************************************************
      - OPENAI_MODEL=gpt-4-turbo-preview
      - OPENAI_EMBEDDING_MODEL=text-embedding-ada-002
      # Local LLM if enabled (Ollama)
      - LOCAL_LLM_ENABLED=true
      - LOCAL_AI_BASE_URL=http://ollama:11434/api
      - LOCAL_AI_MODEL=llama3
      - LOCAL_LLM_TYPE=ollama
      # Ollama adapter configuration
      - OLLAMA_BASE_URL=http://ollama:11434
      - OLLAMA_DEFAULT_MODEL=llama3
    depends_on:
      - redis
      - postgres
      - ollama
    networks:
      - spqr-network
    healthcheck:
      test: ["CMD-SHELL", "wget -q --spider http://localhost:3200/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MCP Coordinator Service
  mcp-coordinator:
    build:
      context: ./mcp-layer
      dockerfile: Dockerfile
    container_name: spqr-mcp-coordinator
    restart: unless-stopped
    ports:
      - "3210:3210"
    environment:
      - NODE_ENV=production
      - PORT=3210
      - CORE_API_URL=http://spqr-core:3200
      - DATABASE_URL=********************************************/spqr2
      - LOG_LEVEL=info
      - REDIS_URL=redis://redis:6379
      # Claude AI
      - CLAUDE_API_KEY=************************************************************************************************************
      - CLAUDE_MODEL=claude-3-opus-20240229
      # OpenAI
      - OPENAI_API_KEY=********************************************************************************************************************************************************************
      - OPENAI_MODEL=gpt-4-turbo-preview
      # Local LLM if enabled (Ollama)
      - LOCAL_LLM_ENABLED=true
      - LOCAL_AI_BASE_URL=http://ollama:11434/api
      # Ollama adapter configuration
      - OLLAMA_BASE_URL=http://ollama:11434
      - OLLAMA_DEFAULT_MODEL=llama3
    depends_on:
      - redis
      - postgres
      - ollama
      - spqr-core
    networks:
      - spqr-network
    healthcheck:
      test: ["CMD-SHELL", "wget -q --spider http://localhost:3210/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Task Management Service
  task-manager:
    build:
      context: ./task-management
      dockerfile: Dockerfile
    container_name: spqr-task-manager
    restart: unless-stopped
    ports:
      - "3220:3220"
    environment:
      - NODE_ENV=production
      - PORT=3220
      - DATABASE_URL=********************************************/spqr2
      - LOG_LEVEL=info
      - REDIS_URL=redis://redis:6379
      - MCP_COORDINATOR_URL=http://mcp-coordinator:3210
    depends_on:
      - redis
      - postgres
      - mcp-coordinator
    networks:
      - spqr-network
    healthcheck:
      test: ["CMD-SHELL", "wget -q --spider http://localhost:3220/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Browser Automation Service
  browser-automation:
    build:
      context: ./browser-automation
      dockerfile: Dockerfile
    container_name: spqr-browser-automation
    restart: unless-stopped
    ports:
      - "3230:3230"
    environment:
      - NODE_ENV=production
      - PORT=3230
      - DATABASE_URL=********************************************/spqr2
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=spqr2
      - LOG_LEVEL=info
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - MCP_COORDINATOR_URL=http://mcp-coordinator:3210
      - TASK_MANAGER_URL=http://task-manager:3220
    depends_on:
      - redis
      - postgres
      - mcp-coordinator
      - task-manager
    networks:
      - spqr-network
    healthcheck:
      test: ["CMD-SHELL", "wget -q --spider http://localhost:3230/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Vector Store (Qdrant)
  vector-store:
    image: qdrant/qdrant:latest
    container_name: spqr-vector-store
    restart: unless-stopped
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - ./data/vector-store:/qdrant/storage
    networks:
      - spqr-network
    healthcheck:
      test: ["CMD-SHELL", "wget -q --spider http://localhost:6333/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: spqr-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    environment:
      - REACT_APP_API_URL=http://localhost:3200
      - REACT_APP_MCP_URL=http://localhost:3210
      - REACT_APP_TASK_MANAGER_URL=http://localhost:3220
      - REACT_APP_BROWSER_AUTOMATION_URL=http://localhost:3230
    depends_on:
      - spqr-core
      - mcp-coordinator
      - task-manager
      - browser-automation
    networks:
      - spqr-network

networks:
  spqr-network:
    driver: bridge
