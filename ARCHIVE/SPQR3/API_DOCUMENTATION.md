# SPQR3 API-Dokumentation

Diese Dokumentation beschreibt die verfügbaren API-Endpunkte des SPQR3-Systems.

## Basis-URL

Alle API-Endpunkte sind relativ zur Basis-URL:

```
http://localhost:3200/api
```

## Authentifizierung

Die meisten API-Endpunkte erfordern eine Authentifizierung. Verwenden Sie den folgenden Header in Ihren Anfragen:

```
Authorization: Bearer <token>
```

Ersetzen Sie `<token>` durch Ihr JWT-Token, das Sie über den `/auth/login`-Endpunkt erhalten.

## Endpunkte

### Authentifizierung

#### Login

```
POST /auth/login
```

Authentifiziert einen Benutzer und gibt ein JWT-Token zurück.

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user-123",
    "email": "<EMAIL>",
    "name": "<PERSON>e",
    "role": "admin"
  }
}
```

#### Registrierung

```
POST /auth/register
```

Registriert einen neuen Benutzer.

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "John Doe"
}
```

**Response:**

```json
{
  "id": "user-123",
  "email": "<EMAIL>",
  "name": "John Doe",
  "role": "user",
  "createdAt": "2023-01-01T00:00:00.000Z"
}
```

### LLM

#### Nachricht generieren

```
POST /llm/generate
```

Generiert eine Antwort auf eine Nachricht mit einem LLM.

**Request Body:**

```json
{
  "prompt": "Wie ist das Wetter heute?",
  "systemPrompt": "Du bist ein hilfreicher Assistent.",
  "options": {
    "temperature": 0.7,
    "maxTokens": 1000
  },
  "preferredProvider": "claude",
  "taskType": "general"
}
```

**Response:**

```json
{
  "response": "Ich kann leider nicht in Echtzeit auf das aktuelle Wetter zugreifen. Um das aktuelle Wetter zu erfahren, empfehle ich, einen Wetterdienst oder eine Wetter-App zu nutzen oder einfach aus dem Fenster zu schauen. Kann ich dir mit etwas anderem helfen?",
  "metadata": {
    "provider": "claude",
    "model": "claude-3-opus-20240229",
    "promptTokens": 12,
    "completionTokens": 54,
    "totalTokens": 66,
    "processingTime": 1.23
  }
}
```

### Aufgaben

#### Aufgabe erstellen

```
POST /tasks
```

Erstellt eine neue Aufgabe.

**Request Body:**

```json
{
  "title": "Kundenanfrage bearbeiten",
  "description": "Kundenanfrage zu Produkt XYZ bearbeiten",
  "priority": "high",
  "dueDate": "2023-01-01T00:00:00.000Z",
  "assigneeId": "user-123",
  "tags": ["kundenanfrage", "produkt-xyz"]
}
```

**Response:**

```json
{
  "id": "task-123",
  "title": "Kundenanfrage bearbeiten",
  "description": "Kundenanfrage zu Produkt XYZ bearbeiten",
  "priority": "high",
  "dueDate": "2023-01-01T00:00:00.000Z",
  "assigneeId": "user-123",
  "tags": ["kundenanfrage", "produkt-xyz"],
  "status": "open",
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-01T00:00:00.000Z"
}
```

#### Aufgaben abrufen

```
GET /tasks
```

Ruft alle Aufgaben ab.

**Query Parameter:**

- `status`: Filtert nach Status (z.B. `open`, `in-progress`, `completed`)
- `assigneeId`: Filtert nach Bearbeiter
- `priority`: Filtert nach Priorität (z.B. `low`, `medium`, `high`)
- `page`: Seitennummer für Paginierung
- `limit`: Anzahl der Ergebnisse pro Seite

**Response:**

```json
{
  "tasks": [
    {
      "id": "task-123",
      "title": "Kundenanfrage bearbeiten",
      "description": "Kundenanfrage zu Produkt XYZ bearbeiten",
      "priority": "high",
      "dueDate": "2023-01-01T00:00:00.000Z",
      "assigneeId": "user-123",
      "tags": ["kundenanfrage", "produkt-xyz"],
      "status": "open",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "pages": 10
  }
}
```

#### Aufgabe abrufen

```
GET /tasks/:id
```

Ruft eine bestimmte Aufgabe ab.

**Response:**

```json
{
  "id": "task-123",
  "title": "Kundenanfrage bearbeiten",
  "description": "Kundenanfrage zu Produkt XYZ bearbeiten",
  "priority": "high",
  "dueDate": "2023-01-01T00:00:00.000Z",
  "assigneeId": "user-123",
  "tags": ["kundenanfrage", "produkt-xyz"],
  "status": "open",
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-01T00:00:00.000Z"
}
```

#### Aufgabe aktualisieren

```
PUT /tasks/:id
```

Aktualisiert eine bestimmte Aufgabe.

**Request Body:**

```json
{
  "title": "Kundenanfrage bearbeiten",
  "description": "Kundenanfrage zu Produkt XYZ bearbeiten",
  "priority": "medium",
  "dueDate": "2023-01-02T00:00:00.000Z",
  "assigneeId": "user-456",
  "tags": ["kundenanfrage", "produkt-xyz", "dringend"],
  "status": "in-progress"
}
```

**Response:**

```json
{
  "id": "task-123",
  "title": "Kundenanfrage bearbeiten",
  "description": "Kundenanfrage zu Produkt XYZ bearbeiten",
  "priority": "medium",
  "dueDate": "2023-01-02T00:00:00.000Z",
  "assigneeId": "user-456",
  "tags": ["kundenanfrage", "produkt-xyz", "dringend"],
  "status": "in-progress",
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-01T00:00:00.000Z"
}
```

#### Aufgabe löschen

```
DELETE /tasks/:id
```

Löscht eine bestimmte Aufgabe.

**Response:**

```json
{
  "message": "Aufgabe erfolgreich gelöscht"
}
```

### Chat

#### Chat-Sitzungen abrufen

```
GET /chat/sessions
```

Ruft alle Chat-Sitzungen des aktuellen Benutzers ab.

**Response:**

```json
{
  "sessions": [
    {
      "id": "session-123",
      "title": "Kundenanfrage zu Produkt XYZ",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z",
      "messageCount": 10
    }
  ]
}
```

#### Chat-Sitzung erstellen

```
POST /chat/sessions
```

Erstellt eine neue Chat-Sitzung.

**Request Body:**

```json
{
  "title": "Kundenanfrage zu Produkt XYZ"
}
```

**Response:**

```json
{
  "id": "session-123",
  "title": "Kundenanfrage zu Produkt XYZ",
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-01T00:00:00.000Z",
  "messageCount": 0
}
```

#### Chat-Nachrichten abrufen

```
GET /chat/sessions/:sessionId/messages
```

Ruft alle Nachrichten einer bestimmten Chat-Sitzung ab.

**Response:**

```json
{
  "messages": [
    {
      "id": "message-123",
      "sessionId": "session-123",
      "content": "Hallo, ich habe eine Frage zu Produkt XYZ.",
      "role": "user",
      "createdAt": "2023-01-01T00:00:00.000Z"
    },
    {
      "id": "message-124",
      "sessionId": "session-123",
      "content": "Hallo! Ich helfe Ihnen gerne bei Fragen zu Produkt XYZ. Was möchten Sie wissen?",
      "role": "assistant",
      "createdAt": "2023-01-01T00:00:01.000Z"
    }
  ]
}
```

#### Chat-Nachricht senden

```
POST /chat/sessions/:sessionId/messages
```

Sendet eine neue Nachricht in einer bestimmten Chat-Sitzung.

**Request Body:**

```json
{
  "content": "Wie lange ist die Garantie für Produkt XYZ?",
  "role": "user"
}
```

**Response:**

```json
{
  "id": "message-125",
  "sessionId": "session-123",
  "content": "Wie lange ist die Garantie für Produkt XYZ?",
  "role": "user",
  "createdAt": "2023-01-01T00:00:02.000Z"
}
```

### Benutzer

#### Benutzer abrufen

```
GET /users
```

Ruft alle Benutzer ab.

**Response:**

```json
{
  "users": [
    {
      "id": "user-123",
      "email": "<EMAIL>",
      "name": "John Doe",
      "role": "admin",
      "createdAt": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

#### Benutzer abrufen

```
GET /users/:id
```

Ruft einen bestimmten Benutzer ab.

**Response:**

```json
{
  "id": "user-123",
  "email": "<EMAIL>",
  "name": "John Doe",
  "role": "admin",
  "createdAt": "2023-01-01T00:00:00.000Z"
}
```

### System

#### Systemstatus abrufen

```
GET /system/status
```

Ruft den aktuellen Systemstatus ab.

**Response:**

```json
{
  "status": "ok",
  "version": "0.9.0",
  "uptime": 3600,
  "services": {
    "database": "ok",
    "redis": "ok",
    "llm": "ok"
  }
}
```

#### Systeminfo abrufen

```
GET /system/info
```

Ruft Informationen über das System ab.

**Response:**

```json
{
  "version": "0.9.0",
  "llmProviders": ["claude", "openai", "local-llm"],
  "tools": ["llm", "vapi", "crm"],
  "agents": ["default", "crm"],
  "applications": ["default", "custom-app-1"]
}
```

## Fehlerbehandlung

Alle API-Endpunkte geben im Fehlerfall einen entsprechenden HTTP-Statuscode und eine JSON-Antwort mit einer Fehlermeldung zurück:

```json
{
  "error": {
    "code": "AUTHENTICATION_FAILED",
    "message": "Ungültige Anmeldeinformationen",
    "details": {
      "field": "password",
      "reason": "Passwort ist falsch"
    }
  }
}
```

## Ratenbegrenzung

Die API hat eine Ratenbegrenzung von 100 Anfragen pro Minute pro Benutzer. Wenn Sie diese Grenze überschreiten, erhalten Sie einen HTTP-Statuscode 429 (Too Many Requests) und eine entsprechende Fehlermeldung.

## Versioning

Die API verwendet Versioning über den URL-Pfad. Die aktuelle Version ist `v1`:

```
http://localhost:3200/api/v1/...
```

Zukünftige Versionen werden über den URL-Pfad verfügbar sein:

```
http://localhost:3200/api/v2/...
```

## Beispiele

### cURL

```bash
# Login
curl -X POST http://localhost:3200/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Nachricht generieren
curl -X POST http://localhost:3200/api/llm/generate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{"prompt":"Wie ist das Wetter heute?","systemPrompt":"Du bist ein hilfreicher Assistent.","options":{"temperature":0.7,"maxTokens":1000},"preferredProvider":"claude","taskType":"general"}'

# Aufgaben abrufen
curl -X GET http://localhost:3200/api/tasks \
  -H "Authorization: Bearer <token>"
```

### JavaScript (Fetch)

```javascript
// Login
fetch('http://localhost:3200/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
})
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));

// Nachricht generieren
fetch('http://localhost:3200/api/llm/generate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer <token>'
  },
  body: JSON.stringify({
    prompt: 'Wie ist das Wetter heute?',
    systemPrompt: 'Du bist ein hilfreicher Assistent.',
    options: {
      temperature: 0.7,
      maxTokens: 1000
    },
    preferredProvider: 'claude',
    taskType: 'general'
  })
})
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));
```

### Python (Requests)

```python
import requests
import json

# Login
response = requests.post(
    'http://localhost:3200/api/auth/login',
    headers={'Content-Type': 'application/json'},
    data=json.dumps({
        'email': '<EMAIL>',
        'password': 'password123'
    })
)
data = response.json()
token = data['token']

# Nachricht generieren
response = requests.post(
    'http://localhost:3200/api/llm/generate',
    headers={
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    },
    data=json.dumps({
        'prompt': 'Wie ist das Wetter heute?',
        'systemPrompt': 'Du bist ein hilfreicher Assistent.',
        'options': {
            'temperature': 0.7,
            'maxTokens': 1000
        },
        'preferredProvider': 'claude',
        'taskType': 'general'
    })
)
data = response.json()
print(data)
