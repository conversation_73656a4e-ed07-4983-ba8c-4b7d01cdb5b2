#!/bin/bash

# SPQR3 Database Initialization Script
# This script initializes the PostgreSQL database with the required schema and test data

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}   SPQR3 Database Initialization         ${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# Function to check if PostgreSQL is ready
check_postgres_ready() {
  echo -e "${YELLOW}Checking if PostgreSQL is ready...${NC}"
  
  # Try to connect to PostgreSQL
  if docker exec spqr-postgres pg_isready -U postgres > /dev/null 2>&1; then
    echo -e "${GREEN}✓ PostgreSQL is ready${NC}"
    return 0
  else
    echo -e "${RED}✗ PostgreSQL is not ready${NC}"
    return 1
  fi
}

# Function to check if database exists
check_database_exists() {
  local db_name=$1
  
  echo -e "${YELLOW}Checking if database ${db_name} exists...${NC}"
  
  # Check if database exists
  if docker exec spqr-postgres psql -U postgres -lqt | cut -d \| -f 1 | grep -qw $db_name; then
    echo -e "${GREEN}✓ Database ${db_name} exists${NC}"
    return 0
  else
    echo -e "${RED}✗ Database ${db_name} does not exist${NC}"
    return 1
  fi
}

# Function to create database
create_database() {
  local db_name=$1
  
  echo -e "${YELLOW}Creating database ${db_name}...${NC}"
  
  # Create database
  if docker exec spqr-postgres psql -U postgres -c "CREATE DATABASE ${db_name};" > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Database ${db_name} created${NC}"
    return 0
  else
    echo -e "${RED}✗ Failed to create database ${db_name}${NC}"
    return 1
  fi
}

# Function to check if pgvector extension is installed
check_pgvector_extension() {
  local db_name=$1
  
  echo -e "${YELLOW}Checking if pgvector extension is installed in ${db_name}...${NC}"
  
  # Check if pgvector extension is installed
  if docker exec spqr-postgres psql -U postgres -d $db_name -c "SELECT * FROM pg_extension WHERE extname = 'vector';" | grep -q vector; then
    echo -e "${GREEN}✓ pgvector extension is installed${NC}"
    return 0
  else
    echo -e "${RED}✗ pgvector extension is not installed${NC}"
    return 1
  fi
}

# Function to install pgvector extension
install_pgvector_extension() {
  local db_name=$1
  
  echo -e "${YELLOW}Installing pgvector extension in ${db_name}...${NC}"
  
  # Install pgvector extension
  if docker exec spqr-postgres psql -U postgres -d $db_name -c "CREATE EXTENSION IF NOT EXISTS vector;" > /dev/null 2>&1; then
    echo -e "${GREEN}✓ pgvector extension installed${NC}"
    return 0
  else
    echo -e "${RED}✗ Failed to install pgvector extension${NC}"
    return 1
  fi
}

# Function to check if schema is initialized
check_schema_initialized() {
  local db_name=$1
  
  echo -e "${YELLOW}Checking if schema is initialized in ${db_name}...${NC}"
  
  # Check if users table exists
  if docker exec spqr-postgres psql -U postgres -d $db_name -c "\dt users" | grep -q users; then
    echo -e "${GREEN}✓ Schema is initialized${NC}"
    return 0
  else
    echo -e "${RED}✗ Schema is not initialized${NC}"
    return 1
  fi
}

# Function to initialize schema
initialize_schema() {
  local db_name=$1
  
  echo -e "${YELLOW}Initializing schema in ${db_name}...${NC}"
  
  # Check if schema.sql exists
  if [ ! -f "sql/schema.sql" ]; then
    echo -e "${RED}✗ schema.sql not found${NC}"
    return 1
  fi
  
  # Initialize schema
  if cat sql/schema.sql | docker exec -i spqr-postgres psql -U postgres -d $db_name > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Schema initialized${NC}"
    return 0
  else
    echo -e "${RED}✗ Failed to initialize schema${NC}"
    return 1
  fi
}

# Function to check if test data is loaded
check_test_data_loaded() {
  local db_name=$1
  
  echo -e "${YELLOW}Checking if test data is loaded in ${db_name}...${NC}"
  
  # Check if admin user exists
  if docker exec spqr-postgres psql -U postgres -d $db_name -c "SELECT * FROM users WHERE username = 'admin';" | grep -q admin; then
    echo -e "${GREEN}✓ Test data is loaded${NC}"
    return 0
  else
    echo -e "${RED}✗ Test data is not loaded${NC}"
    return 1
  fi
}

# Function to load test data
load_test_data() {
  local db_name=$1
  
  echo -e "${YELLOW}Loading test data in ${db_name}...${NC}"
  
  # Check if test-data.sql exists
  if [ ! -f "sql/test-data.sql" ]; then
    echo -e "${RED}✗ test-data.sql not found${NC}"
    return 1
  fi
  
  # Load test data
  if cat sql/test-data.sql | docker exec -i spqr-postgres psql -U postgres -d $db_name > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Test data loaded${NC}"
    return 0
  else
    echo -e "${RED}✗ Failed to load test data${NC}"
    return 1
  fi
}

# Main function
main() {
  # Get database name from environment or use default
  DB_NAME=${POSTGRES_DB:-spqr2}
  
  echo -e "${YELLOW}Initializing database ${DB_NAME}...${NC}"
  
  # Wait for PostgreSQL to be ready
  max_attempts=30
  attempt=1
  
  while [ $attempt -le $max_attempts ]; do
    if check_postgres_ready; then
      break
    fi
    
    echo -e "${YELLOW}Waiting for PostgreSQL to be ready (attempt ${attempt}/${max_attempts})...${NC}"
    sleep 2
    attempt=$((attempt+1))
  done
  
  if [ $attempt -gt $max_attempts ]; then
    echo -e "${RED}PostgreSQL did not become ready within the timeout period.${NC}"
    exit 1
  fi
  
  # Check if database exists, create if not
  if ! check_database_exists $DB_NAME; then
    if ! create_database $DB_NAME; then
      echo -e "${RED}Failed to create database. Exiting.${NC}"
      exit 1
    fi
  fi
  
  # Check if pgvector extension is installed, install if not
  if ! check_pgvector_extension $DB_NAME; then
    if ! install_pgvector_extension $DB_NAME; then
      echo -e "${RED}Failed to install pgvector extension. Exiting.${NC}"
      exit 1
    fi
  fi
  
  # Check if schema is initialized, initialize if not
  if ! check_schema_initialized $DB_NAME; then
    if ! initialize_schema $DB_NAME; then
      echo -e "${RED}Failed to initialize schema. Exiting.${NC}"
      exit 1
    fi
  fi
  
  # Check if test data is loaded, load if not
  if ! check_test_data_loaded $DB_NAME; then
    if ! load_test_data $DB_NAME; then
      echo -e "${RED}Failed to load test data. Exiting.${NC}"
      exit 1
    fi
  fi
  
  echo -e "${GREEN}Database initialization completed successfully.${NC}"
}

# Run main function
main
