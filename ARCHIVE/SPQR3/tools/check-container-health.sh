#!/bin/bash

# SPQR3 Container Health Check Script
# This script checks the health of all SPQR3 containers

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if a container exists
container_exists() {
  docker ps -a --format "{{.Names}}" | grep -q "$1"
  return $?
}

# Function to check container health
check_container_health() {
  local container_name=$1
  
  echo -e "${YELLOW}Checking container ${container_name}...${NC}"
  
  # Check if container exists
  if ! container_exists $container_name; then
    echo -e "${RED}✗ Container ${container_name} does not exist${NC}"
    return 1
  fi
  
  # Check if container is running
  if docker ps --format "{{.Names}}" | grep -q "$container_name"; then
    echo -e "${GREEN}✓ Container ${container_name} is running${NC}"
    
    # Check container health if available
    local health=$(docker inspect --format='{{if .State.Health}}{{.State.Health.Status}}{{else}}no health check{{end}}' $container_name 2>/dev/null)
    
    if [ "$health" == "healthy" ]; then
      echo -e "${GREEN}✓ Health status: ${health}${NC}"
      return 0
    elif [ "$health" == "no health check" ]; then
      echo -e "${YELLOW}! No health check configured${NC}"
      return 0
    else
      echo -e "${RED}✗ Health status: ${health}${NC}"
      
      # Show recent health check logs
      echo -e "${YELLOW}Recent health check logs:${NC}"
      docker inspect --format='{{range .State.Health.Log}}{{.Output}}{{end}}' $container_name | tail -n 1
      
      return 1
    fi
  else
    echo -e "${RED}✗ Container ${container_name} is not running${NC}"
    
    # Show container status
    local status=$(docker inspect --format='{{.State.Status}}' $container_name)
    echo -e "${RED}  Status: ${status}${NC}"
    
    # Show exit code and reason if available
    if [ "$status" == "exited" ]; then
      local exit_code=$(docker inspect --format='{{.State.ExitCode}}' $container_name)
      local exit_reason=$(docker inspect --format='{{.State.Error}}' $container_name)
      
      echo -e "${RED}  Exit code: ${exit_code}${NC}"
      
      if [ -n "$exit_reason" ]; then
        echo -e "${RED}  Exit reason: ${exit_reason}${NC}"
      fi
    fi
    
    return 1
  fi
}

# Function to check service health via HTTP
check_service_health() {
  local service_name=$1
  local url=$2
  local expected_status=${3:-200}
  
  echo -e "${YELLOW}Checking service ${service_name} at ${url}...${NC}"
  
  # Use curl to check the health endpoint
  local response=$(curl -s -o /dev/null -w "%{http_code}" $url)
  
  if [ "$response" == "$expected_status" ]; then
    echo -e "${GREEN}✓ Service ${service_name} is healthy (HTTP ${response})${NC}"
    return 0
  else
    echo -e "${RED}✗ Service ${service_name} returned HTTP ${response} (expected ${expected_status})${NC}"
    return 1
  fi
}

# Main function
main() {
  echo -e "${BLUE}========================================${NC}"
  echo -e "${BLUE}   SPQR3 Container Health Check         ${NC}"
  echo -e "${BLUE}========================================${NC}"
  echo ""
  
  # Check if Docker is running
  if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}Docker is not running. Please start Docker and try again.${NC}"
    exit 1
  fi
  
  # Check core containers
  check_container_health "spqr-postgres" || true
  check_container_health "spqr-redis" || true
  check_container_health "spqr-vector-store" || true
  check_container_health "spqr-ollama" || true
  
  # Check service containers
  check_container_health "spqr-core" || true
  check_container_health "spqr-mcp-coordinator" || true
  check_container_health "spqr-task-manager" || true
  check_container_health "spqr-browser-automation" || true
  check_container_health "spqr-frontend" || true
  
  echo ""
  echo -e "${BLUE}========================================${NC}"
  echo -e "${BLUE}   Service Health Check                 ${NC}"
  echo -e "${BLUE}========================================${NC}"
  echo ""
  
  # Check service health endpoints
  check_service_health "Backend API" "http://localhost:3200/health" || true
  check_service_health "MCP Coordinator" "http://localhost:3210/health" || true
  check_service_health "Task Manager" "http://localhost:3220/health" || true
  check_service_health "Browser Automation" "http://localhost:3230/health" || true
  check_service_health "Frontend" "http://localhost" || true
  
  echo ""
  echo -e "${YELLOW}Health check completed.${NC}"
}

# Run main function
main
