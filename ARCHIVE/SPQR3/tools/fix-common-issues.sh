#!/bin/bash

# SPQR3 Common Issues Fix Tool
# This script fixes common issues with SPQR3 installation

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}   SPQR3 Common Issues Fix Tool         ${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# Function to check if a container exists
container_exists() {
  docker ps -a --format "{{.Names}}" | grep -q "$1"
  return $?
}

# Function to check if a file exists
file_exists() {
  [ -f "$1" ]
  return $?
}

# Function to create missing directories
create_missing_directories() {
  echo -e "${YELLOW}Creating missing directories...${NC}"
  
  # Create data directories
  mkdir -p data/postgres
  mkdir -p data/redis
  mkdir -p data/vector-store
  mkdir -p data/ollama
  
  echo -e "${GREEN}✓ Data directories created${NC}"
}

# Function to fix missing error middleware
fix_missing_error_middleware() {
  echo -e "${YELLOW}Checking for missing error middleware...${NC}"
  
  if [ -d "backend/src" ]; then
    # Create middleware directory if it doesn't exist
    mkdir -p backend/src/middleware
    
    # Check if error.js exists
    if [ ! -f "backend/src/middleware/error.js" ]; then
      echo -e "${YELLOW}Creating missing error middleware file...${NC}"
      
      cat > backend/src/middleware/error.js << 'EOF'
/**
 * Error handling middleware
 * Processes errors and sends appropriate responses to clients
 */

const logger = require('../utils/logger');

/**
 * Central error handling middleware
 */
const errorHandler = (err, req, res, next) => {
  const statusCode = err.statusCode || 500;
  const errorId = req.id || 'unknown';
  
  // Log the error with request ID for traceability
  logger.error(`[${errorId}] ${err.message}`, {
    stack: err.stack,
    statusCode,
    path: req.path,
    method: req.method
  });
  
  // Send appropriate response
  res.status(statusCode).json({
    error: {
      message: err.message,
      requestId: errorId,
      status: statusCode,
      timestamp: new Date().toISOString()
    }
  });
};

/**
 * 404 Not Found middleware
 */
const notFoundHandler = (req, res, next) => {
  const err = new Error(`Route not found: ${req.originalUrl}`);
  err.statusCode = 404;
  next(err);
};

module.exports = {
  errorHandler,
  notFoundHandler
};
EOF
      
      echo -e "${GREEN}✓ Created missing error middleware file${NC}"
    else
      echo -e "${GREEN}✓ Error middleware file already exists${NC}"
    fi
    
    # Check if logger exists
    if [ ! -d "backend/src/utils" ]; then
      mkdir -p backend/src/utils
    fi
    
    if [ ! -f "backend/src/utils/logger.js" ]; then
      echo -e "${YELLOW}Creating missing logger file...${NC}"
      
      cat > backend/src/utils/logger.js << 'EOF'
/**
 * Logger utility
 * Provides consistent logging across the application
 */

const winston = require('winston');

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: { service: 'spqr-core' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    new winston.transports.File({ 
      filename: 'logs/error.log', 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: 'logs/combined.log' 
    })
  ]
});

// Create logs directory if it doesn't exist
const fs = require('fs');
const path = require('path');
const logsDir = path.join(process.cwd(), 'logs');

if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

module.exports = logger;
EOF
      
      echo -e "${GREEN}✓ Created missing logger file${NC}"
    else
      echo -e "${GREEN}✓ Logger file already exists${NC}"
    fi
  else
    echo -e "${RED}✗ Backend source directory not found${NC}"
  fi
}

# Function to fix MCP coordinator issues
fix_mcp_coordinator_issues() {
  echo -e "${YELLOW}Checking for MCP coordinator issues...${NC}"
  
  if [ -d "mcp-layer/src" ]; then
    # Create utils directory if it doesn't exist
    mkdir -p mcp-layer/src/utils
    
    # Check if logger exists
    if [ ! -f "mcp-layer/src/utils/logger.js" ]; then
      echo -e "${YELLOW}Creating missing logger file for MCP coordinator...${NC}"
      
      cat > mcp-layer/src/utils/logger.js << 'EOF'
/**
 * Logger utility
 * Provides consistent logging across the application
 */

const winston = require('winston');

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: { service: 'mcp-coordinator' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    new winston.transports.File({ 
      filename: 'logs/error.log', 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: 'logs/combined.log' 
    })
  ]
});

// Create logs directory if it doesn't exist
const fs = require('fs');
const path = require('path');
const logsDir = path.join(process.cwd(), 'logs');

if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

module.exports = logger;
EOF
      
      echo -e "${GREEN}✓ Created missing logger file for MCP coordinator${NC}"
    else
      echo -e "${GREEN}✓ Logger file for MCP coordinator already exists${NC}"
    fi
  else
    echo -e "${RED}✗ MCP coordinator source directory not found${NC}"
  fi
}

# Function to fix task manager issues
fix_task_manager_issues() {
  echo -e "${YELLOW}Checking for task manager issues...${NC}"
  
  if [ -d "task-management/src" ]; then
    # Create utils directory if it doesn't exist
    mkdir -p task-management/src/utils
    
    # Check if logger exists
    if [ ! -f "task-management/src/utils/logger.js" ]; then
      echo -e "${YELLOW}Creating missing logger file for task manager...${NC}"
      
      cat > task-management/src/utils/logger.js << 'EOF'
/**
 * Logger utility
 * Provides consistent logging across the application
 */

const winston = require('winston');

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: { service: 'task-manager' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    new winston.transports.File({ 
      filename: 'logs/error.log', 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: 'logs/combined.log' 
    })
  ]
});

// Create logs directory if it doesn't exist
const fs = require('fs');
const path = require('path');
const logsDir = path.join(process.cwd(), 'logs');

if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

module.exports = logger;
EOF
      
      echo -e "${GREEN}✓ Created missing logger file for task manager${NC}"
    else
      echo -e "${GREEN}✓ Logger file for task manager already exists${NC}"
    fi
  else
    echo -e "${RED}✗ Task manager source directory not found${NC}"
  fi
}

# Function to check and fix environment variables
check_env_variables() {
  echo -e "${YELLOW}Checking environment variables...${NC}"
  
  if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
      echo -e "${YELLOW}Creating .env file from .env.example...${NC}"
      cp .env.example .env
      echo -e "${GREEN}✓ Created .env file${NC}"
      echo -e "${YELLOW}! Please edit the .env file and set your API keys and passwords${NC}"
    else
      echo -e "${RED}✗ .env.example file not found${NC}"
    fi
  else
    echo -e "${GREEN}✓ .env file already exists${NC}"
  fi
}

# Function to fix frontend build issues
fix_frontend_build_issues() {
  echo -e "${YELLOW}Checking frontend build script...${NC}"
  
  if [ -f "build-frontend.sh" ]; then
    echo -e "${GREEN}✓ Frontend build script exists${NC}"
    
    # Make it executable
    chmod +x build-frontend.sh
    
    # Check if frontend directory exists
    if [ -d "frontend" ]; then
      echo -e "${GREEN}✓ Frontend directory exists${NC}"
      
      # Check if package.json exists
      if [ -f "frontend/package.json" ]; then
        echo -e "${GREEN}✓ Frontend package.json exists${NC}"
      else
        echo -e "${RED}✗ Frontend package.json not found${NC}"
      fi
    else
      echo -e "${RED}✗ Frontend directory not found${NC}"
    fi
  else
    echo -e "${RED}✗ Frontend build script not found${NC}"
    
    # Create build-frontend.sh
    echo -e "${YELLOW}Creating frontend build script...${NC}"
    
    cat > build-frontend.sh << 'EOF'
#!/bin/bash

# Navigate to the frontend directory
cd frontend

# Install dependencies
echo "Installing frontend dependencies..."
npm install --legacy-peer-deps

# Build the frontend
echo "Building frontend..."
npm run build

# Copy the built frontend to the build directory
echo "Copying built frontend to build directory..."
mkdir -p build
cp -r build/* build/

echo "Frontend build complete!"
EOF
    
    chmod +x build-frontend.sh
    echo -e "${GREEN}✓ Created frontend build script${NC}"
  fi
}

# Function to check and fix Docker Compose file
check_docker_compose() {
  echo -e "${YELLOW}Checking Docker Compose file...${NC}"
  
  if [ -f "docker-compose.yml" ]; then
    echo -e "${GREEN}✓ Docker Compose file exists${NC}"
  else
    echo -e "${RED}✗ Docker Compose file not found${NC}"
  fi
}

# Function to check and create SQL initialization files
check_sql_files() {
  echo -e "${YELLOW}Checking SQL initialization files...${NC}"
  
  # Create sql directory if it doesn't exist
  mkdir -p sql
  
  # Check if schema.sql exists
  if [ ! -f "sql/schema.sql" ]; then
    echo -e "${YELLOW}Creating schema.sql file...${NC}"
    
    cat > sql/schema.sql << 'EOF'
-- SPQR3 Database Schema

-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id SERIAL PRIMARY KEY,
  username VARCHAR(255) NOT NULL UNIQUE,
  email VARCHAR(255) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  full_name VARCHAR(255),
  role VARCHAR(50) NOT NULL DEFAULT 'user',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create organizations table
CREATE TABLE IF NOT EXISTS organizations (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create organization_users table
CREATE TABLE IF NOT EXISTS organization_users (
  id SERIAL PRIMARY KEY,
  organization_id INTEGER NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  role VARCHAR(50) NOT NULL DEFAULT 'member',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(organization_id, user_id)
);

-- Create api_keys table
CREATE TABLE IF NOT EXISTS api_keys (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  provider VARCHAR(50) NOT NULL,
  api_key VARCHAR(255) NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create vector_collections table
CREATE TABLE IF NOT EXISTS vector_collections (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  organization_id INTEGER REFERENCES organizations(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create vector_documents table
CREATE TABLE IF NOT EXISTS vector_documents (
  id SERIAL PRIMARY KEY,
  collection_id INTEGER NOT NULL REFERENCES vector_collections(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  metadata JSONB,
  embedding vector(1536),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create tasks table
CREATE TABLE IF NOT EXISTS tasks (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  status VARCHAR(50) NOT NULL DEFAULT 'pending',
  priority VARCHAR(50) NOT NULL DEFAULT 'medium',
  user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
  organization_id INTEGER REFERENCES organizations(id) ON DELETE CASCADE,
  due_date TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create task_executions table
CREATE TABLE IF NOT EXISTS task_executions (
  id SERIAL PRIMARY KEY,
  task_id INTEGER NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  status VARCHAR(50) NOT NULL DEFAULT 'pending',
  result JSONB,
  error TEXT,
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create system_settings table
CREATE TABLE IF NOT EXISTS system_settings (
  id SERIAL PRIMARY KEY,
  key VARCHAR(255) NOT NULL UNIQUE,
  value JSONB NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create audit_logs table
CREATE TABLE IF NOT EXISTS audit_logs (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
  action VARCHAR(255) NOT NULL,
  entity_type VARCHAR(255),
  entity_id INTEGER,
  details JSONB,
  ip_address VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
EOF
    
    echo -e "${GREEN}✓ Created schema.sql file${NC}"
  else
    echo -e "${GREEN}✓ schema.sql file already exists${NC}"
  fi
  
  # Check if test-data.sql exists
  if [ ! -f "sql/test-data.sql" ]; then
    echo -e "${YELLOW}Creating test-data.sql file...${NC}"
    
    cat > sql/test-data.sql << 'EOF'
-- SPQR3 Test Data

-- Insert test user
INSERT INTO users (username, email, password_hash, full_name, role)
VALUES 
  ('admin', '<EMAIL>', '$2a$10$JwrFcgQNGIXbqYTBGQlgXOUxW2XG5U3lN41a8QWgJ1F6aXNsfWO8a', 'Admin User', 'admin'),
  ('user', '<EMAIL>', '$2a$10$JwrFcgQNGIXbqYTBGQlgXOUxW2XG5U3lN41a8QWgJ1F6aXNsfWO8a', 'Regular User', 'user')
ON CONFLICT (username) DO NOTHING;

-- Insert test organization
INSERT INTO organizations (name, description)
VALUES ('Test Organization', 'This is a test organization')
ON CONFLICT DO NOTHING;

-- Link users to organization
INSERT INTO organization_users (organization_id, user_id, role)
VALUES 
  (1, 1, 'admin'),
  (1, 2, 'member')
ON CONFLICT DO NOTHING;

-- Insert system settings
INSERT INTO system_settings (key, value, description)
VALUES 
  ('default_llm_provider', '{"provider": "ollama", "model": "llama3"}', 'Default LLM provider and model'),
  ('system_version', '{"version": "0.9.5", "build_date": "2023-06-01"}', 'System version information')
ON CONFLICT DO NOTHING;
EOF
    
    echo -e "${GREEN}✓ Created test-data.sql file${NC}"
  else
    echo -e "${GREEN}✓ test-data.sql file already exists${NC}"
  fi
}

# Main function
main() {
  # Create missing directories
  create_missing_directories
  
  # Fix missing error middleware
  fix_missing_error_middleware
  
  # Fix MCP coordinator issues
  fix_mcp_coordinator_issues
  
  # Fix task manager issues
  fix_task_manager_issues
  
  # Check environment variables
  check_env_variables
  
  # Fix frontend build issues
  fix_frontend_build_issues
  
  # Check Docker Compose file
  check_docker_compose
  
  # Check SQL files
  check_sql_files
  
  echo -e "${GREEN}Common issues fix completed.${NC}"
  echo -e "${YELLOW}Please restart your containers with:${NC}"
  echo -e "  docker compose down"
  echo -e "  docker compose up -d"
}

# Run main function
main
