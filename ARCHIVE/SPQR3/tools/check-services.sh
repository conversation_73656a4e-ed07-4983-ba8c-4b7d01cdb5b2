#!/bin/bash

# SPQR Service Health Check Script
# This script checks the health of all SPQR services

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print header
echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}   SPQR Service Health Check           ${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# Function to check if a service is running
check_service() {
  local service_name=$1
  local service_url=$2
  local expected_status=$3
  
  echo -e "${YELLOW}Checking ${service_name}...${NC}"
  
  # Make a request to the service
  response=$(curl -s -o /dev/null -w "%{http_code}" $service_url)
  
  if [ "$response" = "$expected_status" ]; then
    echo -e "${GREEN}✓ ${service_name} is running (Status: $response)${NC}"
    return 0
  else
    echo -e "${RED}✗ ${service_name} is not running properly (Status: $response, Expected: $expected_status)${NC}"
    return 1
  fi
}

# Check API service
check_service "SPQR API" "http://localhost:3000/api/info" "200"
api_status=$?

# Check MongoDB
mongo_status=1
if command -v mongosh &> /dev/null; then
  echo -e "${YELLOW}Checking MongoDB...${NC}"
  if mongosh --eval "db.adminCommand('ping')" > /dev/null 2>&1; then
    echo -e "${GREEN}✓ MongoDB is running${NC}"
    mongo_status=0
  else
    echo -e "${RED}✗ MongoDB is not running${NC}"
  fi
else
  echo -e "${RED}✗ MongoDB client (mongosh) not found${NC}"
fi

# Check Redis
redis_status=1
if command -v redis-cli &> /dev/null; then
  echo -e "${YELLOW}Checking Redis...${NC}"
  if redis-cli ping > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Redis is running${NC}"
    redis_status=0
  else
    echo -e "${RED}✗ Redis is not running${NC}"
  fi
else
  echo -e "${RED}✗ Redis client (redis-cli) not found${NC}"
fi

# Check Qdrant
check_service "Qdrant" "http://localhost:6333/health" "200"
qdrant_status=$?

# Print summary
echo ""
echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}   Service Health Summary              ${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

if [ $api_status -eq 0 ]; then
  echo -e "${GREEN}✓ SPQR API: Running${NC}"
else
  echo -e "${RED}✗ SPQR API: Not running${NC}"
fi

if [ $mongo_status -eq 0 ]; then
  echo -e "${GREEN}✓ MongoDB: Running${NC}"
else
  echo -e "${RED}✗ MongoDB: Not running${NC}"
fi

if [ $redis_status -eq 0 ]; then
  echo -e "${GREEN}✓ Redis: Running${NC}"
else
  echo -e "${RED}✗ Redis: Not running${NC}"
fi

if [ $qdrant_status -eq 0 ]; then
  echo -e "${GREEN}✓ Qdrant: Running${NC}"
else
  echo -e "${RED}✗ Qdrant: Not running${NC}"
fi

# Calculate overall status
overall_status=$(( $api_status + $mongo_status + $redis_status + $qdrant_status ))

echo ""
if [ $overall_status -eq 0 ]; then
  echo -e "${GREEN}All services are running properly!${NC}"
  exit 0
else
  echo -e "${RED}Some services are not running properly. Please check the logs for more information.${NC}"
  exit 1
fi
