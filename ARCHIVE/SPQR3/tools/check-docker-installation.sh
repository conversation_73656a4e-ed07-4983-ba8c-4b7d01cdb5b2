#!/bin/bash

# SPQR3 Docker Installation Check Script
# This script checks if <PERSON><PERSON> and <PERSON>er Compose are installed correctly

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}   SPQR3 Docker Installation Check      ${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# Check if Docker is installed
echo -e "${YELLOW}Checking if Docker is installed...${NC}"
if command -v docker &> /dev/null; then
  DOCKER_VERSION=$(docker --version)
  echo -e "${GREEN}✓ Docker is installed: $DOCKER_VERSION${NC}"
else
  echo -e "${RED}✗ Docker is not installed${NC}"
  echo -e "${YELLOW}Please install Docker using the following commands:${NC}"
  echo -e "sudo apt update"
  echo -e "sudo apt install -y apt-transport-https ca-certificates curl software-properties-common"
  echo -e "curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -"
  echo -e "sudo add-apt-repository \"deb [arch=amd64] https://download.docker.com/linux/ubuntu \$(lsb_release -cs) stable\""
  echo -e "sudo apt update"
  echo -e "sudo apt install -y docker-ce"
  echo -e "sudo usermod -aG docker \${USER}"
  echo -e "newgrp docker"
  exit 1
fi

# Check if Docker daemon is running
echo -e "${YELLOW}Checking if Docker daemon is running...${NC}"
if docker info &> /dev/null; then
  echo -e "${GREEN}✓ Docker daemon is running${NC}"
else
  echo -e "${RED}✗ Docker daemon is not running${NC}"
  echo -e "${YELLOW}Please start Docker using the following command:${NC}"
  echo -e "sudo systemctl start docker"
  exit 1
fi

# Check if Docker Compose is installed
echo -e "${YELLOW}Checking if Docker Compose is installed...${NC}"
if command -v docker-compose &> /dev/null; then
  COMPOSE_VERSION=$(docker-compose --version)
  echo -e "${GREEN}✓ Docker Compose is installed: $COMPOSE_VERSION${NC}"
elif command -v docker &> /dev/null && docker compose version &> /dev/null; then
  COMPOSE_VERSION=$(docker compose version)
  echo -e "${GREEN}✓ Docker Compose plugin is installed: $COMPOSE_VERSION${NC}"
else
  echo -e "${RED}✗ Docker Compose is not installed${NC}"
  echo -e "${YELLOW}Please install Docker Compose using the following commands:${NC}"
  echo -e "sudo curl -L \"https://github.com/docker/compose/releases/download/v2.18.1/docker-compose-\$(uname -s)-\$(uname -m)\" -o /usr/local/bin/docker-compose"
  echo -e "sudo chmod +x /usr/local/bin/docker-compose"
  exit 1
fi

# Check Docker permissions
echo -e "${YELLOW}Checking Docker permissions...${NC}"
if groups | grep -q docker; then
  echo -e "${GREEN}✓ Current user is in the docker group${NC}"
else
  echo -e "${RED}✗ Current user is not in the docker group${NC}"
  echo -e "${YELLOW}Please add the current user to the docker group using the following commands:${NC}"
  echo -e "sudo usermod -aG docker \${USER}"
  echo -e "newgrp docker"
  exit 1
fi

echo -e "${GREEN}✓ Docker and Docker Compose are installed correctly${NC}"
echo -e "${GREEN}✓ You can now proceed with the SPQR3 installation${NC}"
