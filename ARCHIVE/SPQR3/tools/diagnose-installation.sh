#!/bin/bash

# SPQR3 Installation Diagnostic Tool
# This script helps diagnose issues with SPQR3 installation

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Create results directory
RESULTS_DIR="./diagnostic-results"
mkdir -p $RESULTS_DIR

# Log function
log_result() {
  echo -e "$1"
  echo "$1" | sed 's/\x1b\[[0-9;]*m//g' >> "${RESULTS_DIR}/diagnostic-summary.txt"
}

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}   SPQR3 Installation Diagnostic Tool   ${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""
log_result "SPQR3 Diagnostic started at $(date)"
log_result "----------------------------------------"

# Check system requirements
echo -e "${YELLOW}Checking system requirements...${NC}"
log_result "System Requirements:"

# Check CPU
CPU_CORES=$(nproc)
if [ $CPU_CORES -ge 2 ]; then
  log_result "${GREEN}✓ CPU: $CPU_CORES cores available (minimum: 2)${NC}"
else
  log_result "${RED}✗ CPU: Only $CPU_CORES core available (minimum: 2)${NC}"
fi

# Check RAM
TOTAL_MEM=$(free -m | awk '/^Mem:/{print $2}')
if [ $TOTAL_MEM -ge 4000 ]; then
  log_result "${GREEN}✓ RAM: $TOTAL_MEM MB available (minimum: 4000 MB)${NC}"
else
  log_result "${RED}✗ RAM: Only $TOTAL_MEM MB available (minimum: 4000 MB)${NC}"
fi

# Check disk space
DISK_SPACE=$(df -m . | awk 'NR==2 {print $4}')
if [ $DISK_SPACE -ge 20000 ]; then
  log_result "${GREEN}✓ Disk: $DISK_SPACE MB available (minimum: 20000 MB)${NC}"
else
  log_result "${RED}✗ Disk: Only $DISK_SPACE MB available (minimum: 20000 MB)${NC}"
fi

# Check Docker
echo -e "${YELLOW}Checking Docker installation...${NC}"
log_result "Docker Installation:"

if command -v docker &> /dev/null; then
  DOCKER_VERSION=$(docker --version)
  log_result "${GREEN}✓ Docker is installed: $DOCKER_VERSION${NC}"
  
  # Check if Docker daemon is running
  if docker info &> /dev/null; then
    log_result "${GREEN}✓ Docker daemon is running${NC}"
  else
    log_result "${RED}✗ Docker daemon is not running${NC}"
  fi
else
  log_result "${RED}✗ Docker is not installed${NC}"
fi

# Check Docker Compose
if command -v docker-compose &> /dev/null; then
  COMPOSE_VERSION=$(docker-compose --version)
  log_result "${GREEN}✓ Docker Compose is installed: $COMPOSE_VERSION${NC}"
elif command -v docker &> /dev/null && docker compose version &> /dev/null; then
  COMPOSE_VERSION=$(docker compose version)
  log_result "${GREEN}✓ Docker Compose plugin is installed: $COMPOSE_VERSION${NC}"
else
  log_result "${RED}✗ Docker Compose is not installed${NC}"
fi

# Check if .env file exists
echo -e "${YELLOW}Checking configuration files...${NC}"
log_result "Configuration Files:"

if [ -f ".env" ]; then
  log_result "${GREEN}✓ .env file exists${NC}"
  
  # Check for required environment variables
  required_vars=("POSTGRES_PASSWORD" "JWT_SECRET")
  for var in "${required_vars[@]}"; do
    if grep -q "^$var=" .env; then
      log_result "${GREEN}✓ $var is configured in .env${NC}"
    else
      log_result "${RED}✗ $var is missing in .env${NC}"
    fi
  done
else
  log_result "${RED}✗ .env file is missing${NC}"
fi

# Check if docker-compose.yml exists
if [ -f "docker-compose.yml" ]; then
  log_result "${GREEN}✓ docker-compose.yml file exists${NC}"
else
  log_result "${RED}✗ docker-compose.yml file is missing${NC}"
fi

# Check container status
echo -e "${YELLOW}Checking container status...${NC}"
log_result "Container Status:"

RUNNING_CONTAINERS=$(docker ps --format "{{.Names}}" | grep "spqr-")
if [ -z "$RUNNING_CONTAINERS" ]; then
  log_result "${RED}✗ No SPQR3 containers are running${NC}"
else
  log_result "${GREEN}✓ Running containers:${NC}"
  for container in $RUNNING_CONTAINERS; do
    HEALTH=$(docker inspect --format='{{if .State.Health}}{{.State.Health.Status}}{{else}}no health check{{end}}' $container 2>/dev/null)
    
    if [ "$HEALTH" == "healthy" ]; then
      log_result "${GREEN}  ✓ $container: $HEALTH${NC}"
    elif [ "$HEALTH" == "no health check" ]; then
      log_result "${YELLOW}  ! $container: No health check configured${NC}"
    else
      log_result "${RED}  ✗ $container: $HEALTH${NC}"
      # Save logs for unhealthy containers
      docker logs $container --tail 50 > "${RESULTS_DIR}/${container}_health_logs.txt"
      log_result "    Health check logs saved to ${RESULTS_DIR}/${container}_health_logs.txt"
    fi
  done
fi

# Check for common errors in container logs
echo -e "${YELLOW}Checking container logs for errors...${NC}"
log_result "Container Logs Analysis:"

# List of containers to check
CONTAINERS_TO_CHECK="spqr-core spqr-mcp-coordinator spqr-task-manager spqr-browser-automation spqr-postgres spqr-redis spqr-vector-store spqr-ollama spqr-frontend"

for container in $CONTAINERS_TO_CHECK; do
  if docker ps -a --format "{{.Names}}" | grep -q "$container"; then
    echo -e "${YELLOW}Analyzing logs for: $container${NC}"
    docker logs $container --tail 100 > "${RESULTS_DIR}/${container}_recent.log" 2>&1
    
    # Look for errors in logs
    ERROR_COUNT=$(grep -i "error\|exception\|fail\|cannot find module" "${RESULTS_DIR}/${container}_recent.log" | wc -l)
    if [ $ERROR_COUNT -gt 0 ]; then
      log_result "${RED}✗ Found $ERROR_COUNT potential errors in $container logs${NC}"
      grep -i "error\|exception\|fail\|cannot find module" "${RESULTS_DIR}/${container}_recent.log" > "${RESULTS_DIR}/${container}_errors.log"
      log_result "  Errors saved to ${RESULTS_DIR}/${container}_errors.log"
      
      # Check for specific common errors
      if grep -q "Cannot find module" "${RESULTS_DIR}/${container}_errors.log"; then
        log_result "${RED}  ✗ Missing module error detected in $container${NC}"
      fi
      
      if grep -q "ECONNREFUSED" "${RESULTS_DIR}/${container}_errors.log"; then
        log_result "${RED}  ✗ Connection refused error detected in $container${NC}"
      fi
      
      if grep -q "password authentication failed" "${RESULTS_DIR}/${container}_errors.log"; then
        log_result "${RED}  ✗ Database authentication error detected in $container${NC}"
      fi
    else
      log_result "${GREEN}✓ No obvious errors found in $container logs${NC}"
    fi
  else
    log_result "${YELLOW}! Container $container does not exist${NC}"
  fi
done

# Check network connectivity between containers
echo -e "${YELLOW}Checking network connectivity...${NC}"
log_result "Network Connectivity:"

# Check if spqr-network exists
if docker network ls | grep -q "spqr-network"; then
  log_result "${GREEN}✓ spqr-network exists${NC}"
  
  # List containers in the network
  NETWORK_CONTAINERS=$(docker network inspect spqr-network -f '{{range .Containers}}{{.Name}} {{end}}')
  log_result "${GREEN}✓ Containers in network: $NETWORK_CONTAINERS${NC}"
else
  log_result "${RED}✗ spqr-network does not exist${NC}"
fi

# Check port availability
echo -e "${YELLOW}Checking port availability...${NC}"
log_result "Port Availability:"

# List of ports to check
PORTS_TO_CHECK="80 3200 3210 3220 3230 5432 6333 6379 11434"

for port in $PORTS_TO_CHECK; do
  if netstat -tuln | grep -q ":$port "; then
    PROCESS=$(lsof -i :$port | tail -n 1)
    log_result "${GREEN}✓ Port $port is in use by: $PROCESS${NC}"
  else
    log_result "${RED}✗ Port $port is not in use by any process${NC}"
  fi
done

# Summary
echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}   Diagnostic Summary                   ${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""
log_result "Diagnostic completed at $(date)"
log_result "Results saved to $RESULTS_DIR directory"

echo -e "${YELLOW}Diagnostic completed. Check ${RESULTS_DIR}/diagnostic-summary.txt for a summary of findings.${NC}"
