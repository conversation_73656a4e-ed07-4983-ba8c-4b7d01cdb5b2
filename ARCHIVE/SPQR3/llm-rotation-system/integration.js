/**
 * LLM Rotation System Integration
 *
 * Integration of the LLM Rotation System with the main application.
 */

const { createLLMRotationSystem } = require('./index');
const { createLogger } = require('./utils/logger');
const express = require('express');
const Joi = require('joi');
const path = require('path');

/**
 * Integrate LLM rotation system with the main application
 *
 * @param {Object} app - Express application
 * @param {Object} options - Integration options
 * @returns {Object} Integration result
 */
function integrateLLMRotationSystem(app, options = {}) {
  const logger = options.logger || createLogger({
    level: options.logLevel || 'info',
    filename: options.logFilename || 'llm-rotation.log'
  });
  const db = options.db;
  const llmService = options.llmService;

  // Create LLM rotation system
  const llmRotationSystem = createLLMRotationSystem({
    logger,
    db,
    llmService,
    defaultStrategy: options.defaultStrategy || 'adaptive',
    cacheEnabled: options.cacheEnabled !== false,
    cacheTtl: options.cacheTtl || 24 * 60 * 60 * 1000, // 24 hours
    learningRate: options.learningRate || 0.1,
    explorationRate: options.explorationRate || 0.1,
    logLevel: options.logLevel || 'info',
    logFilename: options.logFilename || 'llm-rotation.log'
  });

  // Create API routes
  const router = express.Router();

  // Serve frontend if enabled
  if (options.serveFrontend !== false) {
    const frontendPath = path.join(__dirname, '../frontend/build');
    app.use('/llm-rotation', express.static(frontendPath));

    // Serve index.html for all routes under /llm-rotation
    app.get('/llm-rotation/*', (req, res) => {
      res.sendFile(path.join(frontendPath, 'index.html'));
    });
  }

  // Validate request middleware
  const validateRequest = (schema) => {
    return (req, res, next) => {
      const { error } = schema.validate(req.body);

      if (error) {
        return res.status(400).json({
          success: false,
          error: error.details[0].message
        });
      }

      next();
    };
  };

  // Process task
  router.post('/process', validateRequest(Joi.object({
    prompt: Joi.string().required(),
    systemPrompt: Joi.string(),
    temperature: Joi.number().min(0).max(1),
    maxTokens: Joi.number().integer().min(1),
    topP: Joi.number().min(0).max(1),
    frequencyPenalty: Joi.number().min(-2).max(2),
    presencePenalty: Joi.number().min(-2).max(2),
    strategy: Joi.string(),
    useCache: Joi.boolean(),
    explore: Joi.boolean()
  })), async (req, res) => {
    try {
      // Process task
      const result = await llmRotationSystem.processTask(req.body, {
        strategy: req.body.strategy,
        useCache: req.body.useCache,
        explore: req.body.explore
      });

      // Return response
      res.json({
        success: true,
        response: result.response.response,
        provider: result.provider,
        classification: result.classification,
        tokens: {
          input: result.response.inputTokens,
          output: result.response.outputTokens,
          total: result.response.inputTokens + result.response.outputTokens
        },
        cost: result.response.cost,
        duration: result.response.duration,
        cached: req.body.useCache !== false && result.response.cached === true
      });
    } catch (error) {
      logger.error(`Error processing task: ${error.message}`, {
        error
      });

      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  // Get providers
  router.get('/providers', async (req, res) => {
    try {
      // Get providers
      const providers = llmRotationSystem.getAllProviders();

      // Return providers
      res.json({
        success: true,
        providers: providers.map(provider => provider.toJSON())
      });
    } catch (error) {
      logger.error(`Error getting providers: ${error.message}`, {
        error
      });

      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  // Get provider by ID
  router.get('/providers/:id', async (req, res) => {
    try {
      // Get provider
      const provider = llmRotationSystem.getProvider(req.params.id);

      // Check if provider exists
      if (!provider) {
        return res.status(404).json({
          success: false,
          error: `Provider not found: ${req.params.id}`
        });
      }

      // Return provider
      res.json({
        success: true,
        provider: provider.toJSON()
      });
    } catch (error) {
      logger.error(`Error getting provider: ${error.message}`, {
        error
      });

      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  // Register provider
  router.post('/providers', validateRequest(Joi.object({
    name: Joi.string().required(),
    description: Joi.string(),
    apiKeys: Joi.array().items(Joi.string()).required(),
    models: Joi.array().items(Joi.object({
      id: Joi.string().required(),
      name: Joi.string().required(),
      maxTokens: Joi.number().integer().required(),
      tier: Joi.string().valid('premium', 'standard', 'basic').required(),
      capabilities: Joi.array().items(Joi.string()).required(),
      costPerToken: Joi.object({
        input: Joi.number().required(),
        output: Joi.number().required()
      }).required()
    })).required(),
    baseUrl: Joi.string(),
    tier: Joi.string().valid('premium', 'standard', 'basic').required(),
    capabilities: Joi.array().items(Joi.string()).required()
  })), async (req, res) => {
    try {
      // Register provider
      const provider = await llmRotationSystem.registerProvider(req.body);

      // Return provider
      res.status(201).json({
        success: true,
        provider: provider.toJSON()
      });
    } catch (error) {
      logger.error(`Error registering provider: ${error.message}`, {
        error
      });

      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  // Get strategies
  router.get('/strategies', async (req, res) => {
    try {
      // Get strategies
      const strategies = llmRotationSystem.getAllStrategies();

      // Return strategies
      res.json({
        success: true,
        strategies: strategies.map(strategy => ({
          name: strategy.name
        }))
      });
    } catch (error) {
      logger.error(`Error getting strategies: ${error.message}`, {
        error
      });

      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  // Get analytics
  router.get('/analytics', async (req, res) => {
    try {
      const { startDate, endDate, providerId } = req.query;

      // Get analytics
      const analytics = await llmRotationSystem.analyticsService.getAnalytics({
        startDate,
        endDate,
        providerId
      });

      // Return analytics
      res.json({
        success: true,
        analytics
      });
    } catch (error) {
      logger.error(`Error getting analytics: ${error.message}`, {
        error
      });

      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  // Get cache stats
  router.get('/cache/stats', async (req, res) => {
    try {
      const { startDate, endDate } = req.query;

      // Get cache stats
      const stats = await llmRotationSystem.cacheService.getStats({
        startDate,
        endDate
      });

      // Return cache stats
      res.json({
        success: true,
        stats
      });
    } catch (error) {
      logger.error(`Error getting cache stats: ${error.message}`, {
        error
      });

      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  // Clear cache
  router.post('/cache/clear', async (req, res) => {
    try {
      // Clear cache
      await llmRotationSystem.cacheService.clear();

      // Return success
      res.json({
        success: true
      });
    } catch (error) {
      logger.error(`Error clearing cache: ${error.message}`, {
        error
      });

      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  // Get learning parameters
  router.get('/learning', async (req, res) => {
    try {
      // Get learning parameters
      const learningRate = llmRotationSystem.learningService.getLearningRate();
      const explorationRate = llmRotationSystem.learningService.getExplorationRate();

      // Return learning parameters
      res.json({
        success: true,
        learningRate,
        explorationRate
      });
    } catch (error) {
      logger.error(`Error getting learning parameters: ${error.message}`, {
        error
      });

      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  // Update learning parameters
  router.post('/learning', validateRequest(Joi.object({
    learningRate: Joi.number().min(0).max(1),
    explorationRate: Joi.number().min(0).max(1)
  })), async (req, res) => {
    try {
      // Update learning parameters
      if (req.body.learningRate !== undefined) {
        llmRotationSystem.learningService.setLearningRate(req.body.learningRate);
      }

      if (req.body.explorationRate !== undefined) {
        llmRotationSystem.learningService.setExplorationRate(req.body.explorationRate);
      }

      // Return updated parameters
      res.json({
        success: true,
        learningRate: llmRotationSystem.learningService.getLearningRate(),
        explorationRate: llmRotationSystem.learningService.getExplorationRate()
      });
    } catch (error) {
      logger.error(`Error updating learning parameters: ${error.message}`, {
        error
      });

      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  // Get system configuration
  router.get('/config', async (req, res) => {
    try {
      // Return system configuration
      res.json({
        success: true,
        defaultStrategy: llmRotationSystem.rotationStrategyManager.defaultStrategy,
        cacheEnabled: llmRotationSystem.cacheService.enabled,
        cacheTtl: llmRotationSystem.cacheService.ttl,
        learningRate: llmRotationSystem.learningService.getLearningRate(),
        explorationRate: llmRotationSystem.learningService.getExplorationRate()
      });
    } catch (error) {
      logger.error(`Error getting system configuration: ${error.message}`, {
        error
      });

      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  // Update system configuration
  router.post('/config', validateRequest(Joi.object({
    defaultStrategy: Joi.string(),
    cacheEnabled: Joi.boolean(),
    cacheTtl: Joi.number().min(0),
    learningRate: Joi.number().min(0).max(1),
    explorationRate: Joi.number().min(0).max(1)
  })), async (req, res) => {
    try {
      // Update system configuration
      if (req.body.defaultStrategy !== undefined) {
        llmRotationSystem.rotationStrategyManager.defaultStrategy = req.body.defaultStrategy;
      }

      if (req.body.cacheEnabled !== undefined) {
        llmRotationSystem.cacheService.enabled = req.body.cacheEnabled;
      }

      if (req.body.cacheTtl !== undefined) {
        llmRotationSystem.cacheService.ttl = req.body.cacheTtl;
      }

      if (req.body.learningRate !== undefined) {
        llmRotationSystem.learningService.setLearningRate(req.body.learningRate);
      }

      if (req.body.explorationRate !== undefined) {
        llmRotationSystem.learningService.setExplorationRate(req.body.explorationRate);
      }

      // Return updated configuration
      res.json({
        success: true,
        defaultStrategy: llmRotationSystem.rotationStrategyManager.defaultStrategy,
        cacheEnabled: llmRotationSystem.cacheService.enabled,
        cacheTtl: llmRotationSystem.cacheService.ttl,
        learningRate: llmRotationSystem.learningService.getLearningRate(),
        explorationRate: llmRotationSystem.learningService.getExplorationRate()
      });
    } catch (error) {
      logger.error(`Error updating system configuration: ${error.message}`, {
        error
      });

      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  // Register routes
  app.use('/api/llm-rotation', router);

  // Initialize LLM rotation system
  llmRotationSystem.initialize()
    .then(() => {
      logger.info('LLM rotation system initialized');
    })
    .catch(error => {
      logger.error(`Error initializing LLM rotation system: ${error.message}`, {
        error
      });
    });

  // Patch LLM service to use rotation system
  if (llmService && options.patchLLMService !== false) {
    const originalGenerateText = llmService.generateText;

    llmService.generateText = async function(task) {
      // Process task with rotation system
      const result = await llmRotationSystem.processTask(task);

      // Return response
      return result.response.response;
    };

    logger.info('LLM service patched to use rotation system');
  }

  return {
    llmRotationSystem,
    router
  };
}

module.exports = {
  integrateLLMRotationSystem
};
