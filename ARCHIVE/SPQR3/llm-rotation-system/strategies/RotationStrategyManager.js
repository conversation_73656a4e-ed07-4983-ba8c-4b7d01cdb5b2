/**
 * Rotation Strategy Manager
 * 
 * Manages rotation strategies for LLM providers.
 */

const { RoundRobinStrategy } = require('./RoundRobinStrategy');
const { WeightedStrategy } = require('./WeightedStrategy');
const { AdaptiveStrategy } = require('./AdaptiveStrategy');
const { CostOptimizedStrategy } = require('./CostOptimizedStrategy');
const { QualityOptimizedStrategy } = require('./QualityOptimizedStrategy');

/**
 * Rotation strategy manager
 */
class RotationStrategyManager {
  /**
   * Create a new rotation strategy manager
   * 
   * @param {Object} options - Manager options
   */
  constructor(options = {}) {
    this.providerRegistry = options.providerRegistry;
    this.taskClassifier = options.taskClassifier;
    this.logger = options.logger || console;
    this.db = options.db;
    this.defaultStrategy = options.defaultStrategy || 'adaptive';
    this.strategies = new Map();
    
    // Register default strategies
    this._registerDefaultStrategies();
  }
  
  /**
   * Register a strategy
   * 
   * @param {String} name - Strategy name
   * @param {Object} strategy - Strategy
   */
  registerStrategy(name, strategy) {
    this.strategies.set(name, strategy);
  }
  
  /**
   * Get a strategy
   * 
   * @param {String} name - Strategy name
   * @returns {Object} Strategy
   */
  getStrategy(name) {
    return this.strategies.get(name);
  }
  
  /**
   * Get all strategies
   * 
   * @returns {Array<Object>} Strategies
   */
  getAllStrategies() {
    return Array.from(this.strategies.entries()).map(([name, strategy]) => ({
      name,
      strategy
    }));
  }
  
  /**
   * Select a provider for a task
   * 
   * @param {Object} task - Task to execute
   * @param {Object} classification - Task classification
   * @param {String} strategyName - Strategy name
   * @returns {Promise<Object>} Selected provider
   */
  async selectProvider(task, classification, strategyName = this.defaultStrategy) {
    const strategy = this.getStrategy(strategyName);
    
    if (!strategy) {
      throw new Error(`Unknown strategy: ${strategyName}`);
    }
    
    return strategy.selectProvider(task, classification);
  }
  
  /**
   * Register default strategies
   * 
   * @private
   */
  _registerDefaultStrategies() {
    // Round-robin strategy
    this.registerStrategy('round-robin', new RoundRobinStrategy({
      providerRegistry: this.providerRegistry,
      logger: this.logger
    }));
    
    // Weighted strategy
    this.registerStrategy('weighted', new WeightedStrategy({
      providerRegistry: this.providerRegistry,
      logger: this.logger
    }));
    
    // Adaptive strategy
    this.registerStrategy('adaptive', new AdaptiveStrategy({
      providerRegistry: this.providerRegistry,
      taskClassifier: this.taskClassifier,
      logger: this.logger,
      db: this.db
    }));
    
    // Cost-optimized strategy
    this.registerStrategy('cost-optimized', new CostOptimizedStrategy({
      providerRegistry: this.providerRegistry,
      taskClassifier: this.taskClassifier,
      logger: this.logger
    }));
    
    // Quality-optimized strategy
    this.registerStrategy('quality-optimized', new QualityOptimizedStrategy({
      providerRegistry: this.providerRegistry,
      taskClassifier: this.taskClassifier,
      logger: this.logger
    }));
  }
}

module.exports = {
  RotationStrategyManager
};
