/**
 * Round-Robin Strategy
 * 
 * Selects providers in a round-robin fashion.
 */

/**
 * Round-robin strategy
 */
class RoundRobinStrategy {
  /**
   * Create a new round-robin strategy
   * 
   * @param {Object} options - Strategy options
   */
  constructor(options = {}) {
    this.providerRegistry = options.providerRegistry;
    this.logger = options.logger || console;
    this.currentIndex = 0;
  }
  
  /**
   * Select a provider for a task
   * 
   * @param {Object} task - Task to execute
   * @param {Object} classification - Task classification
   * @returns {Promise<Object>} Selected provider
   */
  async selectProvider(task, classification) {
    // Get active providers
    const providers = this.providerRegistry.getActiveProviders();
    
    if (providers.length === 0) {
      throw new Error('No active providers available');
    }
    
    // Get providers for the recommended tier
    const recommendedTier = classification.getRecommendedTier();
    let tierProviders = providers.filter(provider => provider.tier === recommendedTier);
    
    // If no providers for the recommended tier, use all providers
    if (tierProviders.length === 0) {
      tierProviders = providers;
    }
    
    // Filter out rate-limited providers
    const availableProviders = tierProviders.filter(provider => !provider.isRateLimited());
    
    // If no available providers, use all tier providers
    const candidateProviders = availableProviders.length > 0 ? availableProviders : tierProviders;
    
    // Select provider in round-robin fashion
    const selected = candidateProviders[this.currentIndex % candidateProviders.length];
    
    // Update index for next selection
    this.currentIndex = (this.currentIndex + 1) % candidateProviders.length;
    
    this.logger.info(`Selected provider: ${selected.name} (round-robin)`);
    
    return selected;
  }
}

module.exports = {
  RoundRobinStrategy
};
