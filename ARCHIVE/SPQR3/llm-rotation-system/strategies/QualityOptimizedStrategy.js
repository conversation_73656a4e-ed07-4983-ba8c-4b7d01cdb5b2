/**
 * Quality-Optimized Strategy
 * 
 * Selects providers based on quality optimization.
 */

/**
 * Quality-optimized strategy
 */
class QualityOptimizedStrategy {
  /**
   * Create a new quality-optimized strategy
   * 
   * @param {Object} options - Strategy options
   */
  constructor(options = {}) {
    this.providerRegistry = options.providerRegistry;
    this.taskClassifier = options.taskClassifier;
    this.logger = options.logger || console;
    
    // Maximum cost threshold
    this.maxCostThreshold = options.maxCostThreshold || 0.01;
  }
  
  /**
   * Select a provider for a task
   * 
   * @param {Object} task - Task to execute
   * @param {Object} classification - Task classification
   * @returns {Promise<Object>} Selected provider
   */
  async selectProvider(task, classification) {
    // Get active providers
    const providers = this.providerRegistry.getActiveProviders();
    
    if (providers.length === 0) {
      throw new Error('No active providers available');
    }
    
    // Get providers for the recommended tier
    const recommendedTier = classification.getRecommendedTier();
    let tierProviders = providers.filter(provider => provider.tier === recommendedTier);
    
    // If no providers for the recommended tier, use all providers
    if (tierProviders.length === 0) {
      tierProviders = providers;
    }
    
    // Filter providers by maximum cost threshold
    const affordableProviders = tierProviders.filter(provider => {
      const avgCost = (provider.costPerToken.input + provider.costPerToken.output) / 2;
      return avgCost <= this.maxCostThreshold;
    });
    
    // If no affordable providers, use all tier providers
    const candidateProviders = affordableProviders.length > 0 ? affordableProviders : tierProviders;
    
    // Sort providers by quality score (descending)
    candidateProviders.sort((a, b) => b.qualityScore - a.qualityScore);
    
    // Select provider with highest quality score
    const selected = candidateProviders[0];
    
    this.logger.info(`Selected provider: ${selected.name} (quality-optimized)`);
    
    return selected;
  }
}

module.exports = {
  QualityOptimizedStrategy
};
