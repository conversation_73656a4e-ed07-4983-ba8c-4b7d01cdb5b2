/**
 * Weighted Strategy
 * 
 * Selects providers based on weighted scores.
 */

/**
 * Weighted strategy
 */
class WeightedStrategy {
  /**
   * Create a new weighted strategy
   * 
   * @param {Object} options - Strategy options
   */
  constructor(options = {}) {
    this.providerRegistry = options.providerRegistry;
    this.logger = options.logger || console;
    
    // Weights for different factors
    this.weights = options.weights || {
      quality: 0.4,
      reliability: 0.3,
      speed: 0.2,
      cost: 0.1
    };
  }
  
  /**
   * Select a provider for a task
   * 
   * @param {Object} task - Task to execute
   * @param {Object} classification - Task classification
   * @returns {Promise<Object>} Selected provider
   */
  async selectProvider(task, classification) {
    // Get active providers
    const providers = this.providerRegistry.getActiveProviders();
    
    if (providers.length === 0) {
      throw new Error('No active providers available');
    }
    
    // Get providers for the recommended tier
    const recommendedTier = classification.getRecommendedTier();
    let tierProviders = providers.filter(provider => provider.tier === recommendedTier);
    
    // If no providers for the recommended tier, use all providers
    if (tierProviders.length === 0) {
      tierProviders = providers;
    }
    
    // Filter out rate-limited providers
    const availableProviders = tierProviders.filter(provider => !provider.isRateLimited());
    
    // If no available providers, use all tier providers
    const candidateProviders = availableProviders.length > 0 ? availableProviders : tierProviders;
    
    // Calculate weighted scores for each provider
    const providerScores = candidateProviders.map(provider => {
      // Calculate cost score (lower cost = higher score)
      const costPerToken = (provider.costPerToken.input + provider.costPerToken.output) / 2;
      const maxCost = Math.max(...candidateProviders.map(p => (p.costPerToken.input + p.costPerToken.output) / 2));
      const costScore = maxCost > 0 ? 1 - (costPerToken / maxCost) : 1;
      
      // Calculate weighted score
      const weightedScore = (
        (provider.qualityScore * this.weights.quality) +
        (provider.reliabilityScore * this.weights.reliability) +
        (provider.speedScore * this.weights.speed) +
        (costScore * this.weights.cost)
      );
      
      return {
        provider,
        score: weightedScore
      };
    });
    
    // Sort providers by score (descending)
    providerScores.sort((a, b) => b.score - a.score);
    
    // Select provider with highest score
    const selected = providerScores[0].provider;
    
    this.logger.info(`Selected provider: ${selected.name} (weighted, score: ${providerScores[0].score.toFixed(2)})`);
    
    return selected;
  }
}

module.exports = {
  WeightedStrategy
};
