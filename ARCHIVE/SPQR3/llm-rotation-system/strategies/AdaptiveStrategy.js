/**
 * Adaptive Strategy
 * 
 * Adapts provider selection based on historical performance.
 */

/**
 * Adaptive strategy
 */
class AdaptiveStrategy {
  /**
   * Create a new adaptive strategy
   * 
   * @param {Object} options - Strategy options
   */
  constructor(options = {}) {
    this.providerRegistry = options.providerRegistry;
    this.taskClassifier = options.taskClassifier;
    this.logger = options.logger || console;
    this.db = options.db;
    
    // Performance history
    this.performanceHistory = new Map();
    
    // Learning rate
    this.learningRate = options.learningRate || 0.1;
    
    // Exploration rate
    this.explorationRate = options.explorationRate || 0.1;
  }
  
  /**
   * Select a provider for a task
   * 
   * @param {Object} task - Task to execute
   * @param {Object} classification - Task classification
   * @returns {Promise<Object>} Selected provider
   */
  async selectProvider(task, classification) {
    // Get active providers
    const providers = this.providerRegistry.getActiveProviders();
    
    if (providers.length === 0) {
      throw new Error('No active providers available');
    }
    
    // Get providers for the recommended tier
    const recommendedTier = classification.getRecommendedTier();
    let tierProviders = providers.filter(provider => provider.tier === recommendedTier);
    
    // If no providers for the recommended tier, use all providers
    if (tierProviders.length === 0) {
      tierProviders = providers;
    }
    
    // Filter out rate-limited providers
    const availableProviders = tierProviders.filter(provider => !provider.isRateLimited());
    
    // If no available providers, use all tier providers
    const candidateProviders = availableProviders.length > 0 ? availableProviders : tierProviders;
    
    // Decide whether to explore or exploit
    const shouldExplore = Math.random() < this.explorationRate;
    
    if (shouldExplore) {
      // Exploration: select a random provider
      const randomIndex = Math.floor(Math.random() * candidateProviders.length);
      const selected = candidateProviders[randomIndex];
      
      this.logger.info(`Selected provider: ${selected.name} (adaptive, exploration)`);
      
      return selected;
    }
    
    // Exploitation: select the best provider for this task type
    const taskType = this._getTaskType(classification);
    
    // Get performance history for this task type
    let taskPerformance = this.performanceHistory.get(taskType);
    
    if (!taskPerformance) {
      // Initialize performance history for this task type
      taskPerformance = new Map();
      
      for (const provider of candidateProviders) {
        // Initialize with provider's quality score
        taskPerformance.set(provider.id, provider.qualityScore);
      }
      
      this.performanceHistory.set(taskType, taskPerformance);
    }
    
    // Calculate scores for each provider
    const providerScores = candidateProviders.map(provider => {
      // Get performance score for this provider
      const performanceScore = taskPerformance.get(provider.id) || provider.qualityScore;
      
      return {
        provider,
        score: performanceScore
      };
    });
    
    // Sort providers by score (descending)
    providerScores.sort((a, b) => b.score - a.score);
    
    // Select provider with highest score
    const selected = providerScores[0].provider;
    
    this.logger.info(`Selected provider: ${selected.name} (adaptive, exploitation, score: ${providerScores[0].score.toFixed(2)})`);
    
    return selected;
  }
  
  /**
   * Update performance history
   * 
   * @param {String} providerId - Provider ID
   * @param {Object} classification - Task classification
   * @param {Number} score - Performance score
   */
  updatePerformance(providerId, classification, score) {
    const taskType = this._getTaskType(classification);
    
    // Get performance history for this task type
    let taskPerformance = this.performanceHistory.get(taskType);
    
    if (!taskPerformance) {
      // Initialize performance history for this task type
      taskPerformance = new Map();
      this.performanceHistory.set(taskType, taskPerformance);
    }
    
    // Get current score
    const currentScore = taskPerformance.get(providerId) || 0.5;
    
    // Update score with exponential moving average
    const newScore = (this.learningRate * score) + ((1 - this.learningRate) * currentScore);
    
    // Save new score
    taskPerformance.set(providerId, newScore);
    
    this.logger.debug(`Updated performance for provider ${providerId} on task type ${taskType}: ${newScore.toFixed(2)}`);
  }
  
  /**
   * Get task type from classification
   * 
   * @param {Object} classification - Task classification
   * @returns {String} Task type
   * @private
   */
  _getTaskType(classification) {
    // Determine task type based on classification
    if (classification.creativity > 0.7) {
      return 'creative';
    } else if (classification.factuality > 0.7) {
      return 'factual';
    } else if (classification.complexity > 0.7) {
      return 'complex';
    } else {
      return 'general';
    }
  }
}

module.exports = {
  AdaptiveStrategy
};
