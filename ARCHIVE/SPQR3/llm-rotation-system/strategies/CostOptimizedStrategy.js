/**
 * Cost-Optimized Strategy
 * 
 * Selects providers based on cost optimization.
 */

/**
 * Cost-optimized strategy
 */
class CostOptimizedStrategy {
  /**
   * Create a new cost-optimized strategy
   * 
   * @param {Object} options - Strategy options
   */
  constructor(options = {}) {
    this.providerRegistry = options.providerRegistry;
    this.taskClassifier = options.taskClassifier;
    this.logger = options.logger || console;
    
    // Minimum quality threshold
    this.minQualityThreshold = options.minQualityThreshold || 0.6;
  }
  
  /**
   * Select a provider for a task
   * 
   * @param {Object} task - Task to execute
   * @param {Object} classification - Task classification
   * @returns {Promise<Object>} Selected provider
   */
  async selectProvider(task, classification) {
    // Get active providers
    const providers = this.providerRegistry.getActiveProviders();
    
    if (providers.length === 0) {
      throw new Error('No active providers available');
    }
    
    // Get providers for the recommended tier
    const recommendedTier = classification.getRecommendedTier();
    let tierProviders = providers.filter(provider => provider.tier === recommendedTier);
    
    // If no providers for the recommended tier, use all providers
    if (tierProviders.length === 0) {
      tierProviders = providers;
    }
    
    // Filter providers by minimum quality threshold
    const qualifiedProviders = tierProviders.filter(provider => provider.qualityScore >= this.minQualityThreshold);
    
    // If no qualified providers, use all tier providers
    const candidateProviders = qualifiedProviders.length > 0 ? qualifiedProviders : tierProviders;
    
    // Sort providers by cost (ascending)
    candidateProviders.sort((a, b) => {
      const aCost = (a.costPerToken.input + a.costPerToken.output) / 2;
      const bCost = (b.costPerToken.input + b.costPerToken.output) / 2;
      return aCost - bCost;
    });
    
    // Select provider with lowest cost
    const selected = candidateProviders[0];
    
    this.logger.info(`Selected provider: ${selected.name} (cost-optimized)`);
    
    return selected;
  }
}

module.exports = {
  CostOptimizedStrategy
};
