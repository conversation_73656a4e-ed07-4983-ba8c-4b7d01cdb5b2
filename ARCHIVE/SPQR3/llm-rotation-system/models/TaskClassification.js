/**
 * Task Classification Model
 * 
 * Represents the classification of a task.
 */

/**
 * Task classification
 */
class TaskClassification {
  /**
   * Create a new task classification
   * 
   * @param {Object} options - Classification options
   */
  constructor(options = {}) {
    this.complexity = options.complexity !== undefined ? options.complexity : 0.5; // 0-1 score
    this.importance = options.importance !== undefined ? options.importance : 0.5; // 0-1 score
    this.creativity = options.creativity !== undefined ? options.creativity : 0.5; // 0-1 score
    this.factuality = options.factuality !== undefined ? options.factuality : 0.5; // 0-1 score
    this.sensitivity = options.sensitivity !== undefined ? options.sensitivity : 0.5; // 0-1 score
    this.recommendedTier = options.recommendedTier || this._calculateRecommendedTier();
    this.requiredCapabilities = options.requiredCapabilities || this._calculateRequiredCapabilities();
  }
  
  /**
   * Get recommended tier
   * 
   * @returns {String} Recommended tier
   */
  getRecommendedTier() {
    return this.recommendedTier;
  }
  
  /**
   * Calculate recommended tier
   * 
   * @returns {String} Recommended tier
   * @private
   */
  _calculateRecommendedTier() {
    // Calculate weighted score
    const score = (
      (this.complexity * 0.3) +
      (this.importance * 0.3) +
      (this.creativity * 0.2) +
      (this.factuality * 0.1) +
      (this.sensitivity * 0.1)
    );
    
    // Determine tier based on score
    if (score >= 0.7) {
      return 'premium';
    } else if (score >= 0.4) {
      return 'standard';
    } else {
      return 'basic';
    }
  }
  
  /**
   * Calculate required capabilities
   * 
   * @returns {Array<String>} Required capabilities
   * @private
   */
  _calculateRequiredCapabilities() {
    const capabilities = ['text-generation'];
    
    // Add capabilities based on task characteristics
    if (this.creativity >= 0.7) {
      capabilities.push('creative-writing');
    }
    
    if (this.factuality >= 0.7) {
      capabilities.push('factual-responses');
    }
    
    if (this.complexity >= 0.7) {
      capabilities.push('complex-reasoning');
    }
    
    return capabilities;
  }
  
  /**
   * Convert the classification to a JSON object
   * 
   * @returns {Object} JSON representation of the classification
   */
  toJSON() {
    return {
      complexity: this.complexity,
      importance: this.importance,
      creativity: this.creativity,
      factuality: this.factuality,
      sensitivity: this.sensitivity,
      recommendedTier: this.recommendedTier,
      requiredCapabilities: [...this.requiredCapabilities]
    };
  }
}

module.exports = {
  TaskClassification
};
