/**
 * LLM Provider Model
 *
 * Represents an LLM provider with its models and capabilities.
 */

/**
 * LLM provider
 */
class LLMProvider {
  /**
   * Create a new LLM provider
   *
   * @param {Object} options - Provider options
   */
  constructor(options = {}) {
    this.id = options.id || '';
    this.name = options.name || '';
    this.description = options.description || '';

    // Enhanced API key management
    // Instead of simple array of strings, we now use an array of objects with metadata
    if (Array.isArray(options.apiKeys) && typeof options.apiKeys[0] === 'string') {
      // Convert legacy format to new format
      this.apiKeys = options.apiKeys.map(key => ({
        key,
        status: 'active',
        lastUsed: null,
        usageCount: 0,
        errorCount: 0,
        rateLimitedUntil: null,
        expiresAt: null,
        created: new Date().toISOString(),
        updated: new Date().toISOString(),
        quotaUsed: 0,
        quotaLimit: options.defaultQuotaLimit || 1000000, // Default to 1M tokens
        quotaResetDate: null,
        costIncurred: 0,
        successCount: 0,
        failureCount: 0,
        averageLatency: 0,
        rotationGroup: 'default'
      }));
    } else {
      this.apiKeys = options.apiKeys || [];
    }

    // API key rotation settings
    this.keyRotationSettings = options.keyRotationSettings || {
      enabled: true,
      strategy: 'balanced', // 'balanced', 'sequential', 'least-used', 'round-robin'
      maxErrorRate: 0.2, // 20% error rate threshold
      cooldownPeriod: 60000, // 1 minute cooldown after errors
      quotaThresholdPercent: 80, // Rotate when 80% of quota is used
      rotationGroups: ['default'], // Groups for key rotation
      currentGroupIndex: 0
    };

    this.models = options.models || [];
    this.baseUrl = options.baseUrl || '';
    this.rateLimits = options.rateLimits || {
      requestsPerMinute: 60,
      requestsPerDay: 1000,
      tokensPerMinute: 40000,
      tokensPerDay: 1000000
    };
    this.currentUsage = options.currentUsage || {
      requestsToday: 0,
      tokensToday: 0,
      requestsThisMinute: 0,
      tokensThisMinute: 0,
      lastMinuteTimestamp: Date.now(),
      lastDayTimestamp: Date.now()
    };
    this.tier = options.tier || 'standard'; // premium, standard, basic
    this.status = options.status || 'active'; // active, inactive, rate-limited
    this.capabilities = options.capabilities || [];
    this.costPerToken = options.costPerToken || {
      input: 0.0001,
      output: 0.0002
    };
    this.qualityScore = options.qualityScore || 0.5; // 0-1 score based on quality analysis
    this.reliabilityScore = options.reliabilityScore || 0.5; // 0-1 score based on reliability
    this.speedScore = options.speedScore || 0.5; // 0-1 score based on response speed
    this.created_at = options.created_at || new Date().toISOString();
    this.updated_at = options.updated_at || new Date().toISOString();
  }

  /**
   * Get a random API key
   *
   * @returns {String} API key
   */
  getRandomApiKey() {
    // Get only active API keys
    const activeKeys = this.getActiveApiKeys();

    if (activeKeys.length === 0) {
      return '';
    }

    const index = Math.floor(Math.random() * activeKeys.length);
    const keyObj = activeKeys[index];

    // Update usage statistics
    keyObj.lastUsed = new Date().toISOString();
    keyObj.usageCount++;

    return keyObj.key;
  }

  /**
   * Get all active API keys
   *
   * @returns {Array<Object>} Active API keys
   */
  getActiveApiKeys() {
    const now = new Date();

    return this.apiKeys.filter(keyObj => {
      // Skip inactive keys
      if (keyObj.status !== 'active') {
        return false;
      }

      // Skip expired keys
      if (keyObj.expiresAt && new Date(keyObj.expiresAt) < now) {
        keyObj.status = 'expired';
        return false;
      }

      // Skip rate-limited keys
      if (keyObj.rateLimitedUntil && new Date(keyObj.rateLimitedUntil) > now) {
        return false;
      }

      return true;
    });
  }

  /**
   * Add a new API key
   *
   * @param {String} key - API key
   * @param {Object} options - Key options
   * @returns {Object} Added key object
   */
  addApiKey(key, options = {}) {
    // Check if key already exists
    const existingIndex = this.apiKeys.findIndex(k => k.key === key);

    if (existingIndex >= 0) {
      // Update existing key
      this.apiKeys[existingIndex] = {
        ...this.apiKeys[existingIndex],
        status: options.status || 'active',
        expiresAt: options.expiresAt || this.apiKeys[existingIndex].expiresAt,
        updated: new Date().toISOString()
      };

      return this.apiKeys[existingIndex];
    }

    // Create new key object
    const keyObj = {
      key,
      status: options.status || 'active',
      lastUsed: null,
      usageCount: 0,
      errorCount: 0,
      rateLimitedUntil: null,
      expiresAt: options.expiresAt || null,
      created: new Date().toISOString()
    };

    // Add to keys array
    this.apiKeys.push(keyObj);

    return keyObj;
  }

  /**
   * Remove an API key
   *
   * @param {String} key - API key
   * @returns {Boolean} Whether the key was removed
   */
  removeApiKey(key) {
    const initialLength = this.apiKeys.length;
    this.apiKeys = this.apiKeys.filter(k => k.key !== key);

    return this.apiKeys.length < initialLength;
  }

  /**
   * Mark an API key as invalid
   *
   * @param {String} key - API key
   * @param {String} reason - Reason for marking as invalid
   * @returns {Boolean} Whether the key was marked as invalid
   */
  markApiKeyAsInvalid(key, reason = 'unknown') {
    const keyObj = this.apiKeys.find(k => k.key === key);

    if (!keyObj) {
      return false;
    }

    keyObj.status = 'invalid';
    keyObj.invalidReason = reason;
    keyObj.updated = new Date().toISOString();

    return true;
  }

  /**
   * Mark an API key as rate limited
   *
   * @param {String} key - API key
   * @param {Number} durationMs - Duration in milliseconds
   * @returns {Boolean} Whether the key was marked as rate limited
   */
  markApiKeyAsRateLimited(key, durationMs = 60000) {
    const keyObj = this.apiKeys.find(k => k.key === key);

    if (!keyObj) {
      return false;
    }

    const now = new Date();
    const rateLimitedUntil = new Date(now.getTime() + durationMs);

    keyObj.rateLimitedUntil = rateLimitedUntil.toISOString();
    keyObj.updated = now.toISOString();

    return true;
  }

  /**
   * Record an error for an API key
   *
   * @param {String} key - API key
   * @param {String} errorType - Type of error
   * @returns {Boolean} Whether the error was recorded
   */
  recordApiKeyError(key, errorType = 'unknown') {
    const keyObj = this.apiKeys.find(k => k.key === key);

    if (!keyObj) {
      return false;
    }

    keyObj.errorCount++;
    keyObj.lastError = {
      type: errorType,
      timestamp: new Date().toISOString()
    };
    keyObj.updated = new Date().toISOString();

    // If too many errors, mark as invalid
    if (keyObj.errorCount >= 5 && keyObj.status === 'active') {
      keyObj.status = 'invalid';
      keyObj.invalidReason = 'too_many_errors';
    }

    return true;
  }

  /**
   * Get the best API key based on usage and errors
   *
   * @returns {String} Best API key
   */
  getBestApiKey() {
    const activeKeys = this.getActiveApiKeys();

    if (activeKeys.length === 0) {
      return '';
    }

    // Sort keys by usage count (ascending) and error count (ascending)
    activeKeys.sort((a, b) => {
      // First, prioritize keys with fewer errors
      if (a.errorCount !== b.errorCount) {
        return a.errorCount - b.errorCount;
      }

      // Then, prioritize keys with less usage
      return a.usageCount - b.usageCount;
    });

    // Update usage statistics for the selected key
    const keyObj = activeKeys[0];
    keyObj.lastUsed = new Date().toISOString();
    keyObj.usageCount++;

    return keyObj.key;
  }

  /**
   * Get models by tier
   *
   * @param {String} tier - Model tier
   * @returns {Array<Object>} Models
   */
  getModelsByTier(tier) {
    return this.models.filter(model => model.tier === tier);
  }

  /**
   * Get models by capability
   *
   * @param {String} capability - Capability name
   * @returns {Array<Object>} Models
   */
  getModelsByCapability(capability) {
    return this.models.filter(model => model.capabilities.includes(capability));
  }

  /**
   * Calculate cost for tokens
   *
   * @param {Number} inputTokens - Number of input tokens
   * @param {Number} outputTokens - Number of output tokens
   * @returns {Number} Cost
   */
  calculateCost(inputTokens, outputTokens) {
    return (inputTokens * this.costPerToken.input) + (outputTokens * this.costPerToken.output);
  }

  /**
   * Check if provider is rate limited
   *
   * @returns {Boolean} Whether the provider is rate limited
   */
  isRateLimited() {
    // Check if status is rate-limited
    if (this.status === 'rate-limited') {
      return true;
    }

    // Check if inactive
    if (this.status === 'inactive') {
      return true;
    }

    // Check minute rate limit
    const now = Date.now();
    const minuteElapsed = (now - this.currentUsage.lastMinuteTimestamp) / 1000 / 60;

    if (minuteElapsed < 1) {
      // Still within the same minute
      if (this.currentUsage.requestsThisMinute >= this.rateLimits.requestsPerMinute) {
        return true;
      }

      if (this.currentUsage.tokensThisMinute >= this.rateLimits.tokensPerMinute) {
        return true;
      }
    }

    // Check day rate limit
    const dayElapsed = (now - this.currentUsage.lastDayTimestamp) / 1000 / 60 / 60 / 24;

    if (dayElapsed < 1) {
      // Still within the same day
      if (this.currentUsage.requestsToday >= this.rateLimits.requestsPerDay) {
        return true;
      }

      if (this.currentUsage.tokensToday >= this.rateLimits.tokensPerDay) {
        return true;
      }
    }

    return false;
  }

  /**
   * Update usage statistics
   *
   * @param {Number} tokens - Number of tokens used
   */
  updateUsage(tokens) {
    const now = Date.now();

    // Check if minute has elapsed
    const minuteElapsed = (now - this.currentUsage.lastMinuteTimestamp) / 1000 / 60;

    if (minuteElapsed >= 1) {
      // Reset minute counters
      this.currentUsage.requestsThisMinute = 1;
      this.currentUsage.tokensThisMinute = tokens;
      this.currentUsage.lastMinuteTimestamp = now;
    } else {
      // Increment minute counters
      this.currentUsage.requestsThisMinute++;
      this.currentUsage.tokensThisMinute += tokens;
    }

    // Check if day has elapsed
    const dayElapsed = (now - this.currentUsage.lastDayTimestamp) / 1000 / 60 / 60 / 24;

    if (dayElapsed >= 1) {
      // Reset day counters
      this.currentUsage.requestsToday = 1;
      this.currentUsage.tokensToday = tokens;
      this.currentUsage.lastDayTimestamp = now;
    } else {
      // Increment day counters
      this.currentUsage.requestsToday++;
      this.currentUsage.tokensToday += tokens;
    }
  }

  /**
   * Convert the provider to a JSON object
   *
   * @returns {Object} JSON representation of the provider
   */
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      apiKeys: this.apiKeys.map(keyObj => ({
        // Mask the actual key for security
        key: keyObj.key ? `${keyObj.key.substring(0, 3)}...${keyObj.key.substring(keyObj.key.length - 3)}` : '',
        status: keyObj.status,
        lastUsed: keyObj.lastUsed,
        usageCount: keyObj.usageCount,
        errorCount: keyObj.errorCount,
        rateLimitedUntil: keyObj.rateLimitedUntil,
        expiresAt: keyObj.expiresAt,
        created: keyObj.created,
        updated: keyObj.updated
      })),
      apiKeyStats: {
        total: this.apiKeys.length,
        active: this.getActiveApiKeys().length,
        invalid: this.apiKeys.filter(k => k.status === 'invalid').length,
        expired: this.apiKeys.filter(k => k.status === 'expired').length,
        rateLimited: this.apiKeys.filter(k => k.rateLimitedUntil && new Date(k.rateLimitedUntil) > new Date()).length
      },
      models: this.models,
      baseUrl: this.baseUrl,
      rateLimits: { ...this.rateLimits },
      currentUsage: { ...this.currentUsage },
      tier: this.tier,
      status: this.status,
      capabilities: [...this.capabilities],
      costPerToken: { ...this.costPerToken },
      qualityScore: this.qualityScore,
      reliabilityScore: this.reliabilityScore,
      speedScore: this.speedScore,
      created_at: this.created_at,
      updated_at: this.updated_at
    };
  }
}

module.exports = {
  LLMProvider
};
