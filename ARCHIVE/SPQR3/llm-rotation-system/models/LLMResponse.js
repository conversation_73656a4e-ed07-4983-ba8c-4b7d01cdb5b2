/**
 * LLM Response Model
 * 
 * Represents a response from an LLM provider.
 */

/**
 * LLM response
 */
class LLMResponse {
  /**
   * Create a new LLM response
   * 
   * @param {Object} options - Response options
   */
  constructor(options = {}) {
    this.id = options.id || '';
    this.taskId = options.taskId || '';
    this.providerId = options.providerId || '';
    this.modelId = options.modelId || '';
    this.prompt = options.prompt || '';
    this.response = options.response || '';
    this.inputTokens = options.inputTokens || 0;
    this.outputTokens = options.outputTokens || 0;
    this.cost = options.cost || 0;
    this.startTime = options.startTime || new Date().toISOString();
    this.endTime = options.endTime || new Date().toISOString();
    this.duration = options.duration || this.calculateDuration();
    this.success = options.success !== undefined ? options.success : true;
    this.error = options.error || '';
    this.metadata = options.metadata || {};
  }
  
  /**
   * Calculate duration in milliseconds
   * 
   * @returns {Number} Duration in milliseconds
   */
  calculateDuration() {
    const start = new Date(this.startTime).getTime();
    const end = new Date(this.endTime).getTime();
    
    return end - start;
  }
  
  /**
   * Convert the response to a JSON object
   * 
   * @returns {Object} JSON representation of the response
   */
  toJSON() {
    return {
      id: this.id,
      taskId: this.taskId,
      providerId: this.providerId,
      modelId: this.modelId,
      prompt: this.prompt,
      response: this.response,
      inputTokens: this.inputTokens,
      outputTokens: this.outputTokens,
      cost: this.cost,
      startTime: this.startTime,
      endTime: this.endTime,
      duration: this.duration,
      success: this.success,
      error: this.error,
      metadata: { ...this.metadata }
    };
  }
}

module.exports = {
  LLMResponse
};
