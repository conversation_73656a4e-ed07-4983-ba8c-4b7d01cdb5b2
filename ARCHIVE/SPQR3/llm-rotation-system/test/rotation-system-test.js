/**
 * LLM Rotation System Test
 * 
 * Tests for the LLM Rotation System with API key rotation and quality analysis.
 */

const { createLLMRotationSystem, LLMProvider } = require('../index');

// Mock database
const mockDb = {
  query: async () => ({ rows: [] }),
  connect: async () => {},
  end: async () => {}
};

// Mock logger
const mockLogger = {
  info: () => {},
  warn: () => {},
  error: () => {},
  debug: () => {}
};

// Test function
async function testLLMRotationSystem() {
  console.log('Starting LLM Rotation System test...');
  
  // Create rotation system
  const rotationSystem = createLLMRotationSystem({
    logger: mockLogger,
    db: mockDb,
    rotationStrategy: 'performance',
    learningRate: 0.1,
    explorationRate: 0.1,
    costWeight: 0.1,
    qualityWeight: 0.4,
    reliabilityWeight: 0.3,
    speedWeight: 0.2
  });
  
  // Initialize system
  await rotationSystem.initialize();
  
  // Register providers
  const openaiProvider = new LLMProvider({
    name: 'OpenAI',
    endpoint: 'https://api.openai.com/v1',
    models: [
      {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        maxTokens: 4096,
        inputCostPer1000Tokens: 0.0015,
        outputCostPer1000Tokens: 0.002,
        tier: 'standard',
        capabilities: ['text-generation', 'summarization', 'translation']
      },
      {
        id: 'gpt-4',
        name: 'GPT-4',
        maxTokens: 8192,
        inputCostPer1000Tokens: 0.03,
        outputCostPer1000Tokens: 0.06,
        tier: 'premium',
        capabilities: ['text-generation', 'summarization', 'translation', 'code-generation']
      }
    ],
    apiKeys: [
      { key: process.env.OPENAI_API_KEY || 'sk-dummy-key-1' },
      { key: process.env.OPENAI_API_KEY_2 || 'sk-dummy-key-2' }
    ]
  });
  
  const anthropicProvider = new LLMProvider({
    name: 'Anthropic',
    endpoint: 'https://api.anthropic.com/v1',
    models: [
      {
        id: 'claude-2',
        name: 'Claude 2',
        maxTokens: 100000,
        inputCostPer1000Tokens: 0.008,
        outputCostPer1000Tokens: 0.024,
        tier: 'premium',
        capabilities: ['text-generation', 'summarization', 'translation', 'code-generation']
      }
    ],
    apiKeys: [
      { key: process.env.ANTHROPIC_API_KEY || 'sk-ant-dummy-key-1' }
    ]
  });
  
  // Register providers
  await rotationSystem.registerProvider(openaiProvider);
  await rotationSystem.registerProvider(anthropicProvider);
  
  // Get all providers
  const providers = rotationSystem.getAllProviders();
  console.log(`Registered providers: ${providers.map(p => p.name).join(', ')}`);
  
  // Test tasks
  const tasks = [
    {
      prompt: 'Explain quantum computing in simple terms.',
      maxTokens: 500,
      temperature: 0.7
    },
    {
      prompt: 'Write a function in Python to calculate the Fibonacci sequence.',
      maxTokens: 500,
      temperature: 0.7
    },
    {
      prompt: 'Summarize the main points of climate change.',
      maxTokens: 500,
      temperature: 0.7
    },
    {
      prompt: 'Translate "Hello, how are you?" to French.',
      maxTokens: 100,
      temperature: 0.3
    }
  ];
  
  // Process tasks
  console.log('Processing tasks...');
  
  for (const task of tasks) {
    try {
      console.log(`\nProcessing task: "${task.prompt.substring(0, 50)}..."`);
      
      // Process task
      const result = await rotationSystem.processTask(task, {
        useRotationManager: true,
        useCache: false
      });
      
      // Log result
      console.log(`Provider: ${result.provider.name} (${result.provider.model})`);
      console.log(`Task type: ${result.classification.taskType}`);
      console.log(`Response: "${result.response.response.substring(0, 100)}..."`);
      console.log(`Tokens: ${result.response.inputTokens} in, ${result.response.outputTokens} out`);
      console.log(`Cost: $${result.response.cost.toFixed(6)}`);
      console.log(`Duration: ${result.response.duration}ms`);
    } catch (error) {
      console.error(`Error processing task: ${error.message}`);
    }
  }
  
  // Get provider metrics
  console.log('\nProvider metrics:');
  const metrics = rotationSystem.getAllProviderMetrics();
  
  for (const metric of metrics) {
    console.log(`\n${metric.name} (${metric.id}):`);
    console.log(`  Enabled: ${metric.enabled}`);
    console.log(`  Total calls: ${metric.totalCalls}`);
    console.log(`  Success rate: ${(metric.successRate * 100).toFixed(1)}%`);
    console.log(`  Performance:`);
    console.log(`    Quality: ${metric.performance.quality.toFixed(2)}`);
    console.log(`    Reliability: ${metric.performance.reliability.toFixed(2)}`);
    console.log(`    Speed: ${metric.performance.speed.toFixed(2)}`);
    console.log(`    Cost: ${metric.performance.cost.toFixed(2)}`);
    console.log(`    Overall: ${metric.performance.overall.toFixed(2)}`);
  }
  
  // Get learning metrics
  console.log('\nLearning service metrics:');
  const learningMetrics = rotationSystem.learningService.getPerformanceMetrics();
  console.log(`  Total calls: ${learningMetrics.totalCalls}`);
  console.log(`  Successful calls: ${learningMetrics.successfulCalls}`);
  console.log(`  Failed calls: ${learningMetrics.failedCalls}`);
  console.log(`  Average quality score: ${learningMetrics.averageQualityScore.toFixed(2)}`);
  console.log(`  Exploration rate: ${learningMetrics.explorationRate.toFixed(2)}`);
  console.log(`  Learning rate: ${learningMetrics.learningRate.toFixed(2)}`);
  
  console.log('\nTest completed successfully!');
}

// Run test if called directly
if (require.main === module) {
  testLLMRotationSystem().catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
  });
}

module.exports = {
  testLLMRotationSystem
};
