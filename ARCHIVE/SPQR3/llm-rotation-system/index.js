/**
 * LLM Rotation System
 *
 * Main entry point for the LLM Rotation System.
 */

// Core
const { LLMRotationSystem } = require('./core/LLMRotationSystem');
const { ProviderRegistry } = require('./core/ProviderRegistry');
const { TaskClassifier } = require('./core/TaskClassifier');
const { QualityAnalyzer } = require('./core/QualityAnalyzer');
const { ProviderRotationManager } = require('./core/ProviderRotationManager');

// Models
const { LLMProvider } = require('./models/LLMProvider');
const { LLMResponse } = require('./models/LLMResponse');
const { TaskClassification } = require('./models/TaskClassification');

// Strategies
const { RotationStrategyManager } = require('./strategies/RotationStrategyManager');
const { RoundRobinStrategy } = require('./strategies/RoundRobinStrategy');
const { WeightedStrategy } = require('./strategies/WeightedStrategy');
const { AdaptiveStrategy } = require('./strategies/AdaptiveStrategy');
const { CostOptimizedStrategy } = require('./strategies/CostOptimizedStrategy');
const { QualityOptimizedStrategy } = require('./strategies/QualityOptimizedStrategy');

/**
 * Create an LLM rotation system
 *
 * @param {Object} options - System options
 * @returns {Object} LLM rotation system
 */
function createLLMRotationSystem(options = {}) {
  return new LLMRotationSystem(options);
}

module.exports = {
  createLLMRotationSystem,
  LLMRotationSystem,
  ProviderRegistry,
  TaskClassifier,
  QualityAnalyzer,
  ProviderRotationManager,
  LLMProvider,
  LLMResponse,
  TaskClassification,
  RotationStrategyManager,
  RoundRobinStrategy,
  WeightedStrategy,
  AdaptiveStrategy,
  CostOptimizedStrategy,
  QualityOptimizedStrategy
};
