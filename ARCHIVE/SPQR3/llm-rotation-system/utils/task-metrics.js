/**
 * Task-specific quality metrics
 * 
 * Defines metrics for different task types.
 */

// Task-specific quality metrics
const TASK_METRICS = {
  'text-generation': {
    dimensions: ['relevance', 'accuracy', 'completeness', 'coherence', 'helpfulness'],
    weights: { relevance: 0.3, accuracy: 0.2, completeness: 0.2, coherence: 0.15, helpfulness: 0.15 }
  },
  'text-completion': {
    dimensions: ['relevance', 'accuracy', 'completeness', 'coherence', 'helpfulness'],
    weights: { relevance: 0.3, accuracy: 0.2, completeness: 0.2, coherence: 0.15, helpfulness: 0.15 }
  },
  'summarization': {
    dimensions: ['relevance', 'accuracy', 'conciseness', 'completeness', 'coherence'],
    weights: { relevance: 0.2, accuracy: 0.3, conciseness: 0.2, completeness: 0.2, coherence: 0.1 }
  },
  'translation': {
    dimensions: ['accuracy', 'fluency', 'preservation', 'cultural', 'consistency'],
    weights: { accuracy: 0.4, fluency: 0.2, preservation: 0.2, cultural: 0.1, consistency: 0.1 }
  },
  'code-generation': {
    dimensions: ['correctness', 'efficiency', 'readability', 'completeness', 'documentation'],
    weights: { correctness: 0.4, efficiency: 0.2, readability: 0.15, completeness: 0.15, documentation: 0.1 }
  },
  'question-answering': {
    dimensions: ['relevance', 'accuracy', 'completeness', 'conciseness', 'helpfulness'],
    weights: { relevance: 0.3, accuracy: 0.4, completeness: 0.15, conciseness: 0.05, helpfulness: 0.1 }
  },
  'creative-writing': {
    dimensions: ['creativity', 'coherence', 'engagement', 'originality', 'structure'],
    weights: { creativity: 0.3, coherence: 0.2, engagement: 0.2, originality: 0.2, structure: 0.1 }
  },
  'data-analysis': {
    dimensions: ['accuracy', 'insight', 'methodology', 'clarity', 'completeness'],
    weights: { accuracy: 0.3, insight: 0.25, methodology: 0.2, clarity: 0.15, completeness: 0.1 }
  }
};

// Default metrics for unknown task types
const DEFAULT_METRICS = {
  dimensions: ['relevance', 'accuracy', 'completeness', 'coherence', 'helpfulness'],
  weights: { relevance: 0.3, accuracy: 0.2, completeness: 0.2, coherence: 0.15, helpfulness: 0.15 }
};

// Dimension descriptions
const DIMENSION_DESCRIPTIONS = {
  // Common dimensions
  relevance: 'How relevant is the response to the prompt?',
  accuracy: 'How accurate is the information in the response?',
  completeness: 'How complete is the response in addressing all aspects of the prompt?',
  coherence: 'How coherent and well-structured is the response?',
  helpfulness: 'How helpful is the response in addressing the user\'s needs?',
  conciseness: 'How concise and to-the-point is the response?',
  
  // Code-specific dimensions
  correctness: 'Does the code work correctly and solve the problem?',
  efficiency: 'How efficient is the code in terms of time and space complexity?',
  readability: 'How readable and maintainable is the code?',
  documentation: 'How well is the code documented with comments and explanations?',
  
  // Translation-specific dimensions
  fluency: 'How natural and fluent is the translated text?',
  preservation: 'How well does the translation preserve the meaning of the original text?',
  cultural: 'How well does the translation handle cultural nuances?',
  consistency: 'How consistent is the translation in terms of terminology and style?',
  
  // Creative writing dimensions
  creativity: 'How creative and imaginative is the content?',
  engagement: 'How engaging and interesting is the content?',
  originality: 'How original and unique is the content?',
  structure: 'How well-structured is the narrative or content?',
  
  // Data analysis dimensions
  insight: 'How insightful are the conclusions drawn from the data?',
  methodology: 'How sound is the methodology used for analysis?',
  clarity: 'How clearly are the findings presented?'
};

module.exports = {
  TASK_METRICS,
  DEFAULT_METRICS,
  DIMENSION_DESCRIPTIONS
};
