/**
 * Analytics Utility
 * 
 * Provides analytics functionality for the LLM Rotation System.
 */

/**
 * Analytics service
 */
class AnalyticsService {
  /**
   * Create a new analytics service
   * 
   * @param {Object} options - Service options
   */
  constructor(options = {}) {
    this.logger = options.logger || console;
    this.db = options.db;
    this.initialized = false;
    
    // Premium provider costs for comparison
    this.premiumProviderCosts = {
      input: 0.00003,
      output: 0.00006
    };
  }
  
  /**
   * Initialize the service
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }
    
    this.logger.info('Initializing analytics service');
    
    if (this.db) {
      try {
        // Create analytics tables
        await this.db.query(`
          CREATE TABLE IF NOT EXISTS llm_analytics (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            date DATE NOT NULL,
            provider_id UUID NOT NULL,
            model_id TEXT NOT NULL,
            requests_count INTEGER NOT NULL DEFAULT 0,
            success_count INTEGER NOT NULL DEFAULT 0,
            error_count INTEGER NOT NULL DEFAULT 0,
            input_tokens INTEGER NOT NULL DEFAULT 0,
            output_tokens INTEGER NOT NULL DEFAULT 0,
            total_cost FLOAT NOT NULL DEFAULT 0,
            average_quality_score FLOAT,
            average_response_time INTEGER,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
          )
        `);
        
        // Create index on date and provider_id
        await this.db.query(`
          CREATE INDEX IF NOT EXISTS idx_llm_analytics_date ON llm_analytics(date);
          CREATE INDEX IF NOT EXISTS idx_llm_analytics_provider_id ON llm_analytics(provider_id);
        `);
      } catch (error) {
        this.logger.error('Error initializing analytics service', error);
        throw error;
      }
    }
    
    this.initialized = true;
    this.logger.info('Analytics service initialized');
  }
  
  /**
   * Track a response
   * 
   * @param {Object} response - LLM response
   * @param {Object} qualityAnalysis - Quality analysis
   * @returns {Promise<void>}
   */
  async trackResponse(response, qualityAnalysis) {
    await this.initialize();
    
    if (!this.db) {
      return;
    }
    
    try {
      const date = new Date().toISOString().split('T')[0];
      
      // Check if analytics record exists for this date and provider
      const result = await this.db.query(
        `SELECT * FROM llm_analytics 
         WHERE date = $1 AND provider_id = $2 AND model_id = $3`,
        [date, response.providerId, response.modelId]
      );
      
      if (result.rows.length > 0) {
        // Update existing record
        const record = result.rows[0];
        
        await this.db.query(
          `UPDATE llm_analytics SET
            requests_count = requests_count + 1,
            success_count = success_count + $1,
            error_count = error_count + $2,
            input_tokens = input_tokens + $3,
            output_tokens = output_tokens + $4,
            total_cost = total_cost + $5,
            average_quality_score = CASE WHEN $6 IS NULL THEN average_quality_score ELSE (average_quality_score * requests_count + $6) / (requests_count + 1) END,
            average_response_time = CASE WHEN $7 IS NULL THEN average_response_time ELSE (average_response_time * requests_count + $7) / (requests_count + 1) END,
            updated_at = CURRENT_TIMESTAMP
          WHERE id = $8`,
          [
            response.success ? 1 : 0,
            response.success ? 0 : 1,
            response.inputTokens,
            response.outputTokens,
            response.cost,
            qualityAnalysis ? qualityAnalysis.overallScore : null,
            response.duration,
            record.id
          ]
        );
      } else {
        // Create new record
        await this.db.query(
          `INSERT INTO llm_analytics (
            date, provider_id, model_id, requests_count, success_count, error_count,
            input_tokens, output_tokens, total_cost, average_quality_score, average_response_time
          ) VALUES ($1, $2, $3, 1, $4, $5, $6, $7, $8, $9, $10)`,
          [
            date,
            response.providerId,
            response.modelId,
            response.success ? 1 : 0,
            response.success ? 0 : 1,
            response.inputTokens,
            response.outputTokens,
            response.cost,
            qualityAnalysis ? qualityAnalysis.overallScore : null,
            response.duration
          ]
        );
      }
    } catch (error) {
      this.logger.error(`Error tracking response: ${error.message}`, {
        error,
        responseId: response.id
      });
    }
  }
  
  /**
   * Get analytics for a date range
   * 
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Analytics data
   */
  async getAnalytics(options = {}) {
    await this.initialize();
    
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      endDate = new Date().toISOString().split('T')[0],
      providerId = null
    } = options;
    
    if (!this.db) {
      return {
        providers: [],
        totalRequests: 0,
        totalTokens: 0,
        totalCost: 0,
        averageQuality: 0,
        averageResponseTime: 0,
        costSavings: 0,
        dailyStats: [],
        qualityDistribution: {
          excellent: 0,
          good: 0,
          average: 0,
          poor: 0
        }
      };
    }
    
    try {
      // Build query
      let query = `
        SELECT 
          provider_id,
          model_id,
          SUM(requests_count) as requests,
          SUM(success_count) as successes,
          SUM(error_count) as errors,
          SUM(input_tokens) as input_tokens,
          SUM(output_tokens) as output_tokens,
          SUM(total_cost) as cost,
          AVG(average_quality_score) as quality,
          AVG(average_response_time) as response_time
        FROM llm_analytics
        WHERE date BETWEEN $1 AND $2
      `;
      
      const params = [startDate, endDate];
      
      if (providerId) {
        query += ` AND provider_id = $3`;
        params.push(providerId);
      }
      
      query += ` GROUP BY provider_id, model_id`;
      
      // Execute query
      const result = await this.db.query(query, params);
      
      // Get provider details
      const providerIds = [...new Set(result.rows.map(row => row.provider_id))];
      const providersResult = await this.db.query(
        `SELECT id, name FROM llm_providers WHERE id = ANY($1)`,
        [providerIds]
      );
      
      const providerMap = {};
      for (const provider of providersResult.rows) {
        providerMap[provider.id] = provider.name;
      }
      
      // Process results
      const providers = [];
      let totalRequests = 0;
      let totalInputTokens = 0;
      let totalOutputTokens = 0;
      let totalCost = 0;
      let weightedQuality = 0;
      let weightedResponseTime = 0;
      
      for (const row of result.rows) {
        providers.push({
          id: row.provider_id,
          name: providerMap[row.provider_id] || row.provider_id,
          modelId: row.model_id,
          requests: row.requests,
          successes: row.successes,
          errors: row.errors,
          inputTokens: row.input_tokens,
          outputTokens: row.output_tokens,
          cost: row.cost,
          quality: row.quality,
          responseTime: row.response_time
        });
        
        totalRequests += row.requests;
        totalInputTokens += row.input_tokens;
        totalOutputTokens += row.output_tokens;
        totalCost += row.cost;
        weightedQuality += row.quality * row.requests;
        weightedResponseTime += row.response_time * row.requests;
      }
      
      const totalTokens = totalInputTokens + totalOutputTokens;
      const averageQuality = totalRequests > 0 ? weightedQuality / totalRequests : 0;
      const averageResponseTime = totalRequests > 0 ? weightedResponseTime / totalRequests : 0;
      
      // Calculate cost savings (compared to using premium provider for everything)
      const premiumCost = (totalInputTokens * this.premiumProviderCosts.input) + (totalOutputTokens * this.premiumProviderCosts.output);
      const costSavings = Math.max(0, premiumCost - totalCost);
      
      // Get daily stats
      const dailyStatsQuery = `
        SELECT 
          date,
          SUM(requests_count) as requests,
          SUM(input_tokens) as input_tokens,
          SUM(output_tokens) as output_tokens,
          SUM(total_cost) as cost,
          AVG(average_quality_score) as quality,
          AVG(average_response_time) as response_time
        FROM llm_analytics
        WHERE date BETWEEN $1 AND $2
        ${providerId ? ' AND provider_id = $3' : ''}
        GROUP BY date
        ORDER BY date
      `;
      
      const dailyStatsResult = await this.db.query(dailyStatsQuery, params);
      
      const dailyStats = dailyStatsResult.rows.map(row => ({
        date: row.date,
        requests: row.requests,
        tokens: row.input_tokens + row.output_tokens,
        cost: row.cost,
        quality: row.quality,
        responseTime: row.response_time
      }));
      
      // Get quality distribution
      const qualityQuery = `
        SELECT 
          CASE
            WHEN average_quality_score >= 0.9 THEN 'excellent'
            WHEN average_quality_score >= 0.7 THEN 'good'
            WHEN average_quality_score >= 0.5 THEN 'average'
            ELSE 'poor'
          END as quality_level,
          SUM(requests_count) as requests
        FROM llm_analytics
        WHERE date BETWEEN $1 AND $2
        ${providerId ? ' AND provider_id = $3' : ''}
        GROUP BY quality_level
      `;
      
      const qualityResult = await this.db.query(qualityQuery, params);
      
      const qualityDistribution = {
        excellent: 0,
        good: 0,
        average: 0,
        poor: 0
      };
      
      for (const row of qualityResult.rows) {
        qualityDistribution[row.quality_level] = row.requests;
      }
      
      // Calculate percentages
      const totalQualityRequests = Object.values(qualityDistribution).reduce((sum, count) => sum + count, 0);
      
      if (totalQualityRequests > 0) {
        for (const level in qualityDistribution) {
          qualityDistribution[level] = Math.round((qualityDistribution[level] / totalQualityRequests) * 100);
        }
      }
      
      return {
        providers,
        totalRequests,
        totalTokens,
        totalCost,
        averageQuality,
        averageResponseTime,
        costSavings,
        dailyStats,
        qualityDistribution
      };
    } catch (error) {
      this.logger.error(`Error getting analytics: ${error.message}`, {
        error,
        options
      });
      
      throw error;
    }
  }
}

module.exports = {
  AnalyticsService
};
