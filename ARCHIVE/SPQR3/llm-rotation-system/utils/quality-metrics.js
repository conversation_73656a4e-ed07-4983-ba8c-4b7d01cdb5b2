/**
 * Quality Metrics
 * 
 * Utility functions for calculating quality metrics.
 */

/**
 * Calculate code correctness score
 * 
 * @param {String} response - Response text
 * @returns {Number} Correctness score (0-1)
 */
function calculateCodeCorrectnessScore(response) {
  // Check for syntax errors
  const syntaxErrorIndicators = [
    'syntax error', 'undefined variable', 'undefined method',
    'cannot find', 'is not defined', 'error:', 'exception:'
  ];
  
  let errorCount = 0;
  for (const indicator of syntaxErrorIndicators) {
    if (response.toLowerCase().includes(indicator)) {
      errorCount++;
    }
  }
  
  // Check for code structure
  const hasCodeBlocks = response.includes('```') || response.includes('    ') || response.includes('\t');
  const hasFunctions = response.includes('function') || response.includes('def ') || 
                      response.includes('class') || response.includes('method');
  
  // Calculate score
  let score = 1.0;
  
  // Deduct for errors
  score -= errorCount * 0.1;
  
  // Deduct if no code blocks or functions
  if (!hasCodeBlocks) score -= 0.3;
  if (!hasFunctions && response.length > 100) score -= 0.2;
  
  // Clamp to 0-1 range
  return Math.max(0, Math.min(1, score));
}

/**
 * Calculate code efficiency score
 * 
 * @param {String} response - Response text
 * @returns {Number} Efficiency score (0-1)
 */
function calculateCodeEfficiencyScore(response) {
  // Check for efficiency indicators
  const inefficientPatterns = [
    'O(n^2)', 'O(n^3)', 'O(2^n)', 'O(n!)',
    'nested loop', 'for.*for', 'while.*while'
  ];
  
  const efficientPatterns = [
    'O(1)', 'O(log n)', 'O(n)', 'O(n log n)',
    'efficient', 'optimized', 'performance', 'fast'
  ];
  
  // Count occurrences
  let inefficientCount = 0;
  let efficientCount = 0;
  
  for (const pattern of inefficientPatterns) {
    if (new RegExp(pattern, 'i').test(response)) {
      inefficientCount++;
    }
  }
  
  for (const pattern of efficientPatterns) {
    if (new RegExp(pattern, 'i').test(response)) {
      efficientCount++;
    }
  }
  
  // Calculate score
  let score = 0.7; // Default score
  
  score += efficientCount * 0.1;
  score -= inefficientCount * 0.1;
  
  // Clamp to 0-1 range
  return Math.max(0, Math.min(1, score));
}

/**
 * Calculate code readability score
 * 
 * @param {String} response - Response text
 * @returns {Number} Readability score (0-1)
 */
function calculateCodeReadabilityScore(response) {
  // Check for readability indicators
  const readabilityIndicators = [
    'descriptive variable', 'meaningful name', 'comment', 'documentation',
    'indentation', 'spacing', 'readable', 'clean code'
  ];
  
  // Count occurrences
  let readabilityCount = 0;
  
  for (const indicator of readabilityIndicators) {
    if (response.toLowerCase().includes(indicator)) {
      readabilityCount++;
    }
  }
  
  // Check for code formatting
  const hasProperIndentation = /\n(\s{2,}|\t)/.test(response);
  const hasComments = /(\/\/|#|\*\/|''')/.test(response);
  
  // Calculate score
  let score = 0.5; // Default score
  
  score += readabilityCount * 0.05;
  if (hasProperIndentation) score += 0.2;
  if (hasComments) score += 0.2;
  
  // Clamp to 0-1 range
  return Math.max(0, Math.min(1, score));
}

/**
 * Calculate code documentation score
 * 
 * @param {String} response - Response text
 * @returns {Number} Documentation score (0-1)
 */
function calculateCodeDocumentationScore(response) {
  // Check for documentation indicators
  const documentationPatterns = [
    '/\\*\\*.*?\\*/', // JSDoc
    '""".*?"""', // Python docstring
    '///.*?\\n', // Triple slash comments
    '//.*?\\n', // Single line comments
    '#.*?\\n', // Hash comments
    '/\\*.*?\\*/' // Block comments
  ];
  
  // Count documentation lines
  let documentationCount = 0;
  const lines = response.split('\n');
  
  for (const line of lines) {
    for (const pattern of documentationPatterns) {
      if (new RegExp(pattern, 's').test(line)) {
        documentationCount++;
        break;
      }
    }
  }
  
  // Calculate ratio of documentation to code
  const documentationRatio = documentationCount / Math.max(1, lines.length);
  
  // Calculate score
  let score = 0;
  
  if (documentationRatio < 0.05) {
    score = 0.2; // Very little documentation
  } else if (documentationRatio < 0.1) {
    score = 0.4; // Some documentation
  } else if (documentationRatio < 0.2) {
    score = 0.6; // Moderate documentation
  } else if (documentationRatio < 0.3) {
    score = 0.8; // Good documentation
  } else {
    score = 1.0; // Excellent documentation
  }
  
  return score;
}

/**
 * Calculate conciseness score
 * 
 * @param {String} response - Response text
 * @param {String} prompt - Prompt text
 * @returns {Number} Conciseness score (0-1)
 */
function calculateConciseness(response, prompt) {
  // For summarization, a good summary should be shorter than the original
  const promptLength = prompt.length;
  const responseLength = response.length;
  
  // Calculate ratio of response length to prompt length
  const ratio = responseLength / promptLength;
  
  // Ideal ratio for summaries is around 0.3-0.5
  if (ratio < 0.1) {
    // Too short, might be missing important information
    return 0.5;
  } else if (ratio < 0.3) {
    // Good conciseness
    return 0.9;
  } else if (ratio < 0.5) {
    // Still good
    return 0.8;
  } else if (ratio < 0.7) {
    // Getting verbose
    return 0.6;
  } else if (ratio < 1.0) {
    // Not very concise
    return 0.4;
  } else {
    // Longer than the original, poor conciseness
    return 0.2;
  }
}

/**
 * Calculate fluency score
 * 
 * @param {String} response - Response text
 * @returns {Number} Fluency score (0-1)
 */
function calculateFluencyScore(response) {
  // Split response into sentences
  const sentences = response.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0);
  
  // Check for very short or very long sentences
  const shortSentences = sentences.filter(sentence => sentence.length < 20).length;
  const longSentences = sentences.filter(sentence => sentence.length > 200).length;
  
  // Calculate ratios
  const shortRatio = shortSentences / Math.max(1, sentences.length);
  const longRatio = longSentences / Math.max(1, sentences.length);
  
  // Check for repeated words or phrases
  const words = response.toLowerCase().split(/\s+/);
  const wordCounts = {};
  
  for (const word of words) {
    if (word.length > 3) { // Only count meaningful words
      wordCounts[word] = (wordCounts[word] || 0) + 1;
    }
  }
  
  // Count words that appear too frequently
  const repeatedWords = Object.values(wordCounts).filter(count => count > 3).length;
  const repeatedRatio = repeatedWords / Math.max(1, Object.keys(wordCounts).length);
  
  // Calculate fluency score
  let score = 1.0;
  
  // Deduct for poor sentence structure
  score -= shortRatio * 0.3; // Too many short sentences
  score -= longRatio * 0.4; // Too many long sentences
  
  // Deduct for repetition
  score -= repeatedRatio * 0.5;
  
  // Clamp to 0-1 range
  return Math.max(0, Math.min(1, score));
}

/**
 * Calculate consistency score
 * 
 * @param {String} response - Response text
 * @returns {Number} Consistency score (0-1)
 */
function calculateConsistencyScore(response) {
  // Split response into paragraphs
  const paragraphs = response.split('\n\n').filter(p => p.trim().length > 0);
  
  // Check for consistent style and terminology
  const styles = [];
  
  // Check for first/third person consistency
  const firstPerson = /\b(I|we|our|us)\b/gi;
  const thirdPerson = /\b(he|she|they|their|them|it|its)\b/gi;
  
  let firstPersonCount = 0;
  let thirdPersonCount = 0;
  
  for (const paragraph of paragraphs) {
    const firstMatches = paragraph.match(firstPerson) || [];
    const thirdMatches = paragraph.match(thirdPerson) || [];
    
    firstPersonCount += firstMatches.length;
    thirdPersonCount += thirdMatches.length;
    
    // Determine style of paragraph
    if (firstMatches.length > thirdMatches.length) {
      styles.push('first-person');
    } else if (thirdMatches.length > firstMatches.length) {
      styles.push('third-person');
    } else {
      styles.push('neutral');
    }
  }
  
  // Calculate style consistency
  const uniqueStyles = new Set(styles).size;
  const styleConsistency = 1 - (uniqueStyles - 1) / Math.max(1, styles.length);
  
  // Calculate overall consistency score
  return styleConsistency;
}

/**
 * Calculate creativity score
 * 
 * @param {String} response - Response text
 * @returns {Number} Creativity score (0-1)
 */
function calculateCreativityScore(response) {
  // Check for creative language indicators
  const creativeIndicators = [
    'metaphor', 'simile', 'analogy', 'imagery', 'vivid',
    'unique', 'original', 'innovative', 'imaginative', 'creative'
  ];
  
  // Count creative indicators
  let creativeCount = 0;
  
  for (const indicator of creativeIndicators) {
    if (response.toLowerCase().includes(indicator)) {
      creativeCount++;
    }
  }
  
  // Check for sentence variety
  const sentences = response.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0);
  const sentenceLengths = sentences.map(sentence => sentence.length);
  
  // Calculate standard deviation of sentence lengths
  const avgLength = sentenceLengths.reduce((sum, length) => sum + length, 0) / sentenceLengths.length;
  const variance = sentenceLengths.reduce((sum, length) => sum + Math.pow(length - avgLength, 2), 0) / sentenceLengths.length;
  const stdDev = Math.sqrt(variance);
  
  // Normalize standard deviation (higher is better for creativity)
  const normalizedStdDev = Math.min(1, stdDev / 50);
  
  // Calculate creativity score
  const score = 0.4 + (creativeCount * 0.05) + (normalizedStdDev * 0.3);
  
  // Clamp to 0-1 range
  return Math.max(0, Math.min(1, score));
}

/**
 * Calculate engagement score
 * 
 * @param {String} response - Response text
 * @returns {Number} Engagement score (0-1)
 */
function calculateEngagementScore(response) {
  // Check for engagement indicators
  const engagementIndicators = [
    'you', 'your', 'imagine', 'consider', 'think about',
    'question', '?', 'interesting', 'fascinating', 'surprising'
  ];
  
  // Count engagement indicators
  let engagementCount = 0;
  
  for (const indicator of engagementIndicators) {
    const regex = new RegExp(`\\b${indicator}\\b`, 'gi');
    const matches = response.match(regex) || [];
    engagementCount += matches.length;
  }
  
  // Normalize engagement count
  const normalizedCount = Math.min(1, engagementCount / 10);
  
  // Calculate engagement score
  const score = 0.5 + (normalizedCount * 0.5);
  
  return score;
}

/**
 * Calculate originality score
 * 
 * @param {String} response - Response text
 * @param {String} prompt - Prompt text
 * @returns {Number} Originality score (0-1)
 */
function calculateOriginalityScore(response, prompt) {
  // Check how much of the response is directly from the prompt
  const promptWords = new Set(prompt.toLowerCase().split(/\s+/).filter(word => word.length > 3));
  const responseWords = response.toLowerCase().split(/\s+/).filter(word => word.length > 3);
  
  let commonWordCount = 0;
  
  for (const word of responseWords) {
    if (promptWords.has(word)) {
      commonWordCount++;
    }
  }
  
  // Calculate ratio of common words to total response words
  const commonRatio = commonWordCount / Math.max(1, responseWords.length);
  
  // Calculate originality score (inverse of common ratio)
  const score = 1 - Math.min(1, commonRatio * 2);
  
  return score;
}

/**
 * Calculate structure score
 * 
 * @param {String} response - Response text
 * @returns {Number} Structure score (0-1)
 */
function calculateStructureScore(response) {
  // Check for structural elements
  const structuralIndicators = [
    '\n\n', // Paragraph breaks
    'first', 'second', 'third', 'finally', // Sequencing
    'introduction', 'conclusion', // Formal structure
    'beginning', 'middle', 'end', // Narrative structure
    'chapter', 'section', 'part' // Sectioning
  ];
  
  // Count structural indicators
  let structureCount = 0;
  
  for (const indicator of structuralIndicators) {
    const regex = new RegExp(`\\b${indicator}\\b`, 'gi');
    const matches = response.match(regex) || [];
    structureCount += matches.length;
  }
  
  // Count paragraphs
  const paragraphs = response.split('\n\n').filter(p => p.trim().length > 0);
  
  // Calculate structure score
  let score = 0.3; // Base score
  
  // Add for structural indicators
  score += Math.min(0.3, structureCount * 0.05);
  
  // Add for good paragraph structure (not too few, not too many)
  const paragraphCount = paragraphs.length;
  if (paragraphCount >= 3 && paragraphCount <= 10) {
    score += 0.4;
  } else if (paragraphCount > 10) {
    score += 0.3;
  } else if (paragraphCount === 2) {
    score += 0.2;
  } else {
    score += 0.1;
  }
  
  return score;
}

/**
 * Calculate insight score
 * 
 * @param {String} response - Response text
 * @returns {Number} Insight score (0-1)
 */
function calculateInsightScore(response) {
  // Check for insight indicators
  const insightIndicators = [
    'insight', 'analysis', 'conclusion', 'finding', 'result',
    'implication', 'suggest', 'indicate', 'reveal', 'show',
    'pattern', 'trend', 'correlation', 'causation', 'relationship'
  ];
  
  // Count insight indicators
  let insightCount = 0;
  
  for (const indicator of insightIndicators) {
    const regex = new RegExp(`\\b${indicator}\\b`, 'gi');
    const matches = response.match(regex) || [];
    insightCount += matches.length;
  }
  
  // Normalize insight count
  const normalizedCount = Math.min(1, insightCount / 10);
  
  // Calculate insight score
  const score = 0.4 + (normalizedCount * 0.6);
  
  return score;
}

/**
 * Calculate methodology score
 * 
 * @param {String} response - Response text
 * @returns {Number} Methodology score (0-1)
 */
function calculateMethodologyScore(response) {
  // Check for methodology indicators
  const methodologyIndicators = [
    'method', 'approach', 'technique', 'procedure', 'process',
    'analysis', 'calculation', 'computation', 'algorithm', 'formula',
    'statistical', 'quantitative', 'qualitative', 'measure', 'metric'
  ];
  
  // Count methodology indicators
  let methodologyCount = 0;
  
  for (const indicator of methodologyIndicators) {
    const regex = new RegExp(`\\b${indicator}\\b`, 'gi');
    const matches = response.match(regex) || [];
    methodologyCount += matches.length;
  }
  
  // Normalize methodology count
  const normalizedCount = Math.min(1, methodologyCount / 8);
  
  // Calculate methodology score
  const score = 0.3 + (normalizedCount * 0.7);
  
  return score;
}

/**
 * Calculate clarity score
 * 
 * @param {String} response - Response text
 * @returns {Number} Clarity score (0-1)
 */
function calculateClarityScore(response) {
  // Check for clarity indicators
  const clarityIndicators = [
    'clear', 'concise', 'precise', 'specific', 'explicit',
    'straightforward', 'understandable', 'comprehensible', 'readable'
  ];
  
  // Check for clarity detractors
  const clarityDetractors = [
    'complex', 'complicated', 'confusing', 'ambiguous', 'vague',
    'unclear', 'difficult', 'hard to understand', 'obscure'
  ];
  
  // Count indicators and detractors
  let indicatorCount = 0;
  let detractorCount = 0;
  
  for (const indicator of clarityIndicators) {
    if (response.toLowerCase().includes(indicator)) {
      indicatorCount++;
    }
  }
  
  for (const detractor of clarityDetractors) {
    if (response.toLowerCase().includes(detractor)) {
      detractorCount++;
    }
  }
  
  // Calculate average sentence length
  const sentences = response.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0);
  const avgLength = sentences.reduce((sum, sentence) => sum + sentence.length, 0) / Math.max(1, sentences.length);
  
  // Penalize very long sentences
  const lengthPenalty = Math.max(0, Math.min(0.5, (avgLength - 100) / 100));
  
  // Calculate clarity score
  let score = 0.7; // Base score
  
  score += indicatorCount * 0.05;
  score -= detractorCount * 0.1;
  score -= lengthPenalty;
  
  // Clamp to 0-1 range
  return Math.max(0, Math.min(1, score));
}

module.exports = {
  calculateCodeCorrectnessScore,
  calculateCodeEfficiencyScore,
  calculateCodeReadabilityScore,
  calculateCodeDocumentationScore,
  calculateConciseness,
  calculateFluencyScore,
  calculateConsistencyScore,
  calculateCreativityScore,
  calculateEngagementScore,
  calculateOriginalityScore,
  calculateStructureScore,
  calculateInsightScore,
  calculateMethodologyScore,
  calculateClarityScore
};
