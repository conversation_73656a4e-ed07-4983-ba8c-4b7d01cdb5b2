/**
 * Cache Utility
 * 
 * Provides caching functionality for the LLM Rotation System.
 */

const crypto = require('crypto');

/**
 * Cache service
 */
class CacheService {
  /**
   * Create a new cache service
   * 
   * @param {Object} options - Service options
   */
  constructor(options = {}) {
    this.logger = options.logger || console;
    this.db = options.db;
    this.ttl = options.ttl || 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    this.enabled = options.enabled !== false;
    this.initialized = false;
    
    // In-memory cache (fallback if no database)
    this.memoryCache = new Map();
    this.memoryCacheStats = {
      hits: 0,
      misses: 0,
      size: 0
    };
  }
  
  /**
   * Initialize the service
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }
    
    this.logger.info('Initializing cache service');
    
    if (this.db) {
      try {
        // Create cache table
        await this.db.query(`
          CREATE TABLE IF NOT EXISTS llm_cache (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            key TEXT NOT NULL,
            response JSONB NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP WITH TIME ZONE NOT NULL
          )
        `);
        
        // Create index on key
        await this.db.query(`
          CREATE INDEX IF NOT EXISTS idx_llm_cache_key ON llm_cache(key);
          CREATE INDEX IF NOT EXISTS idx_llm_cache_expires_at ON llm_cache(expires_at);
        `);
        
        // Create cache stats table
        await this.db.query(`
          CREATE TABLE IF NOT EXISTS llm_cache_stats (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            date DATE NOT NULL,
            hits INTEGER NOT NULL DEFAULT 0,
            misses INTEGER NOT NULL DEFAULT 0,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
          )
        `);
        
        // Create index on date
        await this.db.query(`
          CREATE INDEX IF NOT EXISTS idx_llm_cache_stats_date ON llm_cache_stats(date);
        `);
      } catch (error) {
        this.logger.error('Error initializing cache service', error);
        throw error;
      }
    }
    
    this.initialized = true;
    this.logger.info('Cache service initialized');
  }
  
  /**
   * Generate cache key
   * 
   * @param {Object} task - Task
   * @returns {String} Cache key
   * @private
   */
  _generateKey(task) {
    // Create a normalized task object for consistent hashing
    const normalizedTask = {
      prompt: task.prompt,
      systemPrompt: task.systemPrompt || '',
      temperature: task.temperature || 0.7,
      maxTokens: task.maxTokens || 1000,
      topP: task.topP || 1,
      frequencyPenalty: task.frequencyPenalty || 0,
      presencePenalty: task.presencePenalty || 0
    };
    
    // Create a hash of the normalized task
    const hash = crypto.createHash('sha256');
    hash.update(JSON.stringify(normalizedTask));
    return hash.digest('hex');
  }
  
  /**
   * Get a cached response
   * 
   * @param {Object} task - Task
   * @returns {Promise<Object>} Cached response or null
   */
  async get(task) {
    if (!this.enabled) {
      return null;
    }
    
    await this.initialize();
    
    const key = this._generateKey(task);
    
    // Try to get from database
    if (this.db) {
      try {
        // Get cached response
        const result = await this.db.query(
          `SELECT response FROM llm_cache WHERE key = $1 AND expires_at > CURRENT_TIMESTAMP`,
          [key]
        );
        
        if (result.rows.length > 0) {
          // Update cache stats
          await this._updateStats(true);
          
          return result.rows[0].response;
        }
        
        // Update cache stats
        await this._updateStats(false);
        
        return null;
      } catch (error) {
        this.logger.error(`Error getting cached response: ${error.message}`, {
          error,
          key
        });
        
        // Fall back to memory cache
        return this._getFromMemory(key);
      }
    }
    
    // Fall back to memory cache
    return this._getFromMemory(key);
  }
  
  /**
   * Set a cached response
   * 
   * @param {Object} task - Task
   * @param {Object} response - Response
   * @returns {Promise<void>}
   */
  async set(task, response) {
    if (!this.enabled) {
      return;
    }
    
    await this.initialize();
    
    const key = this._generateKey(task);
    const expiresAt = new Date(Date.now() + this.ttl);
    
    // Try to set in database
    if (this.db) {
      try {
        // Set cached response
        await this.db.query(
          `INSERT INTO llm_cache (key, response, expires_at)
           VALUES ($1, $2, $3)
           ON CONFLICT (key) DO UPDATE SET
             response = $2,
             expires_at = $3`,
          [key, response, expiresAt]
        );
        
        return;
      } catch (error) {
        this.logger.error(`Error setting cached response: ${error.message}`, {
          error,
          key
        });
        
        // Fall back to memory cache
        this._setInMemory(key, response);
      }
    } else {
      // Fall back to memory cache
      this._setInMemory(key, response);
    }
  }
  
  /**
   * Clear the cache
   * 
   * @returns {Promise<void>}
   */
  async clear() {
    await this.initialize();
    
    // Clear database cache
    if (this.db) {
      try {
        await this.db.query('DELETE FROM llm_cache');
      } catch (error) {
        this.logger.error(`Error clearing cache: ${error.message}`, {
          error
        });
      }
    }
    
    // Clear memory cache
    this.memoryCache.clear();
    this.memoryCacheStats.size = 0;
  }
  
  /**
   * Get cache stats
   * 
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Cache stats
   */
  async getStats(options = {}) {
    await this.initialize();
    
    const {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      endDate = new Date().toISOString().split('T')[0]
    } = options;
    
    // Get stats from database
    if (this.db) {
      try {
        const result = await this.db.query(
          `SELECT 
            SUM(hits) as hits,
            SUM(misses) as misses,
            SUM(hits + misses) as total
          FROM llm_cache_stats
          WHERE date BETWEEN $1 AND $2`,
          [startDate, endDate]
        );
        
        if (result.rows.length > 0) {
          const row = result.rows[0];
          const hits = parseInt(row.hits) || 0;
          const misses = parseInt(row.misses) || 0;
          const total = parseInt(row.total) || 0;
          
          return {
            hits,
            misses,
            total,
            hitRate: total > 0 ? hits / total : 0
          };
        }
        
        return {
          hits: 0,
          misses: 0,
          total: 0,
          hitRate: 0
        };
      } catch (error) {
        this.logger.error(`Error getting cache stats: ${error.message}`, {
          error
        });
        
        // Fall back to memory cache stats
        return {
          hits: this.memoryCacheStats.hits,
          misses: this.memoryCacheStats.misses,
          total: this.memoryCacheStats.hits + this.memoryCacheStats.misses,
          hitRate: this.memoryCacheStats.hits + this.memoryCacheStats.misses > 0
            ? this.memoryCacheStats.hits / (this.memoryCacheStats.hits + this.memoryCacheStats.misses)
            : 0,
          size: this.memoryCacheStats.size
        };
      }
    }
    
    // Fall back to memory cache stats
    return {
      hits: this.memoryCacheStats.hits,
      misses: this.memoryCacheStats.misses,
      total: this.memoryCacheStats.hits + this.memoryCacheStats.misses,
      hitRate: this.memoryCacheStats.hits + this.memoryCacheStats.misses > 0
        ? this.memoryCacheStats.hits / (this.memoryCacheStats.hits + this.memoryCacheStats.misses)
        : 0,
      size: this.memoryCacheStats.size
    };
  }
  
  /**
   * Update cache stats
   * 
   * @param {Boolean} hit - Whether the cache was hit
   * @returns {Promise<void>}
   * @private
   */
  async _updateStats(hit) {
    if (this.db) {
      try {
        const date = new Date().toISOString().split('T')[0];
        
        // Check if stats record exists for this date
        const result = await this.db.query(
          'SELECT * FROM llm_cache_stats WHERE date = $1',
          [date]
        );
        
        if (result.rows.length > 0) {
          // Update existing record
          const record = result.rows[0];
          
          await this.db.query(
            `UPDATE llm_cache_stats SET
              hits = hits + $1,
              misses = misses + $2,
              updated_at = CURRENT_TIMESTAMP
            WHERE id = $3`,
            [hit ? 1 : 0, hit ? 0 : 1, record.id]
          );
        } else {
          // Create new record
          await this.db.query(
            `INSERT INTO llm_cache_stats (date, hits, misses)
             VALUES ($1, $2, $3)`,
            [date, hit ? 1 : 0, hit ? 0 : 1]
          );
        }
      } catch (error) {
        this.logger.error(`Error updating cache stats: ${error.message}`, {
          error
        });
      }
    } else {
      // Update memory cache stats
      if (hit) {
        this.memoryCacheStats.hits++;
      } else {
        this.memoryCacheStats.misses++;
      }
    }
  }
  
  /**
   * Get from memory cache
   * 
   * @param {String} key - Cache key
   * @returns {Object} Cached response or null
   * @private
   */
  _getFromMemory(key) {
    const cached = this.memoryCache.get(key);
    
    if (cached && cached.expiresAt > Date.now()) {
      this.memoryCacheStats.hits++;
      return cached.response;
    }
    
    if (cached) {
      // Remove expired entry
      this.memoryCache.delete(key);
      this.memoryCacheStats.size--;
    }
    
    this.memoryCacheStats.misses++;
    return null;
  }
  
  /**
   * Set in memory cache
   * 
   * @param {String} key - Cache key
   * @param {Object} response - Response
   * @private
   */
  _setInMemory(key, response) {
    // Clean up expired entries if cache is getting too large
    if (this.memoryCache.size > 1000) {
      const now = Date.now();
      
      for (const [k, v] of this.memoryCache.entries()) {
        if (v.expiresAt <= now) {
          this.memoryCache.delete(k);
          this.memoryCacheStats.size--;
        }
      }
    }
    
    this.memoryCache.set(key, {
      response,
      expiresAt: Date.now() + this.ttl
    });
    
    this.memoryCacheStats.size++;
  }
}

module.exports = {
  CacheService
};
