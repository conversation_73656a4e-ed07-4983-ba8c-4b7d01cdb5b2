/**
 * Learning Utility
 *
 * Provides advanced learning functionality for the LLM Rotation System.
 */

const { TASK_METRICS, DEFAULT_METRICS } = require('./task-metrics');

/**
 * Learning service
 */
class LearningService {
  /**
   * Create a new learning service
   *
   * @param {Object} options - Service options
   */
  constructor(options = {}) {
    this.logger = options.logger || console;
    this.db = options.db;
    this.initialized = false;

    // Learning parameters
    this.learningRate = options.learningRate || 0.1;
    this.explorationRate = options.explorationRate || 0.1;
    this.decayRate = options.decayRate || 0.999;
    this.minExplorationRate = options.minExplorationRate || 0.01;
    this.adaptiveLearningRate = options.adaptiveLearningRate !== false;
    this.maxMemorySize = options.maxMemorySize || 1000;

    // Task type features
    this.taskTypeFeatures = {
      'text-generation': [1, 0, 0, 0, 0, 0, 0, 0],
      'text-completion': [0, 1, 0, 0, 0, 0, 0, 0],
      'summarization': [0, 0, 1, 0, 0, 0, 0, 0],
      'translation': [0, 0, 0, 1, 0, 0, 0, 0],
      'code-generation': [0, 0, 0, 0, 1, 0, 0, 0],
      'question-answering': [0, 0, 0, 0, 0, 1, 0, 0],
      'creative-writing': [0, 0, 0, 0, 0, 0, 1, 0],
      'data-analysis': [0, 0, 0, 0, 0, 0, 0, 1]
    };

    // Provider performance model
    this.providerModels = new Map();

    // Performance metrics
    this.metrics = {
      totalCalls: 0,
      successfulCalls: 0,
      failedCalls: 0,
      averageQualityScore: 0,
      taskTypeDistribution: {},
      providerDistribution: {},
      modelDistribution: {}
    };

    // Long-term memory for learning
    this.memory = {
      recentTasks: [],
      recentQualityScores: [],
      providerPerformanceHistory: new Map(),
      taskTypePerformance: new Map()
    };
  }

  /**
   * Initialize the service
   *
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }

    this.logger.info('Initializing learning service');

    if (this.db) {
      try {
        // Create learning tables
        await this.db.query(`
          CREATE TABLE IF NOT EXISTS llm_learning_models (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            provider_id UUID NOT NULL,
            model_type TEXT NOT NULL,
            weights JSONB NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
          )
        `);

        // Create index on provider_id
        await this.db.query(`
          CREATE INDEX IF NOT EXISTS idx_llm_learning_models_provider_id ON llm_learning_models(provider_id);
        `);

        // Create performance history table
        await this.db.query(`
          CREATE TABLE IF NOT EXISTS llm_performance_history (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            provider_id TEXT NOT NULL,
            model_id TEXT NOT NULL,
            task_type TEXT NOT NULL,
            quality_score FLOAT NOT NULL,
            response_time INTEGER NOT NULL,
            token_count INTEGER NOT NULL,
            success BOOLEAN NOT NULL,
            error_type TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
          )
        `);

        // Create indexes for performance history
        await this.db.query(`CREATE INDEX IF NOT EXISTS idx_llm_performance_provider ON llm_performance_history(provider_id)`);
        await this.db.query(`CREATE INDEX IF NOT EXISTS idx_llm_performance_task_type ON llm_performance_history(task_type)`);
        await this.db.query(`CREATE INDEX IF NOT EXISTS idx_llm_performance_created_at ON llm_performance_history(created_at)`);

        // Load existing models
        const result = await this.db.query(
          'SELECT provider_id, model_type, weights FROM llm_learning_models'
        );

        for (const row of result.rows) {
          const key = `${row.provider_id}:${row.model_type}`;
          this.providerModels.set(key, row.weights);
        }

        // Load recent performance history
        const historyResult = await this.db.query(
          `SELECT provider_id, model_id, task_type, quality_score, response_time, token_count, success, error_type, created_at
           FROM llm_performance_history
           ORDER BY created_at DESC
           LIMIT $1`,
          [this.maxMemorySize]
        );

        // Process performance history
        for (const row of historyResult.rows) {
          this._addToPerformanceHistory(row);
        }
      } catch (error) {
        this.logger.error('Error initializing learning service', error);
        throw error;
      }
    }

    this.initialized = true;
    this.logger.info('Learning service initialized');
  }

  /**
   * Get task features
   *
   * @param {Object} task - Task
   * @param {Object} classification - Task classification
   * @returns {Array<Number>} Task features
   * @private
   */
  _getTaskFeatures(task, classification) {
    // Detect task type
    const taskType = this._detectTaskType(task);

    // Get base features from task type
    const typeFeatures = this.taskTypeFeatures[taskType] || [0, 0, 0, 0, 0, 0, 0, 0];

    // Add classification features
    const classificationFeatures = [
      classification.complexity,
      classification.importance,
      classification.creativity,
      classification.factuality,
      classification.sensitivity
    ];

    // Add task-specific features
    const promptLength = task.prompt.length / 1000; // Normalize by 1000
    const maxTokens = (task.maxTokens || 1000) / 1000; // Normalize by 1000
    const temperature = task.temperature || 0.7;

    // Combine all features
    return [
      ...typeFeatures,
      ...classificationFeatures,
      promptLength,
      maxTokens,
      temperature
    ];
  }

  /**
   * Detect task type
   *
   * @param {Object} task - Task
   * @returns {String} Task type
   * @private
   */
  _detectTaskType(task) {
    // If task has a type, use it
    if (task.type) {
      return task.type;
    }

    const prompt = task.prompt.toLowerCase();

    // Check for code generation
    if (prompt.includes('code') || prompt.includes('function') || prompt.includes('program') || prompt.includes('script')) {
      return 'code-generation';
    }

    // Check for summarization
    if (prompt.includes('summarize') || prompt.includes('summary') || prompt.includes('summarization') || prompt.includes('tldr')) {
      return 'summarization';
    }

    // Check for translation
    if (prompt.includes('translate') || prompt.includes('translation') || prompt.includes('in english') || prompt.includes('in spanish')) {
      return 'translation';
    }

    // Check for question answering
    if (prompt.includes('?') || prompt.startsWith('what') || prompt.startsWith('how') || prompt.startsWith('why') || prompt.startsWith('when') || prompt.startsWith('where')) {
      return 'question-answering';
    }

    // Check for creative writing
    if (prompt.includes('story') || prompt.includes('poem') || prompt.includes('creative') || prompt.includes('write') || prompt.includes('essay')) {
      return 'creative-writing';
    }

    // Check for data analysis
    if (prompt.includes('analyze') || prompt.includes('analysis') || prompt.includes('data') || prompt.includes('statistics') || prompt.includes('trends')) {
      return 'data-analysis';
    }

    // Default to text generation
    return 'text-generation';
  }

  /**
   * Predict provider performance
   *
   * @param {String} providerId - Provider ID
   * @param {Object} task - Task
   * @param {Object} classification - Task classification
   * @returns {Object} Predicted performance
   */
  predictPerformance(providerId, task, classification) {
    // Get task features
    const features = this._getTaskFeatures(task, classification);

    // Get provider models
    const qualityModel = this._getProviderModel(providerId, 'quality');
    const reliabilityModel = this._getProviderModel(providerId, 'reliability');
    const speedModel = this._getProviderModel(providerId, 'speed');
    const costModel = this._getProviderModel(providerId, 'cost');

    // Predict performance
    const qualityScore = this._predict(features, qualityModel);
    const reliabilityScore = this._predict(features, reliabilityModel);
    const speedScore = this._predict(features, speedModel);
    const costScore = this._predict(features, costModel);

    return {
      qualityScore,
      reliabilityScore,
      speedScore,
      costScore,
      overallScore: (qualityScore * 0.4) + (reliabilityScore * 0.3) + (speedScore * 0.2) + (costScore * 0.1)
    };
  }

  /**
   * Update provider model
   *
   * @param {String} providerId - Provider ID
   * @param {String} modelType - Model type
   * @param {Object} task - Task
   * @param {Object} classification - Task classification
   * @param {Number} score - Performance score
   * @returns {Promise<void>}
   */
  async updateModel(providerId, modelType, task, classification, score) {
    await this.initialize();

    // Get task features
    const features = this._getTaskFeatures(task, classification);

    // Get provider model
    const model = this._getProviderModel(providerId, modelType);

    // Predict score
    const prediction = this._predict(features, model);

    // Calculate error
    const error = score - prediction;

    // Calculate adaptive learning rate if enabled
    let effectiveLearningRate = this.learningRate;
    if (this.adaptiveLearningRate) {
      // Adjust learning rate based on error magnitude
      // Smaller errors lead to smaller learning rates
      const errorMagnitude = Math.abs(error);
      effectiveLearningRate = this.learningRate * (0.5 + errorMagnitude);
    }

    // Update model weights
    for (let i = 0; i < features.length; i++) {
      model[i] += effectiveLearningRate * error * features[i];
    }

    // Save updated model
    await this._saveProviderModel(providerId, modelType, model);

    // Add to recent quality scores
    this.memory.recentQualityScores.push({
      providerId,
      modelType,
      score,
      prediction,
      error,
      timestamp: Date.now()
    });

    // Trim memory if needed
    if (this.memory.recentQualityScores.length > this.maxMemorySize) {
      this.memory.recentQualityScores.shift();
    }

    // Decay exploration rate
    this.explorationRate = Math.max(this.minExplorationRate, this.explorationRate * this.decayRate);
  }

  /**
   * Get provider model
   *
   * @param {String} providerId - Provider ID
   * @param {String} modelType - Model type
   * @returns {Array<Number>} Model weights
   * @private
   */
  _getProviderModel(providerId, modelType) {
    const key = `${providerId}:${modelType}`;

    if (this.providerModels.has(key)) {
      return [...this.providerModels.get(key)];
    }

    // Initialize with default weights
    const featureCount = Object.values(this.taskTypeFeatures)[0].length + 5 + 3; // Type features + classification features + task-specific features
    const defaultWeights = Array(featureCount).fill(0.5);

    this.providerModels.set(key, defaultWeights);

    return [...defaultWeights];
  }

  /**
   * Save provider model
   *
   * @param {String} providerId - Provider ID
   * @param {String} modelType - Model type
   * @param {Array<Number>} weights - Model weights
   * @returns {Promise<void>}
   * @private
   */
  async _saveProviderModel(providerId, modelType, weights) {
    const key = `${providerId}:${modelType}`;
    this.providerModels.set(key, [...weights]);

    if (this.db) {
      try {
        await this.db.query(
          `INSERT INTO llm_learning_models (provider_id, model_type, weights)
           VALUES ($1, $2, $3)
           ON CONFLICT (provider_id, model_type) DO UPDATE SET
             weights = $3,
             updated_at = CURRENT_TIMESTAMP`,
          [providerId, modelType, JSON.stringify(weights)]
        );
      } catch (error) {
        this.logger.error(`Error saving provider model: ${error.message}`, {
          error,
          providerId,
          modelType
        });
      }
    }
  }

  /**
   * Predict score using model
   *
   * @param {Array<Number>} features - Task features
   * @param {Array<Number>} weights - Model weights
   * @returns {Number} Predicted score
   * @private
   */
  _predict(features, weights) {
    // Dot product of features and weights
    let sum = 0;

    for (let i = 0; i < features.length; i++) {
      sum += features[i] * weights[i];
    }

    // Apply sigmoid function to get score between 0 and 1
    return 1 / (1 + Math.exp(-sum));
  }

  /**
   * Should explore
   *
   * @returns {Boolean} Whether to explore
   */
  shouldExplore() {
    return Math.random() < this.explorationRate;
  }

  /**
   * Get exploration rate
   *
   * @returns {Number} Exploration rate
   */
  getExplorationRate() {
    return this.explorationRate;
  }

  /**
   * Set exploration rate
   *
   * @param {Number} rate - Exploration rate
   */
  setExplorationRate(rate) {
    this.explorationRate = Math.max(this.minExplorationRate, Math.min(1, rate));
  }

  /**
   * Get learning rate
   *
   * @returns {Number} Learning rate
   */
  getLearningRate() {
    return this.learningRate;
  }

  /**
   * Set learning rate
   *
   * @param {Number} rate - Learning rate
   */
  setLearningRate(rate) {
    this.learningRate = Math.max(0.01, Math.min(1, rate));
  }

  /**
   * Record performance data
   *
   * @param {Object} data - Performance data
   * @returns {Promise<void>}
   */
  async recordPerformance(data) {
    await this.initialize();

    const {
      providerId,
      modelId,
      task,
      classification,
      qualityScore,
      responseTime,
      tokenCount,
      success,
      errorType
    } = data;

    // Detect task type
    const taskType = this._detectTaskType(task);

    // Update metrics
    this.metrics.totalCalls++;
    if (success) {
      this.metrics.successfulCalls++;
    } else {
      this.metrics.failedCalls++;
    }

    // Update average quality score
    const totalScores = this.metrics.successfulCalls;
    this.metrics.averageQualityScore = (
      (this.metrics.averageQualityScore * (totalScores - 1)) + qualityScore
    ) / totalScores;

    // Update task type distribution
    this.metrics.taskTypeDistribution[taskType] = (this.metrics.taskTypeDistribution[taskType] || 0) + 1;

    // Update provider distribution
    this.metrics.providerDistribution[providerId] = (this.metrics.providerDistribution[providerId] || 0) + 1;

    // Update model distribution
    this.metrics.modelDistribution[modelId] = (this.metrics.modelDistribution[modelId] || 0) + 1;

    // Add to performance history
    const performanceData = {
      providerId,
      modelId,
      taskType,
      qualityScore,
      responseTime,
      tokenCount,
      success,
      errorType,
      created_at: new Date().toISOString()
    };

    this._addToPerformanceHistory(performanceData);

    // Save to database
    if (this.db) {
      try {
        await this.db.query(
          `INSERT INTO llm_performance_history (
            provider_id, model_id, task_type, quality_score, response_time, token_count, success, error_type
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
          [
            providerId,
            modelId,
            taskType,
            qualityScore,
            responseTime,
            tokenCount,
            success,
            errorType || null
          ]
        );
      } catch (error) {
        this.logger.error(`Error recording performance: ${error.message}`, {
          error,
          providerId,
          modelId
        });
      }
    }
  }

  /**
   * Add to performance history
   *
   * @param {Object} data - Performance data
   * @private
   */
  _addToPerformanceHistory(data) {
    // Add to provider performance history
    if (!this.memory.providerPerformanceHistory.has(data.providerId)) {
      this.memory.providerPerformanceHistory.set(data.providerId, []);
    }

    const providerHistory = this.memory.providerPerformanceHistory.get(data.providerId);
    providerHistory.push(data);

    // Trim if needed
    if (providerHistory.length > this.maxMemorySize) {
      providerHistory.shift();
    }

    // Add to task type performance history
    if (!this.memory.taskTypePerformance.has(data.taskType)) {
      this.memory.taskTypePerformance.set(data.taskType, []);
    }

    const taskTypeHistory = this.memory.taskTypePerformance.get(data.taskType);
    taskTypeHistory.push(data);

    // Trim if needed
    if (taskTypeHistory.length > this.maxMemorySize) {
      taskTypeHistory.shift();
    }
  }

  /**
   * Get provider performance for a task type
   *
   * @param {String} providerId - Provider ID
   * @param {String} taskType - Task type
   * @returns {Object} Performance metrics
   */
  getProviderPerformance(providerId, taskType) {
    // Get provider history
    const providerHistory = this.memory.providerPerformanceHistory.get(providerId) || [];

    // Filter by task type
    const taskTypeHistory = providerHistory.filter(item => item.taskType === taskType);

    // Calculate metrics
    const totalCalls = taskTypeHistory.length;
    const successfulCalls = taskTypeHistory.filter(item => item.success).length;
    const failedCalls = totalCalls - successfulCalls;

    // Calculate success rate
    const successRate = totalCalls > 0 ? successfulCalls / totalCalls : 0;

    // Calculate average quality score
    const qualityScores = taskTypeHistory.filter(item => item.success).map(item => item.qualityScore);
    const averageQualityScore = qualityScores.length > 0 ?
      qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length : 0;

    // Calculate average response time
    const responseTimes = taskTypeHistory.filter(item => item.success).map(item => item.responseTime);
    const averageResponseTime = responseTimes.length > 0 ?
      responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length : 0;

    // Calculate average token count
    const tokenCounts = taskTypeHistory.filter(item => item.success).map(item => item.tokenCount);
    const averageTokenCount = tokenCounts.length > 0 ?
      tokenCounts.reduce((sum, count) => sum + count, 0) / tokenCounts.length : 0;

    return {
      totalCalls,
      successfulCalls,
      failedCalls,
      successRate,
      averageQualityScore,
      averageResponseTime,
      averageTokenCount,
      recentHistory: taskTypeHistory.slice(-10) // Last 10 calls
    };
  }

  /**
   * Get best provider for task type
   *
   * @param {String} taskType - Task type
   * @param {Array<String>} availableProviders - Available providers
   * @returns {String} Best provider ID
   */
  getBestProviderForTaskType(taskType, availableProviders) {
    // Check if we should explore
    if (this.shouldExplore()) {
      // Random selection for exploration
      const randomIndex = Math.floor(Math.random() * availableProviders.length);
      return availableProviders[randomIndex];
    }

    // Get performance for each provider
    const providerPerformance = availableProviders.map(providerId => {
      const performance = this.getProviderPerformance(providerId, taskType);
      return {
        providerId,
        performance
      };
    });

    // Sort by quality score and success rate
    providerPerformance.sort((a, b) => {
      // First prioritize success rate
      const successRateDiff = b.performance.successRate - a.performance.successRate;
      if (Math.abs(successRateDiff) > 0.1) { // Only consider significant differences
        return successRateDiff;
      }

      // Then prioritize quality score
      return b.performance.averageQualityScore - a.performance.averageQualityScore;
    });

    // Return the best provider
    return providerPerformance[0].providerId;
  }

  /**
   * Get performance metrics
   *
   * @returns {Object} Performance metrics
   */
  getPerformanceMetrics() {
    return {
      ...this.metrics,
      explorationRate: this.explorationRate,
      learningRate: this.learningRate,
      memorySize: {
        recentQualityScores: this.memory.recentQualityScores.length,
        providerPerformanceHistory: Array.from(this.memory.providerPerformanceHistory.entries())
          .reduce((sum, [_, history]) => sum + history.length, 0),
        taskTypePerformance: Array.from(this.memory.taskTypePerformance.entries())
          .reduce((sum, [_, history]) => sum + history.length, 0)
      }
    };
  }
}

module.exports = {
  LearningService
};
