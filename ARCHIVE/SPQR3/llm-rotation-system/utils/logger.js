/**
 * Logger Utility
 * 
 * Provides logging functionality for the LLM Rotation System.
 */

const winston = require('winston');
const path = require('path');
const fs = require('fs');

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

/**
 * Create a logger
 * 
 * @param {Object} options - Logger options
 * @returns {Object} Winston logger
 */
function createLogger(options = {}) {
  const { level = 'info', filename = 'llm-rotation.log' } = options;
  
  // Create logger
  const logger = winston.createLogger({
    level,
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json()
    ),
    defaultMeta: { service: 'llm-rotation-system' },
    transports: [
      // Console transport
      new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.simple()
        )
      }),
      
      // File transport
      new winston.transports.File({
        filename: path.join(logsDir, filename),
        maxsize: 10 * 1024 * 1024, // 10MB
        maxFiles: 5,
        tailable: true
      })
    ]
  });
  
  return logger;
}

module.exports = {
  createLogger
};
