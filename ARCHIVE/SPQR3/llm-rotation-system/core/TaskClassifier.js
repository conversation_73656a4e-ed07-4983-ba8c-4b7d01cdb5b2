/**
 * Task Classifier
 * 
 * Classifies tasks for provider selection.
 */

const { TaskClassification } = require('../models/TaskClassification');
const { v4: uuidv4 } = require('uuid');

/**
 * Task classifier
 */
class TaskClassifier {
  /**
   * Create a new task classifier
   * 
   * @param {Object} options - Classifier options
   */
  constructor(options = {}) {
    this.logger = options.logger || console;
    this.db = options.db;
    this.llmService = options.llmService;
    this.initialized = false;
    
    // Keywords for task classification
    this.keywords = {
      complexity: {
        high: ['complex', 'complicated', 'difficult', 'advanced', 'sophisticated', 'intricate', 'elaborate', 'nuanced', 'technical', 'specialized'],
        low: ['simple', 'easy', 'basic', 'straightforward', 'elementary', 'trivial', 'uncomplicated', 'plain', 'clear', 'obvious']
      },
      importance: {
        high: ['important', 'critical', 'crucial', 'essential', 'vital', 'significant', 'key', 'major', 'primary', 'central'],
        low: ['unimportant', 'minor', 'trivial', 'insignificant', 'secondary', 'marginal', 'peripheral', 'negligible', 'inconsequential', 'irrelevant']
      },
      creativity: {
        high: ['creative', 'innovative', 'original', 'imaginative', 'artistic', 'inventive', 'novel', 'unique', 'inspired', 'visionary'],
        low: ['factual', 'objective', 'analytical', 'logical', 'rational', 'methodical', 'systematic', 'structured', 'organized', 'procedural']
      },
      factuality: {
        high: ['factual', 'accurate', 'precise', 'exact', 'correct', 'truthful', 'valid', 'verified', 'confirmed', 'proven'],
        low: ['speculative', 'hypothetical', 'theoretical', 'conjectural', 'unverified', 'unconfirmed', 'uncertain', 'doubtful', 'questionable', 'dubious']
      },
      sensitivity: {
        high: ['sensitive', 'confidential', 'private', 'personal', 'secret', 'restricted', 'classified', 'protected', 'secure', 'privileged'],
        low: ['public', 'open', 'unrestricted', 'unclassified', 'general', 'common', 'shared', 'accessible', 'available', 'disclosed']
      }
    };
    
    // Task types and their characteristics
    this.taskTypes = {
      'text-generation': {
        complexity: 0.5,
        importance: 0.5,
        creativity: 0.7,
        factuality: 0.3,
        sensitivity: 0.3
      },
      'text-completion': {
        complexity: 0.3,
        importance: 0.4,
        creativity: 0.5,
        factuality: 0.5,
        sensitivity: 0.3
      },
      'summarization': {
        complexity: 0.6,
        importance: 0.6,
        creativity: 0.3,
        factuality: 0.8,
        sensitivity: 0.4
      },
      'translation': {
        complexity: 0.7,
        importance: 0.7,
        creativity: 0.2,
        factuality: 0.9,
        sensitivity: 0.5
      },
      'code-generation': {
        complexity: 0.8,
        importance: 0.8,
        creativity: 0.6,
        factuality: 0.9,
        sensitivity: 0.6
      },
      'question-answering': {
        complexity: 0.6,
        importance: 0.7,
        creativity: 0.4,
        factuality: 0.9,
        sensitivity: 0.5
      },
      'creative-writing': {
        complexity: 0.7,
        importance: 0.6,
        creativity: 0.9,
        factuality: 0.3,
        sensitivity: 0.4
      },
      'data-analysis': {
        complexity: 0.8,
        importance: 0.8,
        creativity: 0.3,
        factuality: 0.9,
        sensitivity: 0.7
      }
    };
  }
  
  /**
   * Initialize the classifier
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }
    
    this.logger.info('Initializing task classifier');
    
    // Initialize with database
    if (this.db) {
      try {
        // Create task classifications table
        await this.db.query(`
          CREATE TABLE IF NOT EXISTS task_classifications (
            id UUID PRIMARY KEY,
            task_id UUID NOT NULL,
            complexity FLOAT NOT NULL,
            importance FLOAT NOT NULL,
            creativity FLOAT NOT NULL,
            factuality FLOAT NOT NULL,
            sensitivity FLOAT NOT NULL,
            recommended_tier TEXT NOT NULL,
            created_at TIMESTAMPTZ NOT NULL
          )
        `);
      } catch (error) {
        this.logger.error('Error initializing task classifier', error);
        throw error;
      }
    }
    
    this.initialized = true;
    this.logger.info('Task classifier initialized');
  }
  
  /**
   * Classify a task
   * 
   * @param {Object} task - Task to classify
   * @returns {Promise<TaskClassification>} Task classification
   */
  async classifyTask(task) {
    await this.initialize();
    
    // Use LLM for classification if available
    if (this.llmService && task.prompt.length > 100) {
      try {
        return await this._classifyWithLLM(task);
      } catch (error) {
        this.logger.warn(`Error classifying task with LLM: ${error.message}`, {
          error
        });
      }
    }
    
    // Fall back to heuristic classification
    return this._classifyWithHeuristics(task);
  }
  
  /**
   * Classify a task with LLM
   * 
   * @param {Object} task - Task to classify
   * @returns {Promise<TaskClassification>} Task classification
   * @private
   */
  async _classifyWithLLM(task) {
    try {
      // Create prompt for LLM
      const prompt = `
Analyze the following task and classify it based on the following characteristics:
1. Complexity (0-1): How complex is the task?
2. Importance (0-1): How important is the task?
3. Creativity (0-1): How much creativity is required?
4. Factuality (0-1): How important is factual accuracy?
5. Sensitivity (0-1): How sensitive is the information?

Task: ${task.prompt}

Respond with a JSON object containing the classification scores and the recommended tier (premium, standard, or basic).
Example:
{
  "complexity": 0.7,
  "importance": 0.8,
  "creativity": 0.5,
  "factuality": 0.9,
  "sensitivity": 0.6,
  "recommendedTier": "premium"
}
`;
      
      // Generate classification with LLM
      const response = await this.llmService.generateText({
        prompt,
        temperature: 0.2
      });
      
      // Parse JSON response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      
      if (!jsonMatch) {
        throw new Error('No JSON found in LLM response');
      }
      
      const classification = JSON.parse(jsonMatch[0]);
      
      // Validate classification
      const dimensions = ['complexity', 'importance', 'creativity', 'factuality', 'sensitivity'];
      
      for (const dimension of dimensions) {
        if (typeof classification[dimension] !== 'number' || classification[dimension] < 0 || classification[dimension] > 1) {
          throw new Error(`Invalid ${dimension} score: ${classification[dimension]}`);
        }
      }
      
      if (!['premium', 'standard', 'basic'].includes(classification.recommendedTier)) {
        throw new Error(`Invalid recommended tier: ${classification.recommendedTier}`);
      }
      
      // Save classification to database
      await this._saveClassification(task.id, classification);
      
      // Create task classification
      return new TaskClassification(classification);
    } catch (error) {
      this.logger.error(`Error classifying task with LLM: ${error.message}`, {
        error
      });
      
      // Fall back to heuristic classification
      return this._classifyWithHeuristics(task);
    }
  }
  
  /**
   * Classify a task with heuristics
   * 
   * @param {Object} task - Task to classify
   * @returns {TaskClassification} Task classification
   * @private
   */
  _classifyWithHeuristics(task) {
    // Detect task type
    const taskType = this._detectTaskType(task);
    
    // Get base classification from task type
    const baseClassification = this.taskTypes[taskType] || {
      complexity: 0.5,
      importance: 0.5,
      creativity: 0.5,
      factuality: 0.5,
      sensitivity: 0.5
    };
    
    // Adjust classification based on keywords
    const adjustedClassification = this._adjustClassificationWithKeywords(task, baseClassification);
    
    // Calculate recommended tier
    const classification = new TaskClassification(adjustedClassification);
    
    // Save classification to database
    this._saveClassification(task.id, classification);
    
    return classification;
  }
  
  /**
   * Detect task type
   * 
   * @param {Object} task - Task to classify
   * @returns {String} Task type
   * @private
   */
  _detectTaskType(task) {
    const prompt = task.prompt.toLowerCase();
    
    // Check for code generation
    if (prompt.includes('code') || prompt.includes('function') || prompt.includes('program') || prompt.includes('script')) {
      return 'code-generation';
    }
    
    // Check for summarization
    if (prompt.includes('summarize') || prompt.includes('summary') || prompt.includes('summarization') || prompt.includes('tldr')) {
      return 'summarization';
    }
    
    // Check for translation
    if (prompt.includes('translate') || prompt.includes('translation') || prompt.includes('in english') || prompt.includes('in spanish')) {
      return 'translation';
    }
    
    // Check for question answering
    if (prompt.includes('?') || prompt.startsWith('what') || prompt.startsWith('how') || prompt.startsWith('why') || prompt.startsWith('when') || prompt.startsWith('where')) {
      return 'question-answering';
    }
    
    // Check for creative writing
    if (prompt.includes('story') || prompt.includes('poem') || prompt.includes('creative') || prompt.includes('write') || prompt.includes('essay')) {
      return 'creative-writing';
    }
    
    // Check for data analysis
    if (prompt.includes('analyze') || prompt.includes('analysis') || prompt.includes('data') || prompt.includes('statistics') || prompt.includes('trends')) {
      return 'data-analysis';
    }
    
    // Default to text generation
    return 'text-generation';
  }
  
  /**
   * Adjust classification with keywords
   * 
   * @param {Object} task - Task to classify
   * @param {Object} baseClassification - Base classification
   * @returns {Object} Adjusted classification
   * @private
   */
  _adjustClassificationWithKeywords(task, baseClassification) {
    const prompt = task.prompt.toLowerCase();
    const classification = { ...baseClassification };
    
    // Adjust classification based on keywords
    for (const dimension in this.keywords) {
      // Check for high keywords
      for (const keyword of this.keywords[dimension].high) {
        if (prompt.includes(keyword)) {
          classification[dimension] = Math.min(1, classification[dimension] + 0.1);
        }
      }
      
      // Check for low keywords
      for (const keyword of this.keywords[dimension].low) {
        if (prompt.includes(keyword)) {
          classification[dimension] = Math.max(0, classification[dimension] - 0.1);
        }
      }
    }
    
    // Adjust based on prompt length
    if (prompt.length > 500) {
      classification.complexity = Math.min(1, classification.complexity + 0.1);
    } else if (prompt.length < 50) {
      classification.complexity = Math.max(0, classification.complexity - 0.1);
    }
    
    return classification;
  }
  
  /**
   * Save classification to database
   * 
   * @param {String} taskId - Task ID
   * @param {Object} classification - Classification
   * @returns {Promise<void>}
   * @private
   */
  async _saveClassification(taskId, classification) {
    if (!this.db) {
      return;
    }
    
    try {
      await this.db.query(
        `INSERT INTO task_classifications (
          id, task_id, complexity, importance, creativity, factuality, sensitivity,
          recommended_tier, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
        [
          uuidv4(),
          taskId,
          classification.complexity,
          classification.importance,
          classification.creativity,
          classification.factuality,
          classification.sensitivity,
          classification.recommendedTier || classification.getRecommendedTier(),
          new Date().toISOString()
        ]
      );
    } catch (error) {
      this.logger.error(`Error saving task classification: ${error.message}`, {
        error
      });
    }
  }
}

module.exports = {
  TaskClassifier
};
