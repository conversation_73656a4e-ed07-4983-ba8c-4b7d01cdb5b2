/**
 * Provider Registry
 *
 * Manages LLM providers.
 */

const { LLMProvider } = require('../models/LLMProvider');
const { v4: uuidv4 } = require('uuid');

/**
 * Provider registry
 */
class ProviderRegistry {
  /**
   * Create a new provider registry
   *
   * @param {Object} options - Registry options
   */
  constructor(options = {}) {
    this.logger = options.logger || console;
    this.db = options.db;
    this.providers = new Map();
    this.initialized = false;
  }

  /**
   * Initialize the registry
   *
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }

    this.logger.info('Initializing provider registry');

    // Register default providers if no database
    if (!this.db) {
      this._registerDefaultProviders();
      this.initialized = true;
      return;
    }

    // Initialize with database
    try {
      // Create providers table
      await this.db.query(`
        CREATE TABLE IF NOT EXISTS llm_providers (
          id UUID PRIMARY KEY,
          name TEXT NOT NULL,
          description TEXT,
          api_keys JSONB NOT NULL,
          models JSONB NOT NULL,
          base_url TEXT,
          rate_limits JSONB NOT NULL,
          current_usage JSONB NOT NULL,
          tier TEXT NOT NULL,
          status TEXT NOT NULL,
          capabilities JSONB NOT NULL,
          cost_per_token JSONB NOT NULL,
          quality_score FLOAT NOT NULL,
          reliability_score FLOAT NOT NULL,
          speed_score FLOAT NOT NULL,
          created_at TIMESTAMPTZ NOT NULL,
          updated_at TIMESTAMPTZ NOT NULL
        )
      `);

      // Load providers from database
      const result = await this.db.query('SELECT * FROM llm_providers');

      for (const row of result.rows) {
        const provider = new LLMProvider({
          id: row.id,
          name: row.name,
          description: row.description,
          apiKeys: row.api_keys,
          models: row.models,
          baseUrl: row.base_url,
          rateLimits: row.rate_limits,
          currentUsage: row.current_usage,
          tier: row.tier,
          status: row.status,
          capabilities: row.capabilities,
          costPerToken: row.cost_per_token,
          qualityScore: row.quality_score,
          reliabilityScore: row.reliability_score,
          speedScore: row.speed_score,
          created_at: row.created_at,
          updated_at: row.updated_at
        });

        this.providers.set(provider.id, provider);
      }

      // Register default providers if no providers in database
      if (this.providers.size === 0) {
        this._registerDefaultProviders();
      }

      this.initialized = true;
      this.logger.info(`Provider registry initialized with ${this.providers.size} providers`);
    } catch (error) {
      this.logger.error('Error initializing provider registry', error);
      throw error;
    }
  }

  /**
   * Register a provider
   *
   * @param {LLMProvider} provider - Provider to register
   * @returns {Promise<LLMProvider>} Registered provider
   */
  async registerProvider(provider) {
    await this.initialize();

    // Generate ID if not provided
    if (!provider.id) {
      provider.id = uuidv4();
    }

    // Save to database if available
    if (this.db) {
      try {
        await this.db.query(
          `INSERT INTO llm_providers (
            id, name, description, api_keys, models, base_url, rate_limits, current_usage,
            tier, status, capabilities, cost_per_token, quality_score, reliability_score,
            speed_score, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
          ON CONFLICT (id) DO UPDATE SET
            name = $2,
            description = $3,
            api_keys = $4,
            models = $5,
            base_url = $6,
            rate_limits = $7,
            current_usage = $8,
            tier = $9,
            status = $10,
            capabilities = $11,
            cost_per_token = $12,
            quality_score = $13,
            reliability_score = $14,
            speed_score = $15,
            updated_at = $17
          `,
          [
            provider.id,
            provider.name,
            provider.description,
            JSON.stringify(provider.apiKeys),
            JSON.stringify(provider.models),
            provider.baseUrl,
            JSON.stringify(provider.rateLimits),
            JSON.stringify(provider.currentUsage),
            provider.tier,
            provider.status,
            JSON.stringify(provider.capabilities),
            JSON.stringify(provider.costPerToken),
            provider.qualityScore,
            provider.reliabilityScore,
            provider.speedScore,
            provider.created_at,
            provider.updated_at
          ]
        );
      } catch (error) {
        this.logger.error(`Error registering provider: ${error.message}`, {
          provider: provider.name,
          error
        });

        throw error;
      }
    }

    // Add to registry
    this.providers.set(provider.id, provider);

    this.logger.info(`Registered provider: ${provider.name}`);

    return provider;
  }

  /**
   * Get a provider by ID
   *
   * @param {String} id - Provider ID
   * @returns {LLMProvider} Provider
   */
  getProvider(id) {
    return this.providers.get(id);
  }

  /**
   * Get a provider by name
   *
   * @param {String} name - Provider name
   * @returns {LLMProvider} Provider
   */
  getProviderByName(name) {
    for (const provider of this.providers.values()) {
      if (provider.name === name) {
        return provider;
      }
    }

    return null;
  }

  /**
   * Get all providers
   *
   * @returns {Array<LLMProvider>} Providers
   */
  getAllProviders() {
    return Array.from(this.providers.values());
  }

  /**
   * Get active providers
   *
   * @returns {Array<LLMProvider>} Active providers
   */
  getActiveProviders() {
    return Array.from(this.providers.values()).filter(provider => provider.status === 'active');
  }

  /**
   * Get providers by tier
   *
   * @param {String} tier - Provider tier
   * @returns {Array<LLMProvider>} Providers
   */
  getProvidersByTier(tier) {
    return Array.from(this.providers.values()).filter(provider => provider.tier === tier);
  }

  /**
   * Get providers by capability
   *
   * @param {String} capability - Capability name
   * @returns {Array<LLMProvider>} Providers
   */
  getProvidersByCapability(capability) {
    return Array.from(this.providers.values()).filter(provider => provider.capabilities.includes(capability));
  }

  /**
   * Update provider usage
   *
   * @param {String} id - Provider ID
   * @param {Number} tokens - Number of tokens used
   * @param {String} apiKey - API key used (optional)
   * @returns {Promise<void>}
   */
  async updateProviderUsage(id, tokens, apiKey = null) {
    const provider = this.getProvider(id);

    if (!provider) {
      throw new Error(`Provider not found: ${id}`);
    }

    // Update usage
    provider.updateUsage(tokens);

    // Update in database if available
    if (this.db) {
      try {
        await this.db.query(
          `UPDATE llm_providers SET
            current_usage = $1,
            updated_at = $2,
            api_keys = $3
          WHERE id = $4`,
          [
            JSON.stringify(provider.currentUsage),
            new Date().toISOString(),
            JSON.stringify(provider.apiKeys),
            provider.id
          ]
        );
      } catch (error) {
        this.logger.error(`Error updating provider usage: ${error.message}`, {
          provider: provider.name,
          error
        });
      }
    }
  }

  /**
   * Add an API key to a provider
   *
   * @param {String} id - Provider ID
   * @param {String} apiKey - API key to add
   * @param {Object} options - Key options
   * @returns {Promise<Object>} Added key object
   */
  async addApiKey(id, apiKey, options = {}) {
    const provider = this.getProvider(id);

    if (!provider) {
      throw new Error(`Provider not found: ${id}`);
    }

    // Add API key
    const keyObj = provider.addApiKey(apiKey, options);

    // Update in database if available
    if (this.db) {
      try {
        await this.db.query(
          `UPDATE llm_providers SET
            api_keys = $1,
            updated_at = $2
          WHERE id = $3`,
          [
            JSON.stringify(provider.apiKeys),
            new Date().toISOString(),
            provider.id
          ]
        );
      } catch (error) {
        this.logger.error(`Error adding API key: ${error.message}`, {
          provider: provider.name,
          error
        });
      }
    }

    return keyObj;
  }

  /**
   * Remove an API key from a provider
   *
   * @param {String} id - Provider ID
   * @param {String} apiKey - API key to remove
   * @returns {Promise<Boolean>} Whether the key was removed
   */
  async removeApiKey(id, apiKey) {
    const provider = this.getProvider(id);

    if (!provider) {
      throw new Error(`Provider not found: ${id}`);
    }

    // Remove API key
    const removed = provider.removeApiKey(apiKey);

    if (!removed) {
      return false;
    }

    // Update in database if available
    if (this.db) {
      try {
        await this.db.query(
          `UPDATE llm_providers SET
            api_keys = $1,
            updated_at = $2
          WHERE id = $3`,
          [
            JSON.stringify(provider.apiKeys),
            new Date().toISOString(),
            provider.id
          ]
        );
      } catch (error) {
        this.logger.error(`Error removing API key: ${error.message}`, {
          provider: provider.name,
          error
        });
      }
    }

    return true;
  }

  /**
   * Mark an API key as invalid
   *
   * @param {String} id - Provider ID
   * @param {String} apiKey - API key to mark
   * @param {String} reason - Reason for marking as invalid
   * @returns {Promise<Boolean>} Whether the key was marked as invalid
   */
  async markApiKeyAsInvalid(id, apiKey, reason = 'unknown') {
    const provider = this.getProvider(id);

    if (!provider) {
      throw new Error(`Provider not found: ${id}`);
    }

    // Mark API key as invalid
    const marked = provider.markApiKeyAsInvalid(apiKey, reason);

    if (!marked) {
      return false;
    }

    // Update in database if available
    if (this.db) {
      try {
        await this.db.query(
          `UPDATE llm_providers SET
            api_keys = $1,
            updated_at = $2
          WHERE id = $3`,
          [
            JSON.stringify(provider.apiKeys),
            new Date().toISOString(),
            provider.id
          ]
        );
      } catch (error) {
        this.logger.error(`Error marking API key as invalid: ${error.message}`, {
          provider: provider.name,
          error
        });
      }
    }

    return true;
  }

  /**
   * Mark an API key as rate limited
   *
   * @param {String} id - Provider ID
   * @param {String} apiKey - API key to mark
   * @param {Number} durationMs - Duration in milliseconds
   * @returns {Promise<Boolean>} Whether the key was marked as rate limited
   */
  async markApiKeyAsRateLimited(id, apiKey, durationMs = 60000) {
    const provider = this.getProvider(id);

    if (!provider) {
      throw new Error(`Provider not found: ${id}`);
    }

    // Mark API key as rate limited
    const marked = provider.markApiKeyAsRateLimited(apiKey, durationMs);

    if (!marked) {
      return false;
    }

    // Update in database if available
    if (this.db) {
      try {
        await this.db.query(
          `UPDATE llm_providers SET
            api_keys = $1,
            updated_at = $2
          WHERE id = $3`,
          [
            JSON.stringify(provider.apiKeys),
            new Date().toISOString(),
            provider.id
          ]
        );
      } catch (error) {
        this.logger.error(`Error marking API key as rate limited: ${error.message}`, {
          provider: provider.name,
          error
        });
      }
    }

    return true;
  }

  /**
   * Record an error for an API key
   *
   * @param {String} id - Provider ID
   * @param {String} apiKey - API key
   * @param {String} errorType - Type of error
   * @returns {Promise<Boolean>} Whether the error was recorded
   */
  async recordApiKeyError(id, apiKey, errorType = 'unknown') {
    const provider = this.getProvider(id);

    if (!provider) {
      throw new Error(`Provider not found: ${id}`);
    }

    // Record API key error
    const recorded = provider.recordApiKeyError(apiKey, errorType);

    if (!recorded) {
      return false;
    }

    // Update in database if available
    if (this.db) {
      try {
        await this.db.query(
          `UPDATE llm_providers SET
            api_keys = $1,
            updated_at = $2
          WHERE id = $3`,
          [
            JSON.stringify(provider.apiKeys),
            new Date().toISOString(),
            provider.id
          ]
        );
      } catch (error) {
        this.logger.error(`Error recording API key error: ${error.message}`, {
          provider: provider.name,
          error
        });
      }
    }

    return true;
  }

  /**
   * Update provider quality score
   *
   * @param {String} id - Provider ID
   * @param {Number} score - Quality score
   * @returns {Promise<void>}
   */
  async updateProviderQualityScore(id, score) {
    const provider = this.getProvider(id);

    if (!provider) {
      throw new Error(`Provider not found: ${id}`);
    }

    // Update score with exponential moving average
    const alpha = 0.1; // Weight of new score
    provider.qualityScore = (alpha * score) + ((1 - alpha) * provider.qualityScore);

    // Update in database if available
    if (this.db) {
      try {
        await this.db.query(
          `UPDATE llm_providers SET
            quality_score = $1,
            updated_at = $2
          WHERE id = $3`,
          [
            provider.qualityScore,
            new Date().toISOString(),
            provider.id
          ]
        );
      } catch (error) {
        this.logger.error(`Error updating provider quality score: ${error.message}`, {
          provider: provider.name,
          error
        });
      }
    }
  }

  /**
   * Update provider reliability score
   *
   * @param {String} id - Provider ID
   * @param {Number} score - Reliability score
   * @returns {Promise<void>}
   */
  async updateProviderReliabilityScore(id, score) {
    const provider = this.getProvider(id);

    if (!provider) {
      throw new Error(`Provider not found: ${id}`);
    }

    // Update score with exponential moving average
    const alpha = 0.1; // Weight of new score
    provider.reliabilityScore = (alpha * score) + ((1 - alpha) * provider.reliabilityScore);

    // Update in database if available
    if (this.db) {
      try {
        await this.db.query(
          `UPDATE llm_providers SET
            reliability_score = $1,
            updated_at = $2
          WHERE id = $3`,
          [
            provider.reliabilityScore,
            new Date().toISOString(),
            provider.id
          ]
        );
      } catch (error) {
        this.logger.error(`Error updating provider reliability score: ${error.message}`, {
          provider: provider.name,
          error
        });
      }
    }
  }

  /**
   * Update provider speed score
   *
   * @param {String} id - Provider ID
   * @param {Number} score - Speed score
   * @returns {Promise<void>}
   */
  async updateProviderSpeedScore(id, score) {
    const provider = this.getProvider(id);

    if (!provider) {
      throw new Error(`Provider not found: ${id}`);
    }

    // Update score with exponential moving average
    const alpha = 0.1; // Weight of new score
    provider.speedScore = (alpha * score) + ((1 - alpha) * provider.speedScore);

    // Update in database if available
    if (this.db) {
      try {
        await this.db.query(
          `UPDATE llm_providers SET
            speed_score = $1,
            updated_at = $2
          WHERE id = $3`,
          [
            provider.speedScore,
            new Date().toISOString(),
            provider.id
          ]
        );
      } catch (error) {
        this.logger.error(`Error updating provider speed score: ${error.message}`, {
          provider: provider.name,
          error
        });
      }
    }
  }

  /**
   * Register default providers
   *
   * @private
   */
  _registerDefaultProviders() {
    // OpenAI
    const openaiKeys = [];

    // Add main OpenAI key
    if (process.env.OPENAI_API_KEY) {
      openaiKeys.push(process.env.OPENAI_API_KEY);
    }

    // Add additional OpenAI keys if available
    for (let i = 1; i <= 5; i++) {
      const key = process.env[`OPENAI_API_KEY_${i}`];
      if (key) {
        openaiKeys.push(key);
      }
    }

    this.providers.set('openai', new LLMProvider({
      id: 'openai',
      name: 'OpenAI',
      description: 'OpenAI API',
      apiKeys: openaiKeys,
      models: [
        {
          id: 'gpt-4',
          name: 'GPT-4',
          maxTokens: 8192,
          tier: 'premium',
          capabilities: ['text-generation', 'text-completion', 'function-calling', 'code-generation'],
          costPerToken: {
            input: 0.00003,
            output: 0.00006
          }
        },
        {
          id: 'gpt-4-turbo',
          name: 'GPT-4 Turbo',
          maxTokens: 128000,
          tier: 'premium',
          capabilities: ['text-generation', 'text-completion', 'function-calling', 'code-generation'],
          costPerToken: {
            input: 0.00001,
            output: 0.00003
          }
        },
        {
          id: 'gpt-3.5-turbo',
          name: 'GPT-3.5 Turbo',
          maxTokens: 16385,
          tier: 'standard',
          capabilities: ['text-generation', 'text-completion', 'function-calling', 'code-generation'],
          costPerToken: {
            input: 0.0000015,
            output: 0.000002
          }
        }
      ],
      baseUrl: 'https://api.openai.com/v1',
      tier: 'premium',
      status: 'active',
      capabilities: ['text-generation', 'text-completion', 'function-calling', 'code-generation'],
      costPerToken: {
        input: 0.00001,
        output: 0.00002
      },
      qualityScore: 0.9,
      reliabilityScore: 0.95,
      speedScore: 0.8
    }));

    // Anthropic
    const anthropicKeys = [];

    // Add main Anthropic key
    if (process.env.ANTHROPIC_API_KEY) {
      anthropicKeys.push(process.env.ANTHROPIC_API_KEY);
    }

    // Add additional Anthropic keys if available
    for (let i = 1; i <= 5; i++) {
      const key = process.env[`ANTHROPIC_API_KEY_${i}`];
      if (key) {
        anthropicKeys.push(key);
      }
    }

    this.providers.set('anthropic', new LLMProvider({
      id: 'anthropic',
      name: 'Anthropic',
      description: 'Anthropic API',
      apiKeys: anthropicKeys,
      models: [
        {
          id: 'claude-3-opus',
          name: 'Claude 3 Opus',
          maxTokens: 200000,
          tier: 'premium',
          capabilities: ['text-generation', 'text-completion', 'function-calling', 'code-generation'],
          costPerToken: {
            input: 0.00001,
            output: 0.00003
          }
        },
        {
          id: 'claude-3-sonnet',
          name: 'Claude 3 Sonnet',
          maxTokens: 200000,
          tier: 'standard',
          capabilities: ['text-generation', 'text-completion', 'function-calling', 'code-generation'],
          costPerToken: {
            input: 0.000003,
            output: 0.000015
          }
        },
        {
          id: 'claude-3-haiku',
          name: 'Claude 3 Haiku',
          maxTokens: 200000,
          tier: 'basic',
          capabilities: ['text-generation', 'text-completion', 'function-calling', 'code-generation'],
          costPerToken: {
            input: 0.00000025,
            output: 0.00000125
          }
        }
      ],
      baseUrl: 'https://api.anthropic.com/v1',
      tier: 'premium',
      status: 'active',
      capabilities: ['text-generation', 'text-completion', 'function-calling', 'code-generation'],
      costPerToken: {
        input: 0.000005,
        output: 0.000015
      },
      qualityScore: 0.95,
      reliabilityScore: 0.9,
      speedScore: 0.85
    }));

    // Google
    const googleKeys = [];

    // Add main Google key
    if (process.env.GOOGLE_AI_API_KEY) {
      googleKeys.push(process.env.GOOGLE_AI_API_KEY);
    }

    // Add additional Google keys if available
    for (let i = 1; i <= 3; i++) {
      const key = process.env[`GOOGLE_AI_API_KEY_${i}`];
      if (key) {
        googleKeys.push(key);
      }
    }

    this.providers.set('google', new LLMProvider({
      id: 'google',
      name: 'Google',
      description: 'Google AI API',
      apiKeys: googleKeys,
      models: [
        {
          id: 'gemini-pro',
          name: 'Gemini Pro',
          maxTokens: 32768,
          tier: 'standard',
          capabilities: ['text-generation', 'text-completion', 'function-calling', 'code-generation'],
          costPerToken: {
            input: 0.000001,
            output: 0.000002
          }
        },
        {
          id: 'gemini-ultra',
          name: 'Gemini Ultra',
          maxTokens: 32768,
          tier: 'premium',
          capabilities: ['text-generation', 'text-completion', 'function-calling', 'code-generation'],
          costPerToken: {
            input: 0.000005,
            output: 0.00001
          }
        }
      ],
      baseUrl: 'https://generativelanguage.googleapis.com/v1',
      tier: 'standard',
      status: 'active',
      capabilities: ['text-generation', 'text-completion', 'function-calling', 'code-generation'],
      costPerToken: {
        input: 0.000001,
        output: 0.000002
      },
      qualityScore: 0.85,
      reliabilityScore: 0.9,
      speedScore: 0.9
    }));

    // SambaNova
    this.providers.set('sambanova', new LLMProvider({
      id: 'sambanova',
      name: 'SambaNova',
      description: 'SambaNova API',
      apiKeys: [process.env.SAMBANOVA_API_KEY || ''],
      models: [
        {
          id: 'sambanova-1',
          name: 'SambaNova 1',
          maxTokens: 16000,
          tier: 'basic',
          capabilities: ['text-generation', 'text-completion'],
          costPerToken: {
            input: 0.0000005,
            output: 0.000001
          }
        }
      ],
      baseUrl: 'https://api.sambanova.ai',
      tier: 'basic',
      status: 'active',
      capabilities: ['text-generation', 'text-completion'],
      costPerToken: {
        input: 0.0000005,
        output: 0.000001
      },
      qualityScore: 0.7,
      reliabilityScore: 0.8,
      speedScore: 0.75
    }));

    // Ollama
    this.providers.set('ollama', new LLMProvider({
      id: 'ollama',
      name: 'Ollama',
      description: 'Local Ollama instance',
      apiKeys: [''], // No API key needed for local Ollama
      models: [
        {
          id: 'llama3',
          name: 'Llama 3',
          maxTokens: 8192,
          tier: 'basic',
          capabilities: ['text-generation', 'text-completion', 'code-generation'],
          costPerToken: {
            input: 0,
            output: 0
          }
        },
        {
          id: 'mistral',
          name: 'Mistral',
          maxTokens: 8192,
          tier: 'basic',
          capabilities: ['text-generation', 'text-completion', 'code-generation'],
          costPerToken: {
            input: 0,
            output: 0
          }
        }
      ],
      baseUrl: 'http://localhost:11434/api',
      tier: 'basic',
      status: 'active',
      capabilities: ['text-generation', 'text-completion', 'code-generation'],
      costPerToken: {
        input: 0,
        output: 0
      },
      qualityScore: 0.6,
      reliabilityScore: 0.7,
      speedScore: 0.9
    }));
  }
}

module.exports = {
  ProviderRegistry
};
