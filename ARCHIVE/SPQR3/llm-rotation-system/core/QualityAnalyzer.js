/**
 * Quality Analyzer
 *
 * Analyzes the quality of LLM responses.
 */

const { v4: uuidv4 } = require('uuid');
const { TASK_METRICS, DEFAULT_METRICS, DIMENSION_DESCRIPTIONS } = require('../utils/task-metrics');
const qualityMetrics = require('../utils/quality-metrics');

/**
 * Quality analyzer
 */
class QualityAnalyzer {
  /**
   * Create a new quality analyzer
   *
   * @param {Object} options - Analyzer options
   */
  constructor(options = {}) {
    this.logger = options.logger || console;
    this.db = options.db;
    this.llmService = options.llmService;
    this.providerRegistry = options.providerRegistry;
    this.rotationStrategyManager = options.rotationStrategyManager;
    this.initialized = false;
  }

  /**
   * Initialize the analyzer
   *
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }

    this.logger.info('Initializing quality analyzer');

    if (this.db) {
      try {
        // Create quality analyses table
        await this.db.query(`
          CREATE TABLE IF NOT EXISTS quality_analyses (
            id UUID PRIMARY KEY,
            response_id TEXT NOT NULL,
            provider_id TEXT NOT NULL,
            model_id TEXT NOT NULL,
            task_id TEXT NOT NULL,
            relevance_score FLOAT NOT NULL,
            accuracy_score FLOAT NOT NULL,
            completeness_score FLOAT NOT NULL,
            coherence_score FLOAT NOT NULL,
            helpfulness_score FLOAT NOT NULL,
            overall_score FLOAT NOT NULL,
            analysis_method TEXT NOT NULL,
            created_at TIMESTAMPTZ NOT NULL
          )
        `);
      } catch (error) {
        this.logger.error('Error initializing quality analyzer', error);
        throw error;
      }
    }

    this.initialized = true;
    this.logger.info('Quality analyzer initialized');
  }

  /**
   * Analyze the quality of a response
   *
   * @param {Object} response - LLM response
   * @param {Object} task - Task
   * @param {Object} classification - Task classification
   * @returns {Promise<Object>} Quality analysis
   */
  async analyzeQuality(response, task, classification) {
    await this.initialize();

    // Detect task type
    const taskType = this._detectTaskType(task, classification);

    // Get metrics for task type
    const metrics = TASK_METRICS[taskType] || DEFAULT_METRICS;

    // Determine analysis method
    let analysisMethod = 'heuristic';
    let analysis = null;

    // Use LLM for quality analysis if available and task is important
    if (this.llmService && classification.importance > 0.5) {
      try {
        analysis = await this._analyzeLLMQuality(response, task, taskType, metrics);
        analysisMethod = 'llm';
      } catch (error) {
        this.logger.warn(`Error analyzing quality with LLM: ${error.message}`, {
          responseId: response.id,
          error
        });
      }
    }

    // Fall back to heuristic analysis if LLM analysis failed
    if (!analysis) {
      analysis = this._analyzeHeuristicQuality(response, task, taskType, metrics);
    }

    // Calculate overall score
    const overallScore = this._calculateOverallScore(analysis, metrics.weights);

    // Create quality analysis object with dynamic dimensions
    const qualityAnalysis = {
      id: uuidv4(),
      responseId: response.id,
      providerId: response.providerId,
      modelId: response.modelId,
      taskId: response.taskId,
      taskType,
      dimensions: {},
      overallScore,
      analysisMethod,
      created_at: new Date().toISOString()
    };

    // Add dimension scores
    for (const dimension of metrics.dimensions) {
      qualityAnalysis.dimensions[dimension] = analysis[dimension] || 0;
    }

    // Save analysis to database
    await this._saveAnalysis(qualityAnalysis);

    // Update provider quality score
    await this.providerRegistry.updateProviderQualityScore(response.providerId, overallScore);

    return qualityAnalysis;
  }

  /**
   * Detect task type
   *
   * @param {Object} task - Task
   * @param {Object} classification - Task classification
   * @returns {String} Task type
   * @private
   */
  _detectTaskType(task, classification) {
    // If classification has a task type, use it
    if (classification && classification.taskType) {
      return classification.taskType;
    }

    const prompt = task.prompt.toLowerCase();

    // Check for code generation
    if (prompt.includes('code') || prompt.includes('function') || prompt.includes('program') || prompt.includes('script')) {
      return 'code-generation';
    }

    // Check for summarization
    if (prompt.includes('summarize') || prompt.includes('summary') || prompt.includes('summarization') || prompt.includes('tldr')) {
      return 'summarization';
    }

    // Check for translation
    if (prompt.includes('translate') || prompt.includes('translation') || prompt.includes('in english') || prompt.includes('in spanish')) {
      return 'translation';
    }

    // Check for question answering
    if (prompt.includes('?') || prompt.startsWith('what') || prompt.startsWith('how') || prompt.startsWith('why') || prompt.startsWith('when') || prompt.startsWith('where')) {
      return 'question-answering';
    }

    // Check for creative writing
    if (prompt.includes('story') || prompt.includes('poem') || prompt.includes('creative') || prompt.includes('write') || prompt.includes('essay')) {
      return 'creative-writing';
    }

    // Check for data analysis
    if (prompt.includes('analyze') || prompt.includes('analysis') || prompt.includes('data') || prompt.includes('statistics') || prompt.includes('trends')) {
      return 'data-analysis';
    }

    // Default to text generation
    return 'text-generation';
  }

  /**
   * Analyze quality with LLM
   *
   * @param {Object} response - LLM response
   * @param {Object} task - Task
   * @param {String} taskType - Task type
   * @param {Object} metrics - Quality metrics for task type
   * @returns {Promise<Object>} Quality analysis
   * @private
   */
  async _analyzeLLMQuality(response, task, taskType, metrics) {
    // Create dimension descriptions based on task type
    const dimensionDescriptions = this._getDimensionDescriptions(taskType);

    // Build dimension list for prompt
    let dimensionsList = '';
    metrics.dimensions.forEach((dimension, index) => {
      dimensionsList += `${index + 1}. ${dimension.charAt(0).toUpperCase() + dimension.slice(1)}: ${dimensionDescriptions[dimension] || 'Rate this dimension from 0 to 1.'}
`;
    });

    // Create prompt for LLM
    const prompt = `
Analyze the quality of the following AI response to a user prompt.
This is a ${taskType.replace('-', ' ')} task.
Rate the response on the following dimensions from 0 to 1:

${dimensionsList}
User Prompt: ${task.prompt}

AI Response: ${response.response}

Respond with a JSON object containing the quality scores.
Example:
{
${metrics.dimensions.map(dim => `  "${dim}": 0.8`).join(',\n')}
}
`;

    // Generate analysis with LLM
    const analysisResponse = await this.llmService.generateText({
      prompt,
      temperature: 0.2
    });

    try {
      // Extract JSON from response
      const jsonMatch = analysisResponse.match(/\{[\s\S]*\}/);

      if (!jsonMatch) {
        throw new Error('No JSON found in LLM response');
      }

      const analysis = JSON.parse(jsonMatch[0]);

      // Validate analysis
      const dimensions = metrics.dimensions;

      for (const dimension of dimensions) {
        if (typeof analysis[dimension] !== 'number' || analysis[dimension] < 0 || analysis[dimension] > 1) {
          // If dimension is missing or invalid, set a default value
          analysis[dimension] = 0.5;
          this.logger.warn(`Invalid or missing ${dimension} score, using default value 0.5`);
        }
      }

      return analysis;
    } catch (error) {
      this.logger.error(`Error parsing LLM quality analysis: ${error.message}`, {
        analysisResponse,
        error
      });

      throw error;
    }
  }

  /**
   * Analyze quality with heuristics
   *
   * @param {Object} response - LLM response
   * @param {Object} task - Task
   * @param {String} taskType - Task type
   * @param {Object} metrics - Quality metrics for task type
   * @returns {Object} Quality analysis
   * @private
   */
  _analyzeHeuristicQuality(response, task, taskType, metrics) {
    // Initialize analysis object
    const analysis = {};

    // Common metrics for all task types
    if (metrics.dimensions.includes('relevance')) {
      analysis.relevance = this._calculateRelevanceScore(response.response, task.prompt);
    }

    if (metrics.dimensions.includes('accuracy')) {
      // Accuracy: Assume moderate accuracy (0.7) as we can't verify facts
      analysis.accuracy = 0.7;
    }

    if (metrics.dimensions.includes('completeness')) {
      analysis.completeness = this._calculateCompletenessScore(response.response, task.prompt);
    }

    if (metrics.dimensions.includes('coherence')) {
      analysis.coherence = this._calculateCoherenceScore(response.response);
    }

    if (metrics.dimensions.includes('helpfulness')) {
      analysis.helpfulness = metrics.dimensions.includes('relevance') && metrics.dimensions.includes('completeness') ?
        (analysis.relevance + analysis.completeness) / 2 : 0.7;
    }

    // Task-specific metrics
    switch (taskType) {
      case 'code-generation':
        if (metrics.dimensions.includes('correctness')) {
          analysis.correctness = this._calculateCodeCorrectnessScore(response.response);
        }
        if (metrics.dimensions.includes('efficiency')) {
          analysis.efficiency = this._calculateCodeEfficiencyScore(response.response);
        }
        if (metrics.dimensions.includes('readability')) {
          analysis.readability = this._calculateCodeReadabilityScore(response.response);
        }
        if (metrics.dimensions.includes('documentation')) {
          analysis.documentation = this._calculateCodeDocumentationScore(response.response);
        }
        break;

      case 'summarization':
        if (metrics.dimensions.includes('conciseness')) {
          analysis.conciseness = this._calculateConciseness(response.response, task.prompt);
        }
        break;

      case 'translation':
        if (metrics.dimensions.includes('fluency')) {
          analysis.fluency = this._calculateFluencyScore(response.response);
        }
        if (metrics.dimensions.includes('preservation')) {
          analysis.preservation = 0.7; // Default value as we can't verify meaning preservation
        }
        if (metrics.dimensions.includes('cultural')) {
          analysis.cultural = 0.7; // Default value as we can't verify cultural appropriateness
        }
        if (metrics.dimensions.includes('consistency')) {
          analysis.consistency = this._calculateConsistencyScore(response.response);
        }
        break;

      case 'creative-writing':
        if (metrics.dimensions.includes('creativity')) {
          analysis.creativity = this._calculateCreativityScore(response.response);
        }
        if (metrics.dimensions.includes('engagement')) {
          analysis.engagement = this._calculateEngagementScore(response.response);
        }
        if (metrics.dimensions.includes('originality')) {
          analysis.originality = this._calculateOriginalityScore(response.response, task.prompt);
        }
        if (metrics.dimensions.includes('structure')) {
          analysis.structure = this._calculateStructureScore(response.response);
        }
        break;

      case 'data-analysis':
        if (metrics.dimensions.includes('insight')) {
          analysis.insight = this._calculateInsightScore(response.response);
        }
        if (metrics.dimensions.includes('methodology')) {
          analysis.methodology = this._calculateMethodologyScore(response.response);
        }
        if (metrics.dimensions.includes('clarity')) {
          analysis.clarity = this._calculateClarityScore(response.response);
        }
        break;
    }

    // Fill in any missing dimensions with default values
    for (const dimension of metrics.dimensions) {
      if (analysis[dimension] === undefined) {
        analysis[dimension] = 0.7; // Default value for dimensions we can't calculate
      }
    }

    return analysis;
  }

  /**
   * Calculate relevance score
   *
   * @param {String} response - Response text
   * @param {String} prompt - Prompt text
   * @returns {Number} Relevance score (0-1)
   * @private
   */
  _calculateRelevanceScore(response, prompt) {
    // Extract keywords from prompt
    const promptWords = prompt.toLowerCase().split(/\s+/);
    const keywords = promptWords.filter(word => word.length > 3);

    // Count keyword occurrences in response
    const responseText = response.toLowerCase();
    let keywordCount = 0;

    for (const keyword of keywords) {
      if (responseText.includes(keyword)) {
        keywordCount++;
      }
    }

    // Calculate relevance score
    const uniqueKeywords = [...new Set(keywords)];
    const relevance = uniqueKeywords.length > 0 ? keywordCount / uniqueKeywords.length : 0.5;

    // Clamp to 0-1 range
    return Math.max(0, Math.min(1, relevance));
  }

  /**
   * Calculate completeness score
   *
   * @param {String} response - Response text
   * @param {String} prompt - Prompt text
   * @returns {Number} Completeness score (0-1)
   * @private
   */
  _calculateCompletenessScore(response, prompt) {
    // Calculate completeness based on response length relative to prompt
    const promptLength = prompt.length;
    const responseLength = response.length;

    // Short prompts should have longer responses
    if (promptLength < 100) {
      return Math.min(1, responseLength / 200);
    }

    // Medium prompts should have proportional responses
    if (promptLength < 500) {
      return Math.min(1, responseLength / promptLength);
    }

    // Long prompts should have concise responses
    return Math.min(1, responseLength / (promptLength * 0.5));
  }

  /**
   * Calculate coherence score
   *
   * @param {String} response - Response text
   * @returns {Number} Coherence score (0-1)
   * @private
   */
  _calculateCoherenceScore(response) {
    // Split response into sentences
    const sentences = response.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0);

    // Short responses are assumed to be coherent
    if (sentences.length < 3) {
      return 0.8;
    }

    // Check for very long sentences (potentially incoherent)
    const longSentences = sentences.filter(sentence => sentence.length > 200);
    const longSentenceRatio = longSentences.length / sentences.length;

    // Check for very short sentences (potentially choppy)
    const shortSentences = sentences.filter(sentence => sentence.length < 20);
    const shortSentenceRatio = shortSentences.length / sentences.length;

    // Calculate coherence score
    const coherence = 1 - (longSentenceRatio * 0.5 + shortSentenceRatio * 0.3);

    // Clamp to 0-1 range
    return Math.max(0, Math.min(1, coherence));
  }

  /**
   * Calculate overall score
   *
   * @param {Object} analysis - Quality analysis
   * @param {Object} weights - Dimension weights
   * @returns {Number} Overall score (0-1)
   * @private
   */
  _calculateOverallScore(analysis, weights) {
    let overallScore = 0;
    let totalWeight = 0;

    for (const dimension in weights) {
      if (analysis[dimension] !== undefined) {
        overallScore += analysis[dimension] * weights[dimension];
        totalWeight += weights[dimension];
      }
    }

    // Normalize by total weight
    return totalWeight > 0 ? overallScore / totalWeight : 0.5;
  }

  /**
   * Save analysis to database
   *
   * @param {Object} analysis - Quality analysis
   * @returns {Promise<void>}
   * @private
   */
  async _saveAnalysis(analysis) {
    if (!this.db) {
      return;
    }

    try {
      // Check if quality_analyses_v2 table exists, if not create it
      await this._ensureQualityAnalysesTableExists();

      // Insert into new table format
      await this.db.query(
        `INSERT INTO quality_analyses_v2 (
          id, response_id, provider_id, model_id, task_id, task_type,
          dimensions, overall_score, analysis_method, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
        [
          analysis.id,
          analysis.responseId,
          analysis.providerId,
          analysis.modelId,
          analysis.taskId,
          analysis.taskType,
          JSON.stringify(analysis.dimensions),
          analysis.overallScore,
          analysis.analysisMethod,
          analysis.created_at
        ]
      );
    } catch (error) {
      this.logger.error(`Error saving quality analysis: ${error.message}`, {
        error
      });
    }
  }

  /**
   * Ensure quality analyses table exists
   *
   * @returns {Promise<void>}
   * @private
   */
  async _ensureQualityAnalysesTableExists() {
    try {
      // Create new table with flexible schema
      await this.db.query(`
        CREATE TABLE IF NOT EXISTS quality_analyses_v2 (
          id UUID PRIMARY KEY,
          response_id TEXT NOT NULL,
          provider_id TEXT NOT NULL,
          model_id TEXT NOT NULL,
          task_id TEXT NOT NULL,
          task_type TEXT NOT NULL,
          dimensions JSONB NOT NULL,
          overall_score FLOAT NOT NULL,
          analysis_method TEXT NOT NULL,
          created_at TIMESTAMPTZ NOT NULL
        )
      `);

      // Create indexes
      await this.db.query(`CREATE INDEX IF NOT EXISTS idx_quality_analyses_v2_provider_id ON quality_analyses_v2(provider_id)`);
      await this.db.query(`CREATE INDEX IF NOT EXISTS idx_quality_analyses_v2_task_type ON quality_analyses_v2(task_type)`);
    } catch (error) {
      this.logger.error(`Error creating quality analyses table: ${error.message}`, {
        error
      });
      throw error;
    }
  }
  /**
   * Get dimension descriptions based on task type
   *
   * @param {String} taskType - Task type
   * @returns {Object} Dimension descriptions
   * @private
   */
  _getDimensionDescriptions(taskType) {
    return DIMENSION_DESCRIPTIONS;
  }

  /**
   * Calculate code correctness score
   *
   * @param {String} response - Response text
   * @returns {Number} Correctness score (0-1)
   * @private
   */
  _calculateCodeCorrectnessScore(response) {
    return qualityMetrics.calculateCodeCorrectnessScore(response);
  }

  /**
   * Calculate code efficiency score
   *
   * @param {String} response - Response text
   * @returns {Number} Efficiency score (0-1)
   * @private
   */
  _calculateCodeEfficiencyScore(response) {
    return qualityMetrics.calculateCodeEfficiencyScore(response);
  }

  /**
   * Calculate code readability score
   *
   * @param {String} response - Response text
   * @returns {Number} Readability score (0-1)
   * @private
   */
  _calculateCodeReadabilityScore(response) {
    return qualityMetrics.calculateCodeReadabilityScore(response);
  }

  /**
   * Calculate code documentation score
   *
   * @param {String} response - Response text
   * @returns {Number} Documentation score (0-1)
   * @private
   */
  _calculateCodeDocumentationScore(response) {
    return qualityMetrics.calculateCodeDocumentationScore(response);
  }

  /**
   * Calculate conciseness score
   *
   * @param {String} response - Response text
   * @param {String} prompt - Prompt text
   * @returns {Number} Conciseness score (0-1)
   * @private
   */
  _calculateConciseness(response, prompt) {
    return qualityMetrics.calculateConciseness(response, prompt);
  }

  /**
   * Calculate fluency score
   *
   * @param {String} response - Response text
   * @returns {Number} Fluency score (0-1)
   * @private
   */
  _calculateFluencyScore(response) {
    return qualityMetrics.calculateFluencyScore(response);
  }

  /**
   * Calculate consistency score
   *
   * @param {String} response - Response text
   * @returns {Number} Consistency score (0-1)
   * @private
   */
  _calculateConsistencyScore(response) {
    return qualityMetrics.calculateConsistencyScore(response);
  }

  /**
   * Calculate creativity score
   *
   * @param {String} response - Response text
   * @returns {Number} Creativity score (0-1)
   * @private
   */
  _calculateCreativityScore(response) {
    return qualityMetrics.calculateCreativityScore(response);
  }

  /**
   * Calculate engagement score
   *
   * @param {String} response - Response text
   * @returns {Number} Engagement score (0-1)
   * @private
   */
  _calculateEngagementScore(response) {
    return qualityMetrics.calculateEngagementScore(response);
  }

  /**
   * Calculate originality score
   *
   * @param {String} response - Response text
   * @param {String} prompt - Prompt text
   * @returns {Number} Originality score (0-1)
   * @private
   */
  _calculateOriginalityScore(response, prompt) {
    return qualityMetrics.calculateOriginalityScore(response, prompt);
  }

  /**
   * Calculate structure score
   *
   * @param {String} response - Response text
   * @returns {Number} Structure score (0-1)
   * @private
   */
  _calculateStructureScore(response) {
    return qualityMetrics.calculateStructureScore(response);
  }

  /**
   * Calculate insight score
   *
   * @param {String} response - Response text
   * @returns {Number} Insight score (0-1)
   * @private
   */
  _calculateInsightScore(response) {
    return qualityMetrics.calculateInsightScore(response);
  }

  /**
   * Calculate methodology score
   *
   * @param {String} response - Response text
   * @returns {Number} Methodology score (0-1)
   * @private
   */
  _calculateMethodologyScore(response) {
    return qualityMetrics.calculateMethodologyScore(response);
  }

  /**
   * Calculate clarity score
   *
   * @param {String} response - Response text
   * @returns {Number} Clarity score (0-1)
   * @private
   */
  _calculateClarityScore(response) {
    return qualityMetrics.calculateClarityScore(response);
  }
}

module.exports = {
  QualityAnalyzer
};
