/**
 * LLM Rotation System
 *
 * Main entry point for the LLM Rotation System.
 */

const { ProviderRegistry } = require('./ProviderRegistry');
const { TaskClassifier } = require('./TaskClassifier');
const { QualityAnalyzer } = require('./QualityAnalyzer');
const { ProviderRotationManager } = require('./ProviderRotationManager');
const { RotationStrategyManager } = require('../strategies/RotationStrategyManager');
const { LLMResponse } = require('../models/LLMResponse');
const { createLogger } = require('../utils/logger');
const { AnalyticsService } = require('../utils/analytics');
const { CacheService } = require('../utils/cache');
const { LearningService } = require('../utils/learning');
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');
const { encode: encodeGpt } = require('gpt-3-encoder');
const { countTokens: countAnthropicTokens } = require('@anthropic-ai/tokenizer');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const fetch = require('node-fetch');

/**
 * LLM rotation system
 */
class LLMRotationSystem {
  /**
   * Create a new LLM rotation system
   *
   * @param {Object} options - System options
   */
  constructor(options = {}) {
    // Create logger
    this.logger = options.logger || createLogger({
      level: options.logLevel || 'info',
      filename: options.logFilename || 'llm-rotation.log'
    });

    this.db = options.db;
    this.llmService = options.llmService;

    // Create provider registry
    this.providerRegistry = new ProviderRegistry({
      logger: this.logger,
      db: this.db
    });

    // Create task classifier
    this.taskClassifier = new TaskClassifier({
      logger: this.logger,
      db: this.db,
      llmService: this.llmService
    });

    // Create rotation strategy manager
    this.rotationStrategyManager = new RotationStrategyManager({
      providerRegistry: this.providerRegistry,
      taskClassifier: this.taskClassifier,
      logger: this.logger,
      db: this.db,
      defaultStrategy: options.defaultStrategy || 'adaptive'
    });

    // Create provider rotation manager
    this.providerRotationManager = new ProviderRotationManager({
      logger: this.logger,
      db: this.db,
      providerRegistry: this.providerRegistry,
      rotationStrategy: options.rotationStrategy || 'performance',
      costWeight: options.costWeight || 0.1,
      qualityWeight: options.qualityWeight || 0.4,
      reliabilityWeight: options.reliabilityWeight || 0.3,
      speedWeight: options.speedWeight || 0.2
    });

    // Create quality analyzer
    this.qualityAnalyzer = new QualityAnalyzer({
      logger: this.logger,
      db: this.db,
      llmService: this.llmService,
      providerRegistry: this.providerRegistry,
      rotationStrategyManager: this.rotationStrategyManager
    });

    // Create analytics service
    this.analyticsService = new AnalyticsService({
      logger: this.logger,
      db: this.db
    });

    // Create cache service
    this.cacheService = new CacheService({
      logger: this.logger,
      db: this.db,
      ttl: options.cacheTtl || 24 * 60 * 60 * 1000, // 24 hours in milliseconds
      enabled: options.cacheEnabled !== false
    });

    // Create learning service
    this.learningService = new LearningService({
      logger: this.logger,
      db: this.db,
      learningRate: options.learningRate || 0.1,
      explorationRate: options.explorationRate || 0.1
    });

    this.initialized = false;
  }

  /**
   * Initialize the system
   *
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }

    // Initialize components
    await this.providerRegistry.initialize();
    await this.taskClassifier.initialize();
    await this.providerRotationManager.initialize();
    await this.qualityAnalyzer.initialize();
    await this.analyticsService.initialize();
    await this.cacheService.initialize();
    await this.learningService.initialize();

    this.initialized = true;
    this.logger.info('LLM rotation system initialized');
  }

  /**
   * Process a task
   *
   * @param {Object} task - Task to process
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} Processing result
   */
  async processTask(task, options = {}) {
    await this.initialize();

    // Generate task ID if not provided
    if (!task.id) {
      task.id = uuidv4();
    }

    // Check cache first
    if (options.useCache !== false) {
      const cachedResponse = await this.cacheService.get(task);

      if (cachedResponse) {
        this.logger.info(`Cache hit for task: ${task.id}`);
        return cachedResponse;
      }
    }

    // Classify task
    const classification = await this.taskClassifier.classifyTask(task);

    // Decide whether to explore or exploit
    const shouldExplore = options.explore || this.learningService.shouldExplore();

    // Select provider
    let provider;

    if (shouldExplore) {
      // Exploration: select a random provider
      const providers = this.providerRegistry.getActiveProviders();
      const randomIndex = Math.floor(Math.random() * providers.length);
      provider = providers[randomIndex];

      this.logger.info(`Exploring with provider: ${provider.name}`);
    } else if (options.useRotationManager !== false) {
      // Use the new provider rotation manager
      provider = await this.providerRotationManager.selectProviderForTask(
        task,
        classification
      );

      this.logger.info(`Selected provider using rotation manager: ${provider.name}`);
    } else {
      // Fallback to legacy strategy manager
      provider = await this.rotationStrategyManager.selectProvider(
        task,
        classification,
        options.strategy
      );
    }

    // Select model
    const model = this._selectModel(provider, classification);

    // Process task with provider
    const startTime = new Date().toISOString();
    let response;
    let success = true;
    let error = null;

    try {
      // Generate text
      const result = await this._generateText(provider, model, task);

      // Create response
      response = new LLMResponse({
        id: uuidv4(),
        taskId: task.id,
        providerId: provider.id,
        modelId: model.id,
        prompt: task.prompt,
        response: result.text,
        inputTokens: result.inputTokens,
        outputTokens: result.outputTokens,
        cost: provider.calculateCost(result.inputTokens, result.outputTokens),
        startTime,
        endTime: new Date().toISOString(),
        success: true
      });

      // Get the API key that was used (from the most recent call)
      const usedApiKey = provider.apiKeys.find(k => k.lastUsed === Math.max(...provider.apiKeys.map(k => k.lastUsed)))?.key || '';

      // Update provider usage with the API key that was used
      await this.providerRegistry.updateProviderUsage(
        provider.id,
        response.inputTokens + response.outputTokens,
        usedApiKey
      );

      // Record successful performance in provider rotation manager
      await this.providerRotationManager.recordProviderPerformance({
        providerId: provider.id,
        modelId: model.id,
        task,
        classification,
        qualityScore: 0.7, // Default score until quality analysis is done
        responseTime: response.duration,
        tokenCount: response.inputTokens + response.outputTokens,
        success: true
      });
    } catch (err) {
      success = false;
      error = err.message;

      // Create error response
      response = new LLMResponse({
        id: uuidv4(),
        taskId: task.id,
        providerId: provider.id,
        modelId: model.id,
        prompt: task.prompt,
        response: '',
        inputTokens: 0,
        outputTokens: 0,
        cost: 0,
        startTime,
        endTime: new Date().toISOString(),
        success: false,
        error
      });

      // Update provider reliability score
      await this.providerRegistry.updateProviderReliabilityScore(provider.id, 0);

      // Update learning model
      await this.learningService.updateModel(
        provider.id,
        'reliability',
        task,
        classification,
        0
      );

      // Record failed performance in provider rotation manager
      await this.providerRotationManager.recordProviderPerformance({
        providerId: provider.id,
        modelId: model.id,
        task,
        classification,
        qualityScore: 0,
        responseTime: response.duration,
        tokenCount: 0,
        success: false,
        errorType: error
      });
    }

    // Calculate duration
    response.duration = response.calculateDuration();

    // Update provider speed score
    const speedScore = this._calculateSpeedScore(response.duration);
    await this.providerRegistry.updateProviderSpeedScore(provider.id, speedScore);

    // Update learning model for speed
    await this.learningService.updateModel(
      provider.id,
      'speed',
      task,
      classification,
      speedScore
    );

    // Analyze quality if successful
    let qualityAnalysis = null;

    if (success) {
      try {
        qualityAnalysis = await this.qualityAnalyzer.analyzeQuality(
          response,
          task,
          classification
        );

        // Update learning model for quality
        await this.learningService.updateModel(
          provider.id,
          'quality',
          task,
          classification,
          qualityAnalysis.overallScore
        );

        // Update quality score in provider rotation manager
        await this.providerRotationManager.recordProviderPerformance({
          providerId: provider.id,
          modelId: model.id,
          task,
          classification,
          qualityScore: qualityAnalysis.overallScore,
          responseTime: response.duration,
          tokenCount: response.inputTokens + response.outputTokens,
          success: true
        });
      } catch (err) {
        this.logger.error(`Error analyzing quality: ${err.message}`, {
          taskId: task.id,
          error: err
        });
      }
    }

    // Track response for analytics
    await this.analyticsService.trackResponse(response, qualityAnalysis);

    // Cache successful responses
    if (success && options.useCache !== false) {
      const result = {
        response,
        classification,
        provider: {
          id: provider.id,
          name: provider.name,
          model: model.name
        }
      };

      await this.cacheService.set(task, result);
    }

    return {
      response,
      classification,
      provider: {
        id: provider.id,
        name: provider.name,
        model: model.name
      }
    };
  }

  /**
   * Register a provider
   *
   * @param {Object} providerData - Provider data
   * @returns {Promise<Object>} Registered provider
   */
  async registerProvider(providerData) {
    await this.initialize();

    const { LLMProvider } = require('../models/LLMProvider');
    const provider = new LLMProvider(providerData);

    return this.providerRegistry.registerProvider(provider);
  }

  /**
   * Add an API key to a provider
   *
   * @param {String} providerId - Provider ID
   * @param {String} apiKey - API key to add
   * @param {Object} options - Key options
   * @returns {Promise<Object>} Added key object
   */
  async addApiKey(providerId, apiKey, options = {}) {
    await this.initialize();

    return this.providerRegistry.addApiKey(providerId, apiKey, options);
  }

  /**
   * Remove an API key from a provider
   *
   * @param {String} providerId - Provider ID
   * @param {String} apiKey - API key to remove
   * @returns {Promise<Boolean>} Whether the key was removed
   */
  async removeApiKey(providerId, apiKey) {
    await this.initialize();

    return this.providerRegistry.removeApiKey(providerId, apiKey);
  }

  /**
   * Get API keys for a provider
   *
   * @param {String} providerId - Provider ID
   * @returns {Array<Object>} API keys
   */
  getApiKeys(providerId) {
    const provider = this.getProvider(providerId);

    if (!provider) {
      return [];
    }

    // Return a sanitized version of the API keys
    return provider.apiKeys.map(keyObj => ({
      // Mask the actual key for security
      key: keyObj.key ? `${keyObj.key.substring(0, 3)}...${keyObj.key.substring(keyObj.key.length - 3)}` : '',
      status: keyObj.status,
      lastUsed: keyObj.lastUsed,
      usageCount: keyObj.usageCount,
      errorCount: keyObj.errorCount,
      rateLimitedUntil: keyObj.rateLimitedUntil,
      expiresAt: keyObj.expiresAt,
      created: keyObj.created,
      updated: keyObj.updated
    }));
  }

  /**
   * Get a provider
   *
   * @param {String} id - Provider ID
   * @returns {Object} Provider
   */
  getProvider(id) {
    return this.providerRegistry.getProvider(id);
  }

  /**
   * Get all providers
   *
   * @returns {Array<Object>} Providers
   */
  getAllProviders() {
    return this.providerRegistry.getAllProviders();
  }

  /**
   * Get active providers
   *
   * @returns {Array<Object>} Active providers
   */
  getActiveProviders() {
    return this.providerRegistry.getActiveProviders();
  }

  /**
   * Get providers by tier
   *
   * @param {String} tier - Provider tier
   * @returns {Array<Object>} Providers
   */
  getProvidersByTier(tier) {
    return this.providerRegistry.getProvidersByTier(tier);
  }

  /**
   * Get provider metrics
   *
   * @param {String} providerId - Provider ID
   * @returns {Object} Provider metrics
   */
  getProviderMetrics(providerId) {
    return this.providerRotationManager.getProviderMetrics(providerId);
  }

  /**
   * Get all provider metrics
   *
   * @returns {Array<Object>} Provider metrics
   */
  getAllProviderMetrics() {
    return this.providerRotationManager.getAllProviderMetrics();
  }

  /**
   * Set rotation strategy
   *
   * @param {String} strategy - Rotation strategy
   */
  setRotationStrategy(strategy) {
    return this.providerRotationManager.setRotationStrategy(strategy);
  }

  /**
   * Get rotation strategy
   *
   * @returns {String} Rotation strategy
   */
  getRotationStrategy() {
    return this.providerRotationManager.getRotationStrategy();
  }

  /**
   * Get providers by capability
   *
   * @param {String} capability - Capability name
   * @returns {Array<Object>} Providers
   */
  getProvidersByCapability(capability) {
    return this.providerRegistry.getProvidersByCapability(capability);
  }

  /**
   * Get a strategy
   *
   * @param {String} name - Strategy name
   * @returns {Object} Strategy
   */
  getStrategy(name) {
    return this.rotationStrategyManager.getStrategy(name);
  }

  /**
   * Get all strategies
   *
   * @returns {Array<Object>} Strategies
   */
  getAllStrategies() {
    return this.rotationStrategyManager.getAllStrategies();
  }

  /**
   * Select a model
   *
   * @param {Object} provider - Provider
   * @param {Object} classification - Task classification
   * @returns {Object} Model
   * @private
   */
  _selectModel(provider, classification) {
    // Get recommended tier
    const recommendedTier = classification.getRecommendedTier();

    // Get models for the recommended tier
    const tierModels = provider.getModelsByTier(recommendedTier);

    // If no models for the recommended tier, use all models
    if (tierModels.length === 0) {
      // Get all models
      const allModels = provider.models;

      // Sort models by tier (premium > standard > basic)
      const tierOrder = { premium: 0, standard: 1, basic: 2 };

      allModels.sort((a, b) => {
        return tierOrder[a.tier] - tierOrder[b.tier];
      });

      // Return first model
      return allModels[0];
    }

    // Get required capabilities
    const requiredCapabilities = classification.requiredCapabilities || [];

    // Filter models by required capabilities
    const capableModels = tierModels.filter(model => {
      return requiredCapabilities.every(capability => model.capabilities.includes(capability));
    });

    // If no models with required capabilities, use all tier models
    if (capableModels.length === 0) {
      return tierModels[0];
    }

    // Return first capable model
    return capableModels[0];
  }

  /**
   * Generate text
   *
   * @param {Object} provider - Provider
   * @param {Object} model - Model
   * @param {Object} task - Task
   * @returns {Promise<Object>} Generation result
   * @private
   */
  async _generateText(provider, model, task) {
    // Get the best API key instead of a random one
    const apiKey = provider.getBestApiKey();

    if (!apiKey) {
      throw new Error(`No active API keys available for provider: ${provider.name}`);
    }

    try {
      // Generate text
      const result = await this._callProviderAPI(provider, model, apiKey, task);
      return result;
    } catch (error) {
      // Handle specific error types
      if (error.message.includes('rate limit') ||
          error.message.includes('Rate limit') ||
          error.message.includes('too many requests') ||
          error.message.includes('Too many requests') ||
          error.response?.status === 429) {

        // Mark this API key as rate limited
        this.logger.warn(`API key rate limited for ${provider.name}. Marking as rate limited.`);
        provider.markApiKeyAsRateLimited(apiKey, 60000); // 1 minute

        // Try again with a different key
        const newApiKey = provider.getBestApiKey();

        if (newApiKey && newApiKey !== apiKey) {
          this.logger.info(`Retrying with different API key for ${provider.name}`);
          return this._callProviderAPI(provider, model, newApiKey, task);
        }
      } else if (error.message.includes('invalid') ||
                error.message.includes('Invalid') ||
                error.message.includes('authentication') ||
                error.message.includes('Authentication') ||
                error.message.includes('expired') ||
                error.response?.status === 401 ||
                error.response?.status === 403) {

        // Mark this API key as invalid
        this.logger.warn(`Invalid API key for ${provider.name}. Marking as invalid.`);
        provider.markApiKeyAsInvalid(apiKey, error.message);

        // Try again with a different key
        const newApiKey = provider.getBestApiKey();

        if (newApiKey && newApiKey !== apiKey) {
          this.logger.info(`Retrying with different API key for ${provider.name}`);
          return this._callProviderAPI(provider, model, newApiKey, task);
        }
      } else {
        // Record general error
        provider.recordApiKeyError(apiKey, 'api_error');
      }

      // Re-throw the error if we couldn't recover
      throw error;
    }
  }

  /**
   * Call provider API
   *
   * @param {Object} provider - Provider
   * @param {Object} model - Model
   * @param {String} apiKey - API key
   * @param {Object} task - Task
   * @returns {Promise<Object>} API result
   * @private
   */
  async _callProviderAPI(provider, model, apiKey, task) {
    // This is a simplified implementation
    // In a real system, this would call the actual provider API

    switch (provider.name.toLowerCase()) {
      case 'openai':
        return this._callOpenAI(model, apiKey, task);

      case 'anthropic':
        return this._callAnthropic(model, apiKey, task);

      case 'google':
        return this._callGoogle(model, apiKey, task);

      case 'sambanova':
        return this._callSambaNova(model, apiKey, task);

      case 'ollama':
        return this._callOllama(model, task);

      default:
        throw new Error(`Unsupported provider: ${provider.name}`);
    }
  }

  /**
   * Call OpenAI API
   *
   * @param {Object} model - Model
   * @param {String} apiKey - API key
   * @param {Object} task - Task
   * @returns {Promise<Object>} API result
   * @private
   */
  async _callOpenAI(model, apiKey, task) {
    try {
      // Count input tokens
      const inputTokens = encodeGpt(task.prompt).length;

      // Prepare request
      const response = await axios.post(
        'https://api.openai.com/v1/chat/completions',
        {
          model: model.id,
          messages: [
            { role: 'system', content: task.systemPrompt || 'You are a helpful assistant.' },
            { role: 'user', content: task.prompt }
          ],
          temperature: task.temperature || 0.7,
          max_tokens: task.maxTokens || model.maxTokens || 1000,
          top_p: task.topP || 1,
          frequency_penalty: task.frequencyPenalty || 0,
          presence_penalty: task.presencePenalty || 0
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          }
        }
      );

      // Extract response text
      const text = response.data.choices[0].message.content;

      // Get output tokens
      const outputTokens = response.data.usage.completion_tokens;

      this.logger.info(`OpenAI API call successful: ${model.id}`);

      return {
        text,
        inputTokens,
        outputTokens
      };
    } catch (error) {
      // Extract error details
      const errorMessage = error.response?.data?.error?.message || error.message;
      const statusCode = error.response?.status;

      this.logger.error(`OpenAI API call failed: ${errorMessage}`, {
        error,
        model: model.id,
        statusCode
      });

      // Create a more detailed error object
      const enhancedError = new Error(`OpenAI API call failed: ${errorMessage}`);
      enhancedError.response = error.response;
      enhancedError.statusCode = statusCode;
      enhancedError.originalError = error;

      throw enhancedError;
    }
  }

  /**
   * Call Anthropic API
   *
   * @param {Object} model - Model
   * @param {String} apiKey - API key
   * @param {Object} task - Task
   * @returns {Promise<Object>} API result
   * @private
   */
  async _callAnthropic(model, apiKey, task) {
    try {
      // Count input tokens
      const systemPrompt = task.systemPrompt || 'You are Claude, a helpful AI assistant.';
      const inputTokens = countAnthropicTokens(systemPrompt + task.prompt);

      // Prepare request
      const response = await axios.post(
        'https://api.anthropic.com/v1/messages',
        {
          model: model.id,
          system: systemPrompt,
          messages: [
            { role: 'user', content: task.prompt }
          ],
          max_tokens: task.maxTokens || model.maxTokens || 1000,
          temperature: task.temperature || 0.7
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': apiKey,
            'anthropic-version': '2023-06-01'
          }
        }
      );

      // Extract response text
      const text = response.data.content[0].text;

      // Calculate output tokens
      const outputTokens = countAnthropicTokens(text);

      this.logger.info(`Anthropic API call successful: ${model.id}`);

      return {
        text,
        inputTokens,
        outputTokens
      };
    } catch (error) {
      // Extract error details
      const errorMessage = error.response?.data?.error?.message || error.message;
      const statusCode = error.response?.status;

      this.logger.error(`Anthropic API call failed: ${errorMessage}`, {
        error,
        model: model.id,
        statusCode
      });

      // Create a more detailed error object
      const enhancedError = new Error(`Anthropic API call failed: ${errorMessage}`);
      enhancedError.response = error.response;
      enhancedError.statusCode = statusCode;
      enhancedError.originalError = error;

      throw enhancedError;
    }
  }

  /**
   * Call Google API
   *
   * @param {Object} model - Model
   * @param {String} apiKey - API key
   * @param {Object} task - Task
   * @returns {Promise<Object>} API result
   * @private
   */
  async _callGoogle(model, apiKey, task) {
    try {
      // Initialize Google Generative AI
      const genAI = new GoogleGenerativeAI(apiKey);

      // Get the model
      const googleModel = genAI.getGenerativeModel({ model: model.id });

      // Prepare system prompt
      const systemPrompt = task.systemPrompt || 'You are a helpful AI assistant.';

      // Create chat session
      const chat = googleModel.startChat({
        history: [
          { role: 'user', parts: [{ text: 'Who are you?' }] },
          { role: 'model', parts: [{ text: systemPrompt }] }
        ],
        generationConfig: {
          temperature: task.temperature || 0.7,
          maxOutputTokens: task.maxTokens || model.maxTokens || 1000,
          topP: task.topP || 0.95
        }
      });

      // Send message and get response
      const result = await chat.sendMessage(task.prompt);
      const text = result.response.text();

      // Estimate token counts (Google doesn't provide token counts directly)
      // Using GPT tokenizer as an approximation
      const inputTokens = encodeGpt(systemPrompt + task.prompt).length;
      const outputTokens = encodeGpt(text).length;

      this.logger.info(`Google API call successful: ${model.id}`);

      return {
        text,
        inputTokens,
        outputTokens
      };
    } catch (error) {
      // Extract error details
      const errorMessage = error.response?.data?.error?.message || error.message;
      const statusCode = error.response?.status;

      this.logger.error(`Google API call failed: ${errorMessage}`, {
        error,
        model: model.id,
        statusCode
      });

      // Create a more detailed error object
      const enhancedError = new Error(`Google API call failed: ${errorMessage}`);
      enhancedError.response = error.response;
      enhancedError.statusCode = statusCode;
      enhancedError.originalError = error;

      throw enhancedError;
    }
  }

  /**
   * Call SambaNova API
   *
   * @param {Object} model - Model
   * @param {String} apiKey - API key
   * @param {Object} task - Task
   * @returns {Promise<Object>} API result
   * @private
   */
  async _callSambaNova(model, apiKey, task) {
    try {
      // SambaNova API endpoint
      const endpoint = 'https://api.sambanova.ai/api/v1/completions';

      // Prepare system prompt
      const systemPrompt = task.systemPrompt || 'You are a helpful AI assistant.';

      // Prepare request
      const response = await axios.post(
        endpoint,
        {
          model: model.id,
          prompt: `${systemPrompt}\n\nUser: ${task.prompt}\n\nAssistant:`,
          max_tokens: task.maxTokens || model.maxTokens || 1000,
          temperature: task.temperature || 0.7,
          top_p: task.topP || 0.95
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          }
        }
      );

      // Extract response text
      const text = response.data.choices[0].text.trim();

      // Estimate token counts (SambaNova doesn't provide token counts directly)
      // Using GPT tokenizer as an approximation
      const inputTokens = encodeGpt(systemPrompt + task.prompt).length;
      const outputTokens = encodeGpt(text).length;

      this.logger.info(`SambaNova API call successful: ${model.id}`);

      return {
        text,
        inputTokens,
        outputTokens
      };
    } catch (error) {
      // Extract error details
      const errorMessage = error.response?.data?.error?.message || error.message;
      const statusCode = error.response?.status;

      this.logger.error(`SambaNova API call failed: ${errorMessage}`, {
        error,
        model: model.id,
        statusCode
      });

      // Create a more detailed error object
      const enhancedError = new Error(`SambaNova API call failed: ${errorMessage}`);
      enhancedError.response = error.response;
      enhancedError.statusCode = statusCode;
      enhancedError.originalError = error;

      throw enhancedError;
    }
  }

  /**
   * Call Ollama API
   *
   * @param {Object} model - Model
   * @param {Object} task - Task
   * @returns {Promise<Object>} API result
   * @private
   */
  async _callOllama(model, task) {
    try {
      // Ollama API endpoint (local)
      const endpoint = 'http://localhost:11434/api/generate';

      // Prepare system prompt
      const systemPrompt = task.systemPrompt || 'You are a helpful AI assistant.';

      // Prepare request
      const response = await axios.post(
        endpoint,
        {
          model: model.id,
          prompt: `${systemPrompt}\n\nUser: ${task.prompt}\n\nAssistant:`,
          options: {
            temperature: task.temperature || 0.7,
            num_predict: task.maxTokens || model.maxTokens || 1000
          }
        }
      );

      // Extract response text
      const text = response.data.response.trim();

      // Get token counts from response
      const inputTokens = response.data.prompt_eval_count || encodeGpt(systemPrompt + task.prompt).length;
      const outputTokens = response.data.eval_count || encodeGpt(text).length;

      this.logger.info(`Ollama API call successful: ${model.id}`);

      return {
        text,
        inputTokens,
        outputTokens
      };
    } catch (error) {
      // Extract error details
      const errorMessage = error.response?.data?.error || error.message;
      const statusCode = error.response?.status;

      this.logger.error(`Ollama API call failed: ${errorMessage}`, {
        error,
        model: model.id,
        statusCode
      });

      // Create a more detailed error object
      const enhancedError = new Error(`Ollama API call failed: ${errorMessage}`);
      enhancedError.response = error.response;
      enhancedError.statusCode = statusCode;
      enhancedError.originalError = error;

      throw enhancedError;
    }
  }

  /**
   * Calculate speed score
   *
   * @param {Number} duration - Response duration in milliseconds
   * @returns {Number} Speed score (0-1)
   * @private
   */
  _calculateSpeedScore(duration) {
    // Calculate speed score based on duration
    // Lower duration = higher score

    // Define thresholds
    const fastThreshold = 1000; // 1 second
    const slowThreshold = 10000; // 10 seconds

    if (duration <= fastThreshold) {
      return 1.0;
    } else if (duration >= slowThreshold) {
      return 0.1;
    } else {
      // Linear interpolation between thresholds
      return 1.0 - 0.9 * ((duration - fastThreshold) / (slowThreshold - fastThreshold));
    }
  }
}

module.exports = {
  LLMRotationSystem
};
