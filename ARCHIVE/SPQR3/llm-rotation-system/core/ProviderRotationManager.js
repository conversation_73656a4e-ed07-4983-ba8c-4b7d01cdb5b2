/**
 * Provider Rotation Manager
 * 
 * Manages the rotation of LLM providers based on performance metrics.
 */

const { v4: uuidv4 } = require('uuid');

/**
 * Provider rotation manager
 */
class ProviderRotationManager {
  /**
   * Create a new provider rotation manager
   * 
   * @param {Object} options - Manager options
   */
  constructor(options = {}) {
    this.logger = options.logger || console;
    this.db = options.db;
    this.providerRegistry = options.providerRegistry;
    this.learningService = options.learningService;
    this.qualityAnalyzer = options.qualityAnalyzer;
    this.initialized = false;
    
    // Rotation settings
    this.rotationStrategy = options.rotationStrategy || 'performance'; // 'performance', 'round-robin', 'random'
    this.performanceWindow = options.performanceWindow || 100; // Number of calls to consider for performance metrics
    this.failureThreshold = options.failureThreshold || 0.2; // Failure rate threshold to disable a provider
    this.recoveryThreshold = options.recoveryThreshold || 10; // Number of successful test calls to re-enable a provider
    this.costWeight = options.costWeight || 0.1; // Weight of cost in performance calculation
    this.qualityWeight = options.qualityWeight || 0.4; // Weight of quality in performance calculation
    this.reliabilityWeight = options.reliabilityWeight || 0.3; // Weight of reliability in performance calculation
    this.speedWeight = options.speedWeight || 0.2; // Weight of speed in performance calculation
    
    // Provider state
    this.providerState = new Map();
    this.currentProviderIndex = 0;
    this.activeProviders = [];
    this.disabledProviders = new Set();
    this.recoveryTests = new Map();
    
    // Performance metrics
    this.providerMetrics = new Map();
    this.taskTypePerformance = new Map();
  }
  
  /**
   * Initialize the manager
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }
    
    this.logger.info('Initializing provider rotation manager');
    
    // Initialize dependencies
    if (this.learningService) {
      await this.learningService.initialize();
    }
    
    if (this.providerRegistry) {
      await this.providerRegistry.initialize();
      
      // Get all providers
      const providers = await this.providerRegistry.getAllProviders();
      
      // Initialize provider state
      for (const provider of providers) {
        this.providerState.set(provider.id, {
          id: provider.id,
          name: provider.name,
          enabled: provider.enabled,
          priority: provider.priority || 0,
          models: provider.models || [],
          failureCount: 0,
          successCount: 0,
          totalCalls: 0,
          lastUsed: 0,
          performance: {
            quality: 0.5,
            reliability: 0.5,
            speed: 0.5,
            cost: 0.5,
            overall: 0.5
          }
        });
        
        if (provider.enabled) {
          this.activeProviders.push(provider.id);
        } else {
          this.disabledProviders.add(provider.id);
        }
      }
      
      // Sort active providers by priority
      this.activeProviders.sort((a, b) => {
        const stateA = this.providerState.get(a);
        const stateB = this.providerState.get(b);
        return stateB.priority - stateA.priority;
      });
    }
    
    // Load performance metrics from database
    if (this.db) {
      try {
        // Create provider metrics table if it doesn't exist
        await this.db.query(`
          CREATE TABLE IF NOT EXISTS provider_metrics (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            provider_id TEXT NOT NULL,
            quality_score FLOAT NOT NULL,
            reliability_score FLOAT NOT NULL,
            speed_score FLOAT NOT NULL,
            cost_score FLOAT NOT NULL,
            overall_score FLOAT NOT NULL,
            total_calls INTEGER NOT NULL,
            success_rate FLOAT NOT NULL,
            average_response_time INTEGER NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
          )
        `);
        
        // Create index on provider_id
        await this.db.query(`
          CREATE INDEX IF NOT EXISTS idx_provider_metrics_provider_id ON provider_metrics(provider_id);
        `);
        
        // Load latest metrics for each provider
        const result = await this.db.query(`
          SELECT DISTINCT ON (provider_id) 
            provider_id, quality_score, reliability_score, speed_score, cost_score, 
            overall_score, total_calls, success_rate, average_response_time
          FROM provider_metrics
          ORDER BY provider_id, created_at DESC
        `);
        
        for (const row of result.rows) {
          const providerId = row.provider_id;
          
          if (this.providerState.has(providerId)) {
            const state = this.providerState.get(providerId);
            
            state.performance = {
              quality: row.quality_score,
              reliability: row.reliability_score,
              speed: row.speed_score,
              cost: row.cost_score,
              overall: row.overall_score
            };
            
            state.totalCalls = row.total_calls;
            state.successRate = row.success_rate;
            state.averageResponseTime = row.average_response_time;
          }
        }
      } catch (error) {
        this.logger.error('Error loading provider metrics', error);
      }
    }
    
    this.initialized = true;
    this.logger.info('Provider rotation manager initialized');
  }
  
  /**
   * Select provider for task
   * 
   * @param {Object} task - Task
   * @param {Object} classification - Task classification
   * @returns {Promise<Object>} Selected provider
   */
  async selectProviderForTask(task, classification) {
    await this.initialize();
    
    // Check if we have active providers
    if (this.activeProviders.length === 0) {
      throw new Error('No active providers available');
    }
    
    let selectedProviderId;
    
    // Select provider based on rotation strategy
    switch (this.rotationStrategy) {
      case 'performance':
        selectedProviderId = await this._selectProviderByPerformance(task, classification);
        break;
        
      case 'round-robin':
        selectedProviderId = this._selectProviderRoundRobin();
        break;
        
      case 'random':
        selectedProviderId = this._selectProviderRandom();
        break;
        
      default:
        selectedProviderId = await this._selectProviderByPerformance(task, classification);
    }
    
    // Get provider from registry
    const provider = await this.providerRegistry.getProvider(selectedProviderId);
    
    if (!provider) {
      throw new Error(`Selected provider ${selectedProviderId} not found`);
    }
    
    // Update provider state
    const state = this.providerState.get(selectedProviderId);
    state.lastUsed = Date.now();
    state.totalCalls++;
    
    return provider;
  }
  
  /**
   * Select provider by performance
   * 
   * @param {Object} task - Task
   * @param {Object} classification - Task classification
   * @returns {Promise<String>} Provider ID
   * @private
   */
  async _selectProviderByPerformance(task, classification) {
    // If learning service is available, use it
    if (this.learningService) {
      // Detect task type
      const taskType = this._detectTaskType(task);
      
      // Get best provider for task type
      return this.learningService.getBestProviderForTaskType(taskType, this.activeProviders);
    }
    
    // Otherwise, use simple performance metrics
    const providers = this.activeProviders.map(id => {
      const state = this.providerState.get(id);
      return {
        id,
        performance: state.performance.overall
      };
    });
    
    // Sort by performance
    providers.sort((a, b) => b.performance - a.performance);
    
    // Return best provider
    return providers[0].id;
  }
  
  /**
   * Select provider using round-robin
   * 
   * @returns {String} Provider ID
   * @private
   */
  _selectProviderRoundRobin() {
    const providerId = this.activeProviders[this.currentProviderIndex];
    
    // Update index for next call
    this.currentProviderIndex = (this.currentProviderIndex + 1) % this.activeProviders.length;
    
    return providerId;
  }
  
  /**
   * Select provider randomly
   * 
   * @returns {String} Provider ID
   * @private
   */
  _selectProviderRandom() {
    const randomIndex = Math.floor(Math.random() * this.activeProviders.length);
    return this.activeProviders[randomIndex];
  }
  
  /**
   * Record provider performance
   * 
   * @param {Object} data - Performance data
   * @returns {Promise<void>}
   */
  async recordProviderPerformance(data) {
    await this.initialize();
    
    const {
      providerId,
      modelId,
      task,
      classification,
      qualityScore,
      responseTime,
      tokenCount,
      success,
      errorType
    } = data;
    
    // Update provider state
    if (this.providerState.has(providerId)) {
      const state = this.providerState.get(providerId);
      
      if (success) {
        state.successCount++;
      } else {
        state.failureCount++;
        
        // Check if provider should be disabled
        if (state.failureCount / Math.max(1, state.totalCalls) > this.failureThreshold) {
          await this._disableProvider(providerId, `Failure rate exceeded threshold: ${errorType || 'unknown error'}`);
        }
      }
      
      // Update performance metrics
      this._updateProviderPerformance(providerId, qualityScore, success, responseTime);
    }
    
    // Record in learning service
    if (this.learningService) {
      await this.learningService.recordPerformance(data);
    }
    
    // Save metrics to database
    if (this.db && this.providerState.has(providerId)) {
      const state = this.providerState.get(providerId);
      
      try {
        await this.db.query(`
          INSERT INTO provider_metrics (
            provider_id, quality_score, reliability_score, speed_score, cost_score,
            overall_score, total_calls, success_rate, average_response_time
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        `, [
          providerId,
          state.performance.quality,
          state.performance.reliability,
          state.performance.speed,
          state.performance.cost,
          state.performance.overall,
          state.totalCalls,
          state.successCount / Math.max(1, state.totalCalls),
          state.averageResponseTime || 0
        ]);
      } catch (error) {
        this.logger.error(`Error saving provider metrics: ${error.message}`, {
          error,
          providerId
        });
      }
    }
  }
  
  /**
   * Update provider performance
   * 
   * @param {String} providerId - Provider ID
   * @param {Number} qualityScore - Quality score
   * @param {Boolean} success - Whether the call was successful
   * @param {Number} responseTime - Response time in milliseconds
   * @private
   */
  _updateProviderPerformance(providerId, qualityScore, success, responseTime) {
    const state = this.providerState.get(providerId);
    
    // Update quality score (exponential moving average)
    const alpha = 0.1; // Smoothing factor
    state.performance.quality = alpha * qualityScore + (1 - alpha) * state.performance.quality;
    
    // Update reliability score
    state.performance.reliability = state.successCount / Math.max(1, state.totalCalls);
    
    // Update speed score (normalized response time, lower is better)
    // Convert to a 0-1 score where 1 is fast and 0 is slow
    if (responseTime && success) {
      // Update average response time
      if (!state.averageResponseTime) {
        state.averageResponseTime = responseTime;
      } else {
        state.averageResponseTime = alpha * responseTime + (1 - alpha) * state.averageResponseTime;
      }
      
      // Normalize: 1.0 for <= 500ms, 0.0 for >= 10000ms
      const normalizedSpeed = Math.max(0, Math.min(1, 1 - (state.averageResponseTime - 500) / 9500));
      state.performance.speed = normalizedSpeed;
    }
    
    // Calculate overall performance score
    state.performance.overall = 
      this.qualityWeight * state.performance.quality +
      this.reliabilityWeight * state.performance.reliability +
      this.speedWeight * state.performance.speed +
      this.costWeight * state.performance.cost;
  }
  
  /**
   * Disable provider
   * 
   * @param {String} providerId - Provider ID
   * @param {String} reason - Reason for disabling
   * @returns {Promise<void>}
   * @private
   */
  async _disableProvider(providerId, reason) {
    if (!this.providerState.has(providerId)) {
      return;
    }
    
    const state = this.providerState.get(providerId);
    
    // Skip if already disabled
    if (this.disabledProviders.has(providerId)) {
      return;
    }
    
    this.logger.warn(`Disabling provider ${state.name} (${providerId}): ${reason}`);
    
    // Remove from active providers
    const index = this.activeProviders.indexOf(providerId);
    if (index !== -1) {
      this.activeProviders.splice(index, 1);
    }
    
    // Add to disabled providers
    this.disabledProviders.add(providerId);
    
    // Update provider in registry
    if (this.providerRegistry) {
      await this.providerRegistry.updateProvider(providerId, { enabled: false });
    }
    
    // Schedule recovery test
    this._scheduleRecoveryTest(providerId);
  }
  
  /**
   * Schedule recovery test
   * 
   * @param {String} providerId - Provider ID
   * @private
   */
  _scheduleRecoveryTest(providerId) {
    // Reset recovery counter
    this.recoveryTests.set(providerId, 0);
    
    // Schedule test in 5 minutes
    setTimeout(() => this._testProviderRecovery(providerId), 5 * 60 * 1000);
  }
  
  /**
   * Test provider recovery
   * 
   * @param {String} providerId - Provider ID
   * @returns {Promise<void>}
   * @private
   */
  async _testProviderRecovery(providerId) {
    if (!this.disabledProviders.has(providerId) || !this.providerState.has(providerId)) {
      return;
    }
    
    const state = this.providerState.get(providerId);
    
    this.logger.info(`Testing recovery for provider ${state.name} (${providerId})`);
    
    try {
      // Get provider from registry
      const provider = await this.providerRegistry.getProvider(providerId);
      
      if (!provider) {
        throw new Error('Provider not found');
      }
      
      // Create a simple test task
      const testTask = {
        id: uuidv4(),
        prompt: 'This is a test prompt to check if the provider is working. Please respond with "OK".',
        maxTokens: 10,
        temperature: 0.1
      };
      
      // Send test request
      const response = await provider.generateText(testTask);
      
      // Check if response is valid
      if (response && response.response) {
        // Increment recovery counter
        const recoveryCount = (this.recoveryTests.get(providerId) || 0) + 1;
        this.recoveryTests.set(providerId, recoveryCount);
        
        this.logger.info(`Recovery test ${recoveryCount}/${this.recoveryThreshold} successful for provider ${state.name}`);
        
        // Check if provider should be re-enabled
        if (recoveryCount >= this.recoveryThreshold) {
          await this._enableProvider(providerId);
        } else {
          // Schedule next test
          setTimeout(() => this._testProviderRecovery(providerId), 5 * 60 * 1000);
        }
      } else {
        throw new Error('Invalid response');
      }
    } catch (error) {
      this.logger.error(`Recovery test failed for provider ${state.name}: ${error.message}`, {
        error,
        providerId
      });
      
      // Reset recovery counter
      this.recoveryTests.set(providerId, 0);
      
      // Schedule next test with exponential backoff
      const backoff = Math.min(60, 5 * Math.pow(2, state.failureCount));
      setTimeout(() => this._testProviderRecovery(providerId), backoff * 60 * 1000);
    }
  }
  
  /**
   * Enable provider
   * 
   * @param {String} providerId - Provider ID
   * @returns {Promise<void>}
   * @private
   */
  async _enableProvider(providerId) {
    if (!this.providerState.has(providerId)) {
      return;
    }
    
    const state = this.providerState.get(providerId);
    
    // Skip if already enabled
    if (!this.disabledProviders.has(providerId)) {
      return;
    }
    
    this.logger.info(`Re-enabling provider ${state.name} (${providerId})`);
    
    // Remove from disabled providers
    this.disabledProviders.delete(providerId);
    
    // Add to active providers
    this.activeProviders.push(providerId);
    
    // Reset failure count
    state.failureCount = 0;
    
    // Sort active providers by priority
    this.activeProviders.sort((a, b) => {
      const stateA = this.providerState.get(a);
      const stateB = this.providerState.get(b);
      return stateB.priority - stateA.priority;
    });
    
    // Update provider in registry
    if (this.providerRegistry) {
      await this.providerRegistry.updateProvider(providerId, { enabled: true });
    }
  }
  
  /**
   * Detect task type
   * 
   * @param {Object} task - Task
   * @returns {String} Task type
   * @private
   */
  _detectTaskType(task) {
    // If task has a type, use it
    if (task.type) {
      return task.type;
    }
    
    const prompt = task.prompt.toLowerCase();
    
    // Check for code generation
    if (prompt.includes('code') || prompt.includes('function') || prompt.includes('program') || prompt.includes('script')) {
      return 'code-generation';
    }
    
    // Check for summarization
    if (prompt.includes('summarize') || prompt.includes('summary') || prompt.includes('summarization') || prompt.includes('tldr')) {
      return 'summarization';
    }
    
    // Check for translation
    if (prompt.includes('translate') || prompt.includes('translation') || prompt.includes('in english') || prompt.includes('in spanish')) {
      return 'translation';
    }
    
    // Check for question answering
    if (prompt.includes('?') || prompt.startsWith('what') || prompt.startsWith('how') || prompt.startsWith('why') || prompt.startsWith('when') || prompt.startsWith('where')) {
      return 'question-answering';
    }
    
    // Check for creative writing
    if (prompt.includes('story') || prompt.includes('poem') || prompt.includes('creative') || prompt.includes('write') || prompt.includes('essay')) {
      return 'creative-writing';
    }
    
    // Check for data analysis
    if (prompt.includes('analyze') || prompt.includes('analysis') || prompt.includes('data') || prompt.includes('statistics') || prompt.includes('trends')) {
      return 'data-analysis';
    }
    
    // Default to text generation
    return 'text-generation';
  }
  
  /**
   * Get provider metrics
   * 
   * @param {String} providerId - Provider ID
   * @returns {Object} Provider metrics
   */
  getProviderMetrics(providerId) {
    if (!this.providerState.has(providerId)) {
      return null;
    }
    
    const state = this.providerState.get(providerId);
    
    return {
      id: state.id,
      name: state.name,
      enabled: !this.disabledProviders.has(providerId),
      totalCalls: state.totalCalls,
      successCount: state.successCount,
      failureCount: state.failureCount,
      successRate: state.successCount / Math.max(1, state.totalCalls),
      averageResponseTime: state.averageResponseTime || 0,
      performance: { ...state.performance },
      lastUsed: state.lastUsed
    };
  }
  
  /**
   * Get all provider metrics
   * 
   * @returns {Array<Object>} Provider metrics
   */
  getAllProviderMetrics() {
    return Array.from(this.providerState.keys()).map(id => this.getProviderMetrics(id));
  }
  
  /**
   * Get active providers
   * 
   * @returns {Array<String>} Active provider IDs
   */
  getActiveProviders() {
    return [...this.activeProviders];
  }
  
  /**
   * Get disabled providers
   * 
   * @returns {Array<String>} Disabled provider IDs
   */
  getDisabledProviders() {
    return Array.from(this.disabledProviders);
  }
  
  /**
   * Set rotation strategy
   * 
   * @param {String} strategy - Rotation strategy
   */
  setRotationStrategy(strategy) {
    if (['performance', 'round-robin', 'random'].includes(strategy)) {
      this.rotationStrategy = strategy;
    } else {
      throw new Error(`Invalid rotation strategy: ${strategy}`);
    }
  }
  
  /**
   * Get rotation strategy
   * 
   * @returns {String} Rotation strategy
   */
  getRotationStrategy() {
    return this.rotationStrategy;
  }
}

module.exports = {
  ProviderRotationManager
};
