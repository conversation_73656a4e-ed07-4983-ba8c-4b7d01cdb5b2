/**
 * SPQR2 System
 *
 * Main entry point for the SPQR2 system.
 */

const { createLLMRotationSystem } = require('../llm-rotation-system');
const { createToolRegistry, Tool, ToolRegistry, LLMTool, VAPITool, CRMTool } = require('./tools');
const { createAgent, Agent, CRMAgent } = require('./agents');
const { createApplicationRegistry, Application, CustomApplication } = require('./applications');

/**
 * Create SPQR2 system
 *
 * @param {Object} options - System options
 * @returns {Object} SPQR2 system
 */
function createSPQR2System(options = {}) {
  // Create LLM rotation system
  const llmRotationSystem = createLLMRotationSystem(options);

  // Create tool registry
  const toolRegistry = createToolRegistry({
    ...options,
    llmRotationSystem
  });

  // Create application registry
  const applicationRegistry = createApplicationRegistry({
    ...options,
    toolRegistry,
    llmRotationSystem
  });

  // Create default agents
  const agents = {
    default: createAgent('default', {
      ...options,
      toolRegistry,
      llmRotationSystem
    }),
    crm: createAgent('crm', {
      ...options,
      toolRegistry,
      llmRotationSystem
    })
  };

  return {
    llmRotationSystem,
    toolRegistry,
    applicationRegistry,
    agents,

    /**
     * Initialize the system
     *
     * @returns {Promise<void>}
     */
    async initialize() {
      await llmRotationSystem.initialize();
      await toolRegistry.initialize();
      await applicationRegistry.initialize();

      // Initialize agents
      for (const agent of Object.values(agents)) {
        await agent.initialize();
      }
    },

    /**
     * Shutdown the system
     *
     * @returns {Promise<void>}
     */
    async shutdown() {
      // Shutdown agents
      for (const agent of Object.values(agents)) {
        if (agent.shutdown) {
          await agent.shutdown();
        }
      }

      // Shutdown application registry
      if (applicationRegistry.shutdown) {
        await applicationRegistry.shutdown();
      }

      // Shutdown tool registry
      if (toolRegistry.shutdown) {
        await toolRegistry.shutdown();
      }

      // Shutdown LLM rotation system
      await llmRotationSystem.shutdown();
    },

    /**
     * Process a task
     *
     * @param {Object} task - Task to process
     * @param {String} agentType - Agent type to use
     * @returns {Promise<Object>} Task result
     */
    async processTask(task, agentType = 'default') {
      const agent = agents[agentType] || agents.default;
      return await agent.processTask(task);
    },

    /**
     * Process a message
     *
     * @param {Object} message - Message to process
     * @param {String} agentType - Agent type to use
     * @returns {Promise<Object>} Message result
     */
    async processMessage(message, agentType = 'default') {
      const agent = agents[agentType] || agents.default;
      return await agent.processMessage(message);
    },

    /**
     * Register a provider
     *
     * @param {Object} provider - Provider to register
     * @returns {Promise<Object>} Registered provider
     */
    async registerProvider(provider) {
      return await llmRotationSystem.registerProvider(provider);
    },

    /**
     * Register a tool
     *
     * @param {Tool} tool - Tool to register
     * @returns {Tool} Registered tool
     */
    registerTool(tool) {
      return toolRegistry.registerTool(tool);
    },

    /**
     * Create an application
     *
     * @param {Object} options - Application options
     * @returns {Promise<Application>} Created application
     */
    async createApplication(options) {
      return await applicationRegistry.createApplication(options);
    },

    /**
     * Get an application by ID
     *
     * @param {String} id - Application ID
     * @returns {Application|null} Application or null if not found
     */
    getApplication(id) {
      return applicationRegistry.getApplication(id);
    },

    /**
     * Delete an application
     *
     * @param {String} id - Application ID
     * @returns {Promise<Boolean>} True if deleted, false if not found
     */
    async deleteApplication(id) {
      return await applicationRegistry.deleteApplication(id);
    },

    /**
     * Register an agent
     *
     * @param {String} name - Agent name
     * @param {Agent} agent - Agent to register
     * @returns {Agent} Registered agent
     */
    registerAgent(name, agent) {
      agents[name] = agent;
      return agent;
    },

    /**
     * Get system information
     *
     * @returns {Object} System information
     */
    getInfo() {
      return {
        llmRotationSystem: {
          providers: llmRotationSystem.getAllProviders().map(p => p.name),
          models: llmRotationSystem.getAllModels().map(m => m.name)
        },
        toolRegistry: {
          tools: toolRegistry.getAllTools().map(t => t.name)
        },
        applicationRegistry: {
          applications: applicationRegistry.getAllApplications().map(a => a.name)
        },
        agents: Object.entries(agents).map(([name, agent]) => ({
          name,
          type: agent.constructor.name,
          capabilities: agent.capabilities
        }))
      };
    }
  };
}

module.exports = {
  createSPQR2System,
  Tool,
  ToolRegistry,
  LLMTool,
  VAPITool,
  CRMTool,
  Agent,
  CRMAgent,
  Application,
  CustomApplication
};
