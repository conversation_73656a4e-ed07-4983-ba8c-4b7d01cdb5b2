#!/bin/bash

# SPQR3 Stop Services Script v0.9.5
# This script stops all SPQR3 services

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if a command exists
command_exists() {
  command -v "$1" >/dev/null 2>&1
}

# Main function
main() {
  echo -e "${BLUE}========================================${NC}"
  echo -e "${BLUE}   SPQR3 v0.9.5 Service Stopper         ${NC}"
  echo -e "${BLUE}========================================${NC}"
  echo ""

  echo -e "${YELLOW}Stopping SPQR3 services...${NC}"

  # Check if Dock<PERSON> is running
  if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}Docker is not running. Please start Docker and try again.${NC}"
    exit 1
  fi

  # Check if any containers are running
  if ! docker ps --format '{{.Names}}' | grep -q "spqr-"; then
    echo -e "${YELLOW}No SPQR3 services appear to be running.${NC}"

    # Ask if user wants to continue
    read -p "Do you want to continue anyway? (y/n) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
      echo -e "${YELLOW}Operation cancelled.${NC}"
      exit 0
    fi
  fi

  # Stop services with Docker Compose
  echo -e "${YELLOW}Stopping services with Docker Compose...${NC}"
  if command_exists docker-compose; then
    docker-compose down
  else
    docker compose down
  fi

  echo -e "${GREEN}Services stopped.${NC}"
}

# Run main function
main
