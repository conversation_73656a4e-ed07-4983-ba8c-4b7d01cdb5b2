FROM node:18-alpine

WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm install --production

# Copy source code
COPY . .

# Expose port
EXPOSE 3220

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3220

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:3220/health || exit 1

# Start the service
CMD ["node", "index.js"]
