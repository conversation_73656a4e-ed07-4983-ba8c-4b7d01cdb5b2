/**
 * Task Management System Main Entry Point
 *
 * This file serves as the main entry point for the Task Management System
 * which analyzes prioritizes and schedules tasks based on complexity and system load.
 */

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const logger = require('./utils/logger');
const { v4: uuidv4 } = require('uuid');

// Import Task Management components
const taskAnalyzer = require('./analyzer');
const taskPrioritizer = require('./prioritizer');
const taskScheduler = require('./scheduler');
const taskManager = require('./manager');

// Create Express app
const app = express();
const PORT = process.env.PORT || 3220;

// Request ID middleware
app.use((req, res, next) => {
  req.id = req.headers['x-request-id'] || uuidv4();
  res.setHeader('X-Request-ID', req.id);
  next();
});

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'task-management',
    version: require('./package.json').version
  });
});

// API routes
app.use('/api/tasks', require('./routes/tasks'));
app.use('/api/analyze', require('./routes/analyze'));
app.use('/api/schedule', require('./routes/schedule'));
app.use('/api/status', require('./routes/status'));

// Add scheduler routes
app.use('/api/scheduler', require('./routes/schedule'));

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error(`Error processing request ${req.id}: ${err.message}`);

  res.status(err.status || 500).json({
    error: {
      message: err.message,
      requestId: req.id
    }
  });
});

// Start server
const server = app.listen(PORT, () => {
  logger.info(`Task Management service running on port ${PORT}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM signal received: closing HTTP server');

  server.close(async () => {
    logger.info('HTTP server closed');

    // Shutdown Task Management components
    await taskManager.shutdown();
    await taskScheduler.shutdown();

    process.exit(0);
  });
});

module.exports = app;
