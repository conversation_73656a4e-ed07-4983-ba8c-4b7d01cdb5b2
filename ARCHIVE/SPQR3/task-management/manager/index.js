/**
 * Task Manager Module
 * 
 * Central coordinator for task management operations including
 * lifecycle management, status updates, and system integration.
 */

const logger = require('../utils/logger');
const { analyzeTask } = require('../analyzer');
const { calculatePriorityScore } = require('../prioritizer');
const { createSchedule } = require('../scheduler');

// In-memory store for active tasks (would use database in production)
const taskStore = {
  tasks: new Map(),
  schedules: new Map()
};

/**
 * Create a new task in the system
 *
 * @param {Object} taskData Task creation data
 * @returns {Object} Created task with metadata
 */
async function createTask(taskData) {
  logger.info(`Creating new task: ${taskData.title}`);
  
  try {
    // Generate task with metadata
    const task = {
      id: taskData.id || `task-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      title: taskData.title,
      description: taskData.description,
      status: 'created',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...taskData
    };
    
    // Analyze the task
    const analysis = await analyzeTask(task);
    task.analysis = analysis;
    
    // Calculate priority score
    task.priorityScore = calculatePriorityScore(task, {
      complexity: analysis.complexity
    });
    
    // Store the task
    taskStore.tasks.set(task.id, task);
    
    logger.info(`Task created successfully: ${task.id}`);
    return task;
  } catch (error) {
    logger.error(`Failed to create task: ${error.message}`);
    throw new Error(`Task creation failed: ${error.message}`);
  }
}

/**
 * Get a task by ID
 *
 * @param {string} taskId Task ID to retrieve
 * @returns {Object} Task data
 */
function getTask(taskId) {
  const task = taskStore.tasks.get(taskId);
  
  if (!task) {
    logger.warn(`Task not found: ${taskId}`);
    throw new Error(`Task not found: ${taskId}`);
  }
  
  return task;
}

/**
 * Update a task's properties
 *
 * @param {string} taskId Task ID to update
 * @param {Object} updates Properties to update
 * @returns {Object} Updated task
 */
async function updateTask(taskId, updates) {
  logger.info(`Updating task: ${taskId}`);
  
  const task = getTask(taskId);
  
  // Apply updates
  Object.keys(updates).forEach(key => {
    // Don't allow updating certain fields
    if (['id', 'createdAt'].includes(key)) return;
    
    task[key] = updates[key];
  });
  
  // Update metadata
  task.updatedAt = new Date().toISOString();
  
  // Re-analyze if significant fields changed
  const significantChanges = ['title', 'description', 'priority', 'deadline'];
  const needsReanalysis = significantChanges.some(field => updates[field] !== undefined);
  
  if (needsReanalysis) {
    logger.info(`Re-analyzing task after updates: ${taskId}`);
    task.analysis = await analyzeTask(task);
    task.priorityScore = calculatePriorityScore(task, {
      complexity: task.analysis.complexity
    });
  }
  
  // Update in store
  taskStore.tasks.set(taskId, task);
  
  return task;
}

/**
 * Delete a task
 *
 * @param {string} taskId Task ID to delete
 * @returns {boolean} Success indicator
 */
function deleteTask(taskId) {
  logger.info(`Deleting task: ${taskId}`);
  
  if (!taskStore.tasks.has(taskId)) {
    logger.warn(`Task not found for deletion: ${taskId}`);
    return false;
  }
  
  taskStore.tasks.delete(taskId);
  return true;
}

/**
 * List all tasks, optionally filtered
 *
 * @param {Object} filters Optional filters to apply
 * @returns {Array} Matching tasks
 */
function listTasks(filters = {}) {
  let tasks = Array.from(taskStore.tasks.values());
  
  // Apply filters if provided
  if (Object.keys(filters).length > 0) {
    tasks = tasks.filter(task => {
      return Object.entries(filters).every(([key, value]) => {
        // Handle special cases
        if (key === 'status' && Array.isArray(value)) {
          return value.includes(task.status);
        }
        
        if (key === 'minPriority') {
          return (task.priority || 0) >= value;
        }
        
        if (key === 'maxPriority') {
          return (task.priority || 0) <= value;
        }
        
        // Default equality check
        return task[key] === value;
      });
    });
  }
  
  return tasks;
}

/**
 * Create a schedule for tasks
 *
 * @param {Object} options Schedule creation options
 * @returns {Object} Generated schedule
 */
function scheduleTaskExecution(options = {}) {
  logger.info('Creating task execution schedule');
  
  const {
    taskIds,
    startDate = new Date(),
    resources = { workers: 3 },
    scheduleId = `schedule-${Date.now()}`
  } = options;
  
  // Get tasks to schedule (either specified or all non-completed)
  let tasksToSchedule;
  
  if (taskIds && Array.isArray(taskIds)) {
    tasksToSchedule = taskIds
      .map(id => taskStore.tasks.get(id))
      .filter(task => task !== undefined);
  } else {
    tasksToSchedule = Array.from(taskStore.tasks.values())
      .filter(task => task.status !== 'completed');
  }
  
  if (tasksToSchedule.length === 0) {
    logger.warn('No tasks available for scheduling');
    return { scheduleId, tasks: [] };
  }
  
  // Create schedule
  const schedule = createSchedule(tasksToSchedule, resources, {
    startDate,
    workHoursPerDay: options.workHoursPerDay || 8,
    includeWeekends: options.includeWeekends || false
  });
  
  schedule.id = scheduleId;
  
  // Store schedule
  taskStore.schedules.set(scheduleId, schedule);
  
  return schedule;
}

/**
 * Get task statistics
 *
 * @returns {Object} Task system statistics
 */
function getTaskStats() {
  const tasks = Array.from(taskStore.tasks.values());
  
  // Count tasks by status
  const statusCounts = tasks.reduce((counts, task) => {
    const status = task.status || 'unknown';
    counts[status] = (counts[status] || 0) + 1;
    return counts;
  }, {});
  
  // Count tasks by priority
  const priorityCounts = tasks.reduce((counts, task) => {
    const priority = task.priority || 0;
    counts[priority] = (counts[priority] || 0) + 1;
    return counts;
  }, {});
  
  // Calculate average completion time for completed tasks
  const completedTasks = tasks.filter(task => task.status === 'completed' && task.completedAt);
  let avgCompletionTime = 0;
  
  if (completedTasks.length > 0) {
    const totalCompletionTime = completedTasks.reduce((total, task) => {
      const created = new Date(task.createdAt);
      const completed = new Date(task.completedAt);
      return total + (completed - created);
    }, 0);
    
    avgCompletionTime = totalCompletionTime / completedTasks.length / (1000 * 60 * 60); // In hours
  }
  
  return {
    totalTasks: tasks.length,
    statusCounts,
    priorityCounts,
    avgCompletionTimeHours: avgCompletionTime,
    oldestTaskAge: tasks.length > 0 ? 
      (Date.now() - new Date(Math.min(...tasks.map(t => new Date(t.createdAt))))) / (1000 * 60 * 60 * 24) : 
      0,
    activeSchedules: taskStore.schedules.size
  };
}

// OPTIONAL: Add an initialization function to clean up when shutting down
function shutdown() {
  return Promise.resolve();
}

module.exports = {
  createTask,
  getTask,
  updateTask,
  deleteTask,
  listTasks,
  scheduleTaskExecution,
  getTaskStats,
  shutdown
};
