/**
 * Task Scheduler Module
 *
 * Schedules tasks based on priority, available resources, and time constraints.
 */

const logger = require('../utils/logger');
const { sortByPriority } = require('../prioritizer');

/**
 * Create an optimal schedule for tasks given resource constraints
 *
 * @param {Array} tasks Tasks to schedule
 * @param {Object} resources Available resources
 * @param {Object} options Scheduling options
 * @returns {Object} Generated schedule
 */
function createSchedule(tasks, resources, options = {}) {
  logger.info(`Creating schedule for ${tasks.length} tasks`);

  const {
    startDate = new Date(),
    workHoursPerDay = 8,
    maxParallelTasks = 5,
    includeWeekends = false
  } = options;

  // Sort tasks by priority
  const sortedTasks = sortByPriority(tasks);

  // Initialize schedule
  const schedule = {
    generatedAt: new Date().toISOString(),
    startDate: startDate.toISOString(),
    tasks: [],
    summary: {
      totalTasks: tasks.length,
      totalDuration: 0,
      earliestStart: null,
      latestEnd: null
    }
  };

  // Current schedule time pointer
  let currentDate = new Date(startDate);

  // Track resource allocation
  const resourceAllocation = {};
  Object.keys(resources).forEach(resource => {
    resourceAllocation[resource] = {
      available: resources[resource],
      allocated: 0
    };
  });

  // Assign start and end times to each task
  sortedTasks.forEach(task => {
    const taskDuration = task.estimatedDuration ||
                      task.resourceRequirements?.estimatedTimeHours || 4;

    // Find the appropriate start time based on resource availability
    let startTime = new Date(currentDate);
    let endTime = calculateEndTime(startTime, taskDuration, workHoursPerDay, includeWeekends);

    // Check for resource conflicts and adjust if needed
    if (isResourceConflict(task, startTime, endTime, schedule.tasks)) {
      // Find next available slot if there's a conflict
      const nextSlot = findNextAvailableSlot(
        schedule.tasks, task, startTime, workHoursPerDay, includeWeekends
      );
      startTime = nextSlot.startTime;
      endTime = nextSlot.endTime;
    }

    // Add task to schedule
    schedule.tasks.push({
      id: task.id,
      title: task.title,
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      duration: taskDuration,
      priority: task.priority,
      resources: task.resources
    });

    // Update summary info
    schedule.summary.totalDuration += taskDuration;

    if (!schedule.summary.earliestStart || startTime < new Date(schedule.summary.earliestStart)) {
      schedule.summary.earliestStart = startTime.toISOString();
    }

    if (!schedule.summary.latestEnd || endTime > new Date(schedule.summary.latestEnd)) {
      schedule.summary.latestEnd = endTime.toISOString();
    }

    // Only move the current date pointer if this task would be the latest
    if (endTime > currentDate) {
      currentDate = new Date(endTime);
    }
  });

  return schedule;
}

/**
 * Calculate end time based on start time and duration
 */
function calculateEndTime(startTime, durationHours, workHoursPerDay, includeWeekends) {
  const endTime = new Date(startTime);
  let remainingHours = durationHours;

  while (remainingHours > 0) {
    // Skip weekends if not included
    if (!includeWeekends && (endTime.getDay() === 0 || endTime.getDay() === 6)) {
      endTime.setDate(endTime.getDate() + 1);
      continue;
    }

    const hoursToAdd = Math.min(remainingHours, workHoursPerDay);
    endTime.setHours(endTime.getHours() + hoursToAdd);
    remainingHours -= hoursToAdd;

    // If we've used up the day's work hours, move to next day
    if (endTime.getHours() >= 17) { // Assuming 9-5 workday
      endTime.setHours(9);
      endTime.setDate(endTime.getDate() + 1);
    }
  }

  return endTime;
}

/**
 * Check if there's a resource conflict for the proposed schedule
 */
function isResourceConflict(task, startTime, endTime, scheduledTasks) {
  // If task requires specific resources, check availability
  if (!task.resources || Object.keys(task.resources).length === 0) {
    return false; // No specific resources, so no conflict
  }

  return scheduledTasks.some(scheduledTask => {
    // Check if time periods overlap
    const scheduledStart = new Date(scheduledTask.startTime);
    const scheduledEnd = new Date(scheduledTask.endTime);

    const timeOverlap = !(
      endTime <= scheduledStart ||
      startTime >= scheduledEnd
    );

    if (!timeOverlap) return false;

    // If times overlap, check resource conflicts
    if (!scheduledTask.resources) return false;

    // Check for any shared resources
    return Object.keys(task.resources).some(resource =>
      scheduledTask.resources[resource] &&
      task.resources[resource] + scheduledTask.resources[resource] > 100
    );
  });
}

/**
 * Find the next available time slot
 */
function findNextAvailableSlot(scheduledTasks, task, afterTime, workHoursPerDay, includeWeekends) {
  let candidateStart = new Date(afterTime);
  let conflict = true;

  // Try incremental time slots until an available one is found
  while (conflict) {
    // Move to next slot (increments of 1 hour for simplicity)
    candidateStart.setHours(candidateStart.getHours() + 1);

    // Skip non-working hours
    if (candidateStart.getHours() < 9 || candidateStart.getHours() >= 17) {
      candidateStart.setHours(9);
      candidateStart.setDate(candidateStart.getDate() + 1);
    }

    // Skip weekends if not included
    if (!includeWeekends && (candidateStart.getDay() === 0 || candidateStart.getDay() === 6)) {
      candidateStart.setDate(candidateStart.getDate() + 1);
      candidateStart.setHours(9);
      continue;
    }

    const candidateEnd = calculateEndTime(
      candidateStart,
      task.estimatedDuration || task.resourceRequirements?.estimatedTimeHours || 4,
      workHoursPerDay,
      includeWeekends
    );

    conflict = isResourceConflict(task, candidateStart, candidateEnd, scheduledTasks);

    if (!conflict) {
      return {
        startTime: candidateStart,
        endTime: candidateEnd
      };
    }
  }
}

/**
 * Get schedule statistics
 *
 * @param {Object} schedule Schedule to analyze
 * @returns {Object} Schedule statistics
 */
function getScheduleStats(schedule) {
  if (!schedule || !schedule.tasks || schedule.tasks.length === 0) {
    return {
      isEmpty: true
    };
  }

  const stats = {
    taskCount: schedule.tasks.length,
    totalDuration: 0,
    averageDuration: 0,
    earliestTask: null,
    latestTask: null,
    resourceUtilization: {}
  };

  let earliestTime = new Date(schedule.tasks[0].startTime);
  let latestTime = new Date(schedule.tasks[0].endTime);

  schedule.tasks.forEach(task => {
    // Track total and find earliest/latest
    stats.totalDuration += task.duration;

    const startTime = new Date(task.startTime);
    const endTime = new Date(task.endTime);

    if (startTime < earliestTime) {
      earliestTime = startTime;
      stats.earliestTask = task.id;
    }

    if (endTime > latestTime) {
      latestTime = endTime;
      stats.latestTask = task.id;
    }

    // Track resource utilization
    if (task.resources) {
      Object.keys(task.resources).forEach(resource => {
        if (!stats.resourceUtilization[resource]) {
          stats.resourceUtilization[resource] = 0;
        }
        stats.resourceUtilization[resource] +=
          (task.resources[resource] * task.duration);
      });
    }
  });

  stats.averageDuration = stats.totalDuration / stats.taskCount;
  stats.scheduleSpan = {
    start: earliestTime.toISOString(),
    end: latestTime.toISOString(),
    durationHours: (latestTime - earliestTime) / (1000 * 60 * 60)
  };

  return stats;
}

/**
 * Get the current status of the scheduler
 *
 * @returns {Object} Current scheduler status
 */
async function getStatus() {
  try {
    // This would typically query a database or in-memory state
    // For now, we'll return a mock status
    return {
      activeSchedule: true,
      activeTasks: 5,
      pendingTasks: 12,
      completedTasks: 28,
      currentLoad: 65, // percentage
      nextScheduledTask: new Date(Date.now() + 1800000).toISOString(), // 30 minutes from now
      timeWindows: await getTimeWindows(),
      lastUpdated: new Date().toISOString()
    };
  } catch (error) {
    logger.error('Error getting scheduler status', { error });
    throw error;
  }
}

/**
 * Get the configured time windows for task scheduling
 *
 * @returns {Array} Time windows configuration
 */
async function getTimeWindows() {
  try {
    // This would typically come from a database or configuration
    // For now, we'll return default time windows
    return [
      {
        name: 'Business Hours',
        priority: 'high',
        startTime: '09:00',
        endTime: '17:00',
        daysOfWeek: [1, 2, 3, 4, 5], // Monday to Friday
        resourceAllocation: 100 // percentage
      },
      {
        name: 'Evening Hours',
        priority: 'medium',
        startTime: '17:00',
        endTime: '22:00',
        daysOfWeek: [1, 2, 3, 4, 5], // Monday to Friday
        resourceAllocation: 50 // percentage
      },
      {
        name: 'Weekend Hours',
        priority: 'low',
        startTime: '10:00',
        endTime: '16:00',
        daysOfWeek: [0, 6], // Sunday and Saturday
        resourceAllocation: 30 // percentage
      }
    ];
  } catch (error) {
    logger.error('Error getting time windows', { error });
    throw error;
  }
}

/**
 * Update the time windows configuration
 *
 * @param {Array} windows New time windows configuration
 * @returns {Array} Updated time windows
 */
async function updateTimeWindows(windows) {
  try {
    // This would typically update a database or configuration
    // For now, we'll just return the input
    logger.info('Time windows updated', { windowCount: windows.length });
    return windows;
  } catch (error) {
    logger.error('Error updating time windows', { error });
    throw error;
  }
}

/**
 * Shutdown the scheduler
 */
async function shutdown() {
  logger.info('Shutting down scheduler');
  // Perform any cleanup needed
  return true;
}

module.exports = {
  createSchedule,
  getScheduleStats,
  getStatus,
  getTimeWindows,
  updateTimeWindows,
  shutdown
};
