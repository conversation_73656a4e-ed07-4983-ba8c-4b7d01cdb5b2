/**
 * Task Prioritizer Module
 * 
 * Prioritizes tasks based on multiple factors including
 * deadline, importance, dependencies, and resource availability.
 */

const logger = require('../utils/logger');

/**
 * Calculate task priority score based on multiple factors
 *
 * @param {Object} task Task object to prioritize
 * @param {Object} options Additional options for prioritization
 * @returns {Number} Priority score (higher = more urgent)
 */
function calculatePriorityScore(task, options = {}) {
  logger.info(`Calculating priority for task: ${task.id}`);
  
  const {
    userPriority = task.priority || 1,
    deadline = task.deadline,
    dependencies = task.dependencies || [],
    importance = task.importance || 1,
    complexity = task.complexity || { score: 1 }
  } = options;
  
  // Base score starts with user-assigned priority (1-5)
  let priorityScore = userPriority * 5;
  
  // Add deadline factor
  if (deadline) {
    const deadlineDate = new Date(deadline);
    const now = new Date();
    const daysRemaining = (deadlineDate - now) / (1000 * 60 * 60 * 24);
    
    if (daysRemaining < 0) {
      // Overdue tasks get maximum priority
      priorityScore += 50;
    } else if (daysRemaining < 1) {
      // Due today
      priorityScore += 40;
    } else if (daysRemaining < 3) {
      // Due in next 3 days
      priorityScore += 30;
    } else if (daysRemaining < 7) {
      // Due this week
      priorityScore += 20;
    } else if (daysRemaining < 14) {
      // Due in next two weeks
      priorityScore += 10;
    }
  }
  
  // Factor in dependencies
  if (dependencies.length > 0) {
    // Tasks that block others are more important
    priorityScore += dependencies.length * 5;
  }
  
  // Importance factor (1-10 scale)
  priorityScore += importance * 3;
  
  // Complexity factor
  if (complexity) {
    // More complex tasks may need to start earlier
    priorityScore += complexity.score;
  }
  
  return Math.min(100, priorityScore);
}

/**
 * Sort tasks by priority
 *
 * @param {Array} tasks Array of tasks to sort
 * @returns {Array} Sorted array of tasks
 */
function sortByPriority(tasks) {
  return [...tasks].sort((a, b) => {
    const scoreA = a.priorityScore || calculatePriorityScore(a);
    const scoreB = b.priorityScore || calculatePriorityScore(b);
    
    return scoreB - scoreA; // Higher score = higher priority
  });
}

/**
 * Assign priority categories to tasks
 * 
 * @param {Array} tasks Array of tasks to categorize
 * @returns {Object} Tasks grouped by priority category
 */
function categorizeByPriority(tasks) {
  const categories = {
    critical: [],
    high: [],
    medium: [],
    low: []
  };
  
  tasks.forEach(task => {
    const score = task.priorityScore || calculatePriorityScore(task);
    
    if (score >= 75) {
      categories.critical.push(task);
    } else if (score >= 50) {
      categories.high.push(task);
    } else if (score >= 25) {
      categories.medium.push(task);
    } else {
      categories.low.push(task);
    }
  });
  
  return categories;
}

module.exports = {
  calculatePriorityScore,
  sortByPriority,
  categorizeByPriority
};
