/**
 * Task Scheduling Routes
 */
const express = require('express');
const router = express.Router();
const scheduler = require('../scheduler');
const logger = require('../utils/logger');

// Simple placeholder route
router.get('/', (req, res) => {
  res.json({ message: 'Scheduling API is working' });
});

/**
 * GET /api/scheduler/status
 *
 * Returns the current status of the scheduler including active tasks and time windows
 */
router.get('/status', async (req, res, next) => {
  try {
    const status = await scheduler.getStatus();

    res.status(200).json({
      status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error(`Error getting scheduler status: ${error.message}`, { error, requestId: req.id });
    next(error);
  }
});

/**
 * GET /api/scheduler/windows
 *
 * Returns the configured time windows for task scheduling
 */
router.get('/windows', async (req, res, next) => {
  try {
    const windows = await scheduler.getTimeWindows();

    res.status(200).json({
      windows,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error(`Error getting scheduler time windows: ${error.message}`, { error, requestId: req.id });
    next(error);
  }
});

/**
 * POST /api/scheduler/windows
 *
 * Updates the time windows configuration
 */
router.post('/windows', async (req, res, next) => {
  try {
    const windows = req.body;

    // Validate windows
    if (!windows || !Array.isArray(windows) || windows.length === 0) {
      return res.status(400).json({
        error: {
          message: 'Invalid time windows configuration',
          requestId: req.id
        }
      });
    }

    const updatedWindows = await scheduler.updateTimeWindows(windows);

    res.status(200).json({
      windows: updatedWindows,
      message: 'Time windows updated successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error(`Error updating scheduler time windows: ${error.message}`, { error, requestId: req.id });
    next(error);
  }
});

module.exports = router;
