{"name": "Intelligenter Telefonie-Workflow mit Google-Integration", "nodes": [{"parameters": {"webhookId": "vapi-incoming-call", "public": true, "options": {"responseMode": "responseNode", "allowedOrigins": ["*"], "title": "Intelligenter Telefonassistent", "subtitle": "Anrufverarbeitung mit Google-Integration", "loadPreviousSession": "memory"}}, "id": "vapi-webhook-trigger", "name": "Eingehender <PERSON>", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [0, 0]}, {"parameters": {"content": "# Anrufbearbeitungs-Workflow\n\nDieser Workflow verarbeitet eingehende Anrufe und integriert Daten aus Google-Diensten:\n\n1. An<PERSON>f empfangen über Webhook\n2. Anruferinformationen extrahieren\n3. Google Kontakte nach der Nummer durchsuchen\n4. Kalendertermine prüfen\n5. E-Mails abfragen\n6. Konversation entsprechend steuern\n7. An<PERSON><PERSON> protokollieren", "height": 260, "width": 300, "color": 4}, "id": "workflow-description", "name": "Workflow-Beschreibung", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, -200]}, {"parameters": {"functionCode": "// Extrahiert Anruferinformationen und bereitet sie für weitere Verarbeitung vor\ntry {\n  const incomingCall = $input.item.json;\n\n  // Standardwerte für unbekannte oder fehlende Informationen\n  const data = {\n    callId: incomingCall.call_id || incomingCall.id || 'unknown-call-' + Date.now(),\n    callerNumber: incomingCall.caller_number || incomingCall.from || 'unknown-number',\n    callerInput: incomingCall.user_input || '',\n    callStage: incomingCall.stage || 'greeting',\n    timestamp: new Date().toISOString()\n  };\n\n  // Bereinige Telefonnummer für die Google Kontakte-Abfrage\n  if (data.callerNumber !== 'unknown-number') {\n    // Entferne Ländercode und Formatierung für die Suche\n    data.callerNumberSearch = data.callerNumber\n      .replace(/^\\+?\\d{1,3}[\\s-]?/, '') // Entferne Ländercode\n      .replace(/[\\s()-]/g, '');       // Entferne Formatierung\n  }\n\n  // Konversation im Call-Log speichern\n  if (incomingCall.user_input) {\n    data.conversationHistory = [\n      {\n        role: 'user',\n        content: incomingCall.user_input\n      }\n    ];\n  }\n\n  return { json: data };\n} catch (error) {\n  console.error('Fehler beim <PERSON>rbeiten der Anrufdaten:', error);\n  return {\n    json: {\n      callId: 'error-' + Date.now(),\n      callerNumber: 'error',\n      callStage: 'error',\n      error: error.message\n    }\n  };\n}"}, "id": "call-data-processor", "name": "Anrufdaten Aufbereiten", "type": "n8n-nodes-base.function", "typeVersion": 3, "position": [240, 0]}, {"parameters": {"resource": "contact", "operation": "search", "query": "={{ $json.callerNumberSearch }}"}, "id": "google-contacts-node", "name": "Google Kontakte durchsuchen", "type": "n8n-nodes-base.googleContacts", "typeVersion": 1, "position": [460, 0], "credentials": {"googleContactsOAuth2Api": {"id": "google-contacts-creds", "name": "Google Kontakte Account"}}}, {"parameters": {"mode": "mergeByPosition", "join": true, "outputDataFrom": "input1", "options": {"multipleMatches": "first"}}, "id": "merge-contacts-data", "name": "Kontaktdaten zusammenführen", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [680, 0]}, {"parameters": {"content": "# Kontaktdaten-Verarbeitung\n\nDieser Abschnitt extrahiert und verarbeitet Kontaktdaten:\n\n1. <PERSON><PERSON><PERSON><PERSON> <PERSON> in Google Kontakten zu identifizieren\n2. Extrahiere wichtige Kontaktinformationen für die Gesprächssteuerung\n3. Bereite die Daten für die Konversationslogik auf\n\nDie Kontaktdaten werden im nächsten Schritt mit Kalender- und E-Mail-Daten angereichert.", "height": 200, "width": 300, "color": 3}, "id": "contact-processing-description", "name": "Kontaktverarbeitung", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [460, -200]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ Object.keys($json.connections || {}).length > 0 }}", "value2": true}]}}, "id": "if-known-caller", "name": "<PERSON><PERSON><PERSON>?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [900, 0]}, {"parameters": {"functionCode": "// Kontaktdaten für bekannte Anrufer aufbereiten\ntry {\n  const data = $input.item.json;\n  const contact = data.connections?.[0] || {};\n  \n  const name = contact.names?.[0]?.displayName || 'Unbekannt';\n  const emails = contact.emailAddresses || [];\n  const primaryEmail = emails.find(email => email.metadata?.primary) || emails[0] || {};\n  const phoneNumbers = contact.phoneNumbers || [];\n  const organizations = contact.organizations || [];\n  const currentOrganization = organizations.find(org => org.metadata?.primary) || organizations[0] || {};\n  \n  return {\n    json: {\n      ...data,\n      callerName: name,\n      callerEmail: primaryEmail.value || '',\n      callerCompany: currentOrganization.name || '',\n      callerJobTitle: currentOrganization.title || '',\n      callerPhones: phoneNumbers.map(phone => ({ type: phone.type, number: phone.value })),\n      knownCaller: true,\n      resourceName: contact.resourceName || ''\n    }\n  };\n} catch (error) {\n  console.error('<PERSON><PERSON> beim <PERSON> bekannter Anrufer:', error);\n  return {\n    json: {\n      ...($input.item.json || {}),\n      callerName: '<PERSON><PERSON> bei Kontaktdaten',\n      error: error.message,\n      knownCaller: false\n    }\n  };\n}"}, "id": "process-known-caller", "name": "Bekannten Anrufer aufbereiten", "type": "n8n-nodes-base.function", "typeVersion": 3, "position": [1100, -100]}, {"parameters": {"functionCode": "// Standarddaten für unbekannte Anrufer\\ntry {\\n  const data = $input.item.json;\\n\\n  return {\\n    json: {\\n      ...data,\\n      callerName: '',\\n      callerEmail: '',\\n      callerCompany: '',\\n      callerJobTitle: '',\\n      callerPhones: [{ type: 'other', number: data.callerNumber }],\\n      knownCaller: false,\\n      resourceName: ''\\n    }\\n  };\\n} catch (error) {\\n  console.error('<PERSON>hler beim Verarbeiten unbekannter Anrufer:', error);\\n  return {\\n    json: {\\n      ...($input.item.json || {}),\\n      callerName: '',\\n      knownCaller: false,\\n      error: error.message\\n    }\\n  };\\n}"}, "id": "process-unknown-caller", "name": "Unbekannten Anrufer aufbereiten", "type": "n8n-nodes-base.function", "typeVersion": 3, "position": [1100, 100]}, {"parameters": {"mode": "passThrough"}, "id": "join-caller-data", "name": "Anruferdaten zusammenführen", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [1320, 0]}, {"parameters": {"resource": "calendar", "operation": "getAll", "calendarId": "primary", "timeMin": "={{ new Date().toISOString() }}", "timeMax": "={{ new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() }}", "options": {"maxResults": 5}}, "id": "google-calendar-node", "name": "Google Kalender abrufen", "type": "n8n-nodes-base.googleCalendar", "typeVersion": 1, "position": [1320, 200], "credentials": {"googleCalendarOAuth2Api": {"id": "google-calendar-creds", "name": "Google Kalender Account"}}}, {"parameters": {"resource": "message", "operation": "getAll", "limit": 5, "filters": {"q": "={{ $json.knownCaller && $json.callerEmail ? 'from:' + $json.callerEmail : '' }}"}, "options": {"includeSpamTrash": false}}, "id": "google-mail-node", "name": "Google Mail abrufen", "type": "n8n-nodes-base.gmail", "typeVersion": 2, "position": [1320, 400], "credentials": {"gmailOAuth2Api": {"id": "gmail-creds", "name": "Google Mail Account"}}}, {"parameters": {"content": "# Daten-Integration\n\nDieser Bereich führt alle relevanten Daten zusammen:\n\n1. Google Kontakte für Anruferinformationen\n2. Google Kalender für Termine in den nächsten 24 Stunden\n3. Gmail für kürzliche E-Mail-Kommunikation\n\nDiese Daten werden dem KI-Agenten zur Verfügung gestellt, um intelligente Gesprächsführung zu ermöglichen.", "height": 200, "width": 550, "color": 2}, "id": "data-integration-description", "name": "Daten-Integration", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1100, -320]}, {"parameters": {"functionCode": "// Bereitet integrierte Daten für den Konversationsagenten vor\ntry {\n  const callerData = $input.item.json;\n  \n  // Extrahiere Kalenderdaten vom Google Kalender Node\n  const calendarNode = $node['google-calendar-node'];\n  const calendarItems = calendarNode ? (calendarNode.json?.items || []) : [];\n  \n  // Extrahiere Mail-Daten vom Gmail Node\n  const mailNode = $node['google-mail-node'];\n  const mailItems = mailNode ? (mailNode.json?.messages || []) : [];\n  \n  // Formatiere Kalenderereignisse\n  const upcomingEvents = calendarItems.slice(0, 3).map(event => ({\n    title: event.summary || 'Unbenannter Termin',\n    start: event.start?.dateTime || event.start?.date || 'Unbekannt',\n    end: event.end?.dateTime || event.end?.date || 'Unbekannt',\n    attendees: (event.attendees || []).map(a => a.email).join(', ')\n  }));\n  \n  // Formatiere E-Mails\n  const recentEmails = mailItems.slice(0, 3).map(mail => ({\n    subject: mail.payload?.headers?.find(h => h.name === 'Subject')?.value || 'Kein Betreff',\n    date: mail.payload?.headers?.find(h => h.name === 'Date')?.value || 'Unbekanntes Datum',\n    from: mail.payload?.headers?.find(h => h.name === 'From')?.value || 'Unbekannter Absender'\n  }));\n  \n  // Bereite integrierte Daten vor\n  return {\n    json: {\n      ...callerData,\n      upcomingEvents,\n      recentEmails,\n      hasUpcomingEvents: upcomingEvents.length > 0,\n      hasRecentEmails: recentEmails.length > 0,\n      lastContactDate: recentEmails.length > 0 ? recentEmails[0].date : 'Keine kürzliche Kommunikation'\n    }\n  };\n} catch (error) {\n  console.error('Fehler bei der Datenintegration:', error);\n  return {\n    json: {\n      ...($input.item.json || {}),\n      error: error.message,\n      upcomingEvents: [],\n      recentEmails: [],\n      hasUpcomingEvents: false,\n      hasRecentEmails: false\n    }\n  };\n}"}, "id": "data-integration", "name": "Daten integrieren", "type": "n8n-nodes-base.function", "typeVersion": 3, "position": [1540, 0]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.callStage }}", "value2": "greeting"}]}}, "id": "check-call-stage", "name": "Anrufphase prüfen", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1760, 0]}, {"parameters": {"content": "# Konversations-Management\n\nDieser Abschnitt verwaltet die Anrufphasen:\n\n1. Begrüßung für neue Anrufe\n2. Gesprächsführung für laufende Anrufe\n3. Weitergabe von relevanten Kalenderterminen und E-Mails\n4. Intelligente Entscheidungsfindung basierend auf Anrufkontext\n\nDie KI-Agenten nutzen die zuvor integrierten Daten, um personalisierte Antworten zu generieren.", "height": 220, "width": 300, "color": 1}, "id": "conversation-management-description", "name": "Konversations-Management", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1760, -240]}, {"parameters": {"promptType": "define", "text": "={{ $json.callerInput }}", "options": {"systemMessage": "# Intelligenter Telefonassistent - Begrüßung\n\nDu bist ein professioneller Telefonassistent. Deine Aufgabe ist es, eingehende Anrufe freundlich zu begrüßen und den Anrufer zu identifizieren.\n\n## Aktueller Anrufer\nNummer: {{ $json.callerNumber }}\nName: {{ $json.callerName || 'Noch nicht bekannt' }}\nFirma: {{ $json.callerCompany || '' }}\nBekannter Kontakt: {{ $json.knownCaller ? 'Ja' : 'Nein' }}\n\n## Begrüßungsrichtlinien\n\n{{#if $json.knownCaller}}\n- Begrüße den Anrufer mit seinem Namen: \"Guten Tag Herr/Frau {{$json.callerName}}, schön wieder von <PERSON>en zu hören!\"\n- Erwähne, wenn passend, den letzten Kontakt: \"Wir hatten zuletzt Kontakt via E-Mail am {{$json.lastContactDate}}.\"\n- Falls Kalendertermine anstehen: \"Ich sehe, dass Sie einen Termin haben: {{$json.upcomingEvents[0].title}} um {{$json.upcomingEvents[0].start}}.\"\n{{else}}\n- Begrüße den Anrufer allgemein: \"Guten Tag, hier ist der virtuelle Assistent. Mit wem spreche ich?\"\n{{/if}}\n\n- Stelle dich als virtueller Assistent vor\n- Frage höflich nach dem Grund des Anrufs\n- Halte die Begrüßung kurz und professionell\n\nAntworte nur mit dem Begrüßungstext. Die weitere Gesprächsführung erfolgt in den nächsten Schritten."}}, "id": "greeting-agent", "name": "Begrüßungs-Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1980, -100]}, {"parameters": {"promptType": "define", "text": "={{ $json.callerInput }}", "options": {"systemMessage": "# Intelligenter Telefonassistent - Gesprächsführung\n\nDu bist ein professioneller Telefonassistent. Du führst gerade ein Gespräch mit einem Anrufer und hast Zugriff auf Kontakt-, Kalender- und E-Mail-Daten.\n\n## Aktueller Anrufer\nName: {{ $json.callerName || 'Noch nicht bekannt' }}\nFirma: {{ $json.callerCompany || '' }}\nE-Mail: {{ $json.callerEmail || '' }}\nBekannter Kontakt: {{ $json.knownCaller ? 'Ja' : 'Nein' }}\n\n## Verfügbare Daten\n\n{{#if $json.hasUpcomingEvents}}\nBevorstehende Termine (max. 3):\n{{#each $json.upcomingEvents}}\n- {{this.title}}: {{this.start}}\n{{/each}}\n{{/if}}\n\n{{#if $json.hasRecentEmails}}\nAktuelle E-Mails (max. 3):\n{{#each $json.recentEmails}}\n- {{this.subject}}: {{this.date}}\n{{/each}}\n{{/if}}\n\n## Gesprächsführung\n\nWenn der Name des Anrufers noch nicht bekannt ist, erfrage ihn höflich.\n\nErfrage den Grund des Anrufs und hilf entsprechend:\n\n1. Bei Terminanfragen - Verweise auf bevorstehende Termine oder biete an, einen neuen Termin zu vereinbaren\n2. Bei E-Mail-Anfragen - Informiere über kürzliche E-Mails oder biete an, eine Nachricht weiterzuleiten\n3. Bei anderen Anfragen - Biete an, eine Nachricht zu hinterlassen\n\nVermeide technische Details. Deine Antworten sollten kurz, höflich und hilfreich sein.\n\nAnalysiere die Eingabe des Anrufers und entscheide über die nächsten Schritte. Du kannst auch anbieten, den Anruf weiterzuleiten, wenn es sinnvoll erscheint."}}, "id": "conversation-agent", "name": "Gesprächsführungs-Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1980, 100]}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.callId }}", "contextWindowLength": 10}, "id": "memory-buffer", "name": "Gesprächsgedächtnis", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [1760, 200]}, {"parameters": {"model": "gpt-4o", "options": {}}, "id": "openai-model", "name": "OpenAI Chat Modell", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1760, 400], "credentials": {"openAiApi": {"id": "openai-key-id", "name": "OpenAI API Key"}}}, {"parameters": {}}, {"parameters": {"functionCode": "// Extrahiert Informationen aus der Konversation und aktualisiert die Anrufphase\ntry {\n  const data = $input.item.json;\n  const conversationText = data.output || '';\n\n  // Analysiere die Antwort, um relevante Informationen zu extrahieren\n  let callerName = data.callerName;\n  let callReason = '';\n  let callUrgency = 'normal';\n  let needsCallback = false;\n  let needsAppointment = false;\n  let wantsTransfer = false;\n\n  // Dringlichkeitsanalyse\n  if (conversationText.includes('dringend') || \n      conversationText.includes('sofort') || \n      conversationText.includes('eilig')) {\n    callUrgency = 'high';\n  }\n\n  // Extrahiere Namen, falls noch nicht bekannt\n  if (!callerName || callerName === '') {\n    const nameMatch = conversationText.match(/(?:ich bin|mein name ist|hier spricht) ([\\w\\s-]+)/i);\n    if (nameMatch && nameMatch[1]) {\n      callerName = nameMatch[1].trim();\n    }\n  }\n\n  // Anliegen-Analyse\n  if (conversationText.includes('termin') || \n      conversationText.includes('treffen') || \n      conversationText.includes('meeting')) {\n    needsAppointment = true;\n    callReason = 'Terminanfrage';\n  } else if (conversationText.includes('rückruf') || \n             conversationText.includes('später anrufen')) {\n    needsCallback = true;\n    callReason = 'Rückrufbitte';\n  } else if (conversationText.includes('durchstellen') || \n             conversationText.includes('verbinden') || \n             conversationText.includes('sprechen mit')) {\n    wantsTransfer = true;\n    callReason = 'Verbindungsanfrage';\n  } else {\n    // Extrahiere allgemeinen Anrufgrund\n    const reasonPhrases = [\n      'wegen',\n      'bezüglich',\n      'Grund meines Anrufs',\n      'ich rufe an, um',\n      'ich möchte',\n      'es geht um'\n    ];\n\n    for (const phrase of reasonPhrases) {\n      const regex = new RegExp(`${phrase}\\\\s+([^.!?]+)`, 'i');\n      const match = conversationText.match(regex);\n      if (match && match[1]) {\n        callReason = match[1].trim();\n        break;\n      }\n    }\n  }\n\n  // Falls kein expliziter Grund gefunden wurde, verwende den gesamten Text\n  if (!callReason) {\n    // Begrenze auf max. 100 Zeichen\n    callReason = conversationText.substring(0, 100) + (conversationText.length > 100 ? '...' : '');\n  }\n\n  // Update conversation history\n  const conversationHistory = data.conversationHistory || [];\n  conversationHistory.push({\n    role: 'assistant',\n    content: conversationText\n  });\n\n  // Aktualisiere und gib die Daten zurück\n  return {\n    json: {\n      ...data,\n      callerName: callerName || data.callerName || 'Unbekannt',\n      callReason: callReason,\n      callUrgency: callUrgency,\n      callStage: 'information_gathered',\n      needsCallback: needsCallback,\n      needsAppointment: needsAppointment,\n      wantsTransfer: wantsTransfer,\n      conversationHistory: conversationHistory\n    }\n  };\n} catch (error) {\n  console.error('Fehler beim Extrahieren von Informationen:', error);\n  return {\n    json: {\n      ...($input.item.json || {}),\n      error: error.message,\n      callStage: 'error'\n    }\n  };\n}"}, "id": "info-extractor", "name": "<PERSON><PERSON>n", "type": "n8n-nodes-base.function", "typeVersion": 3, "position": [2200, 0]}, {"parameters": {"url": "https://api.example.com/notification", "method": "POST", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"callId\": \"{{ $json.callId }}\",\n  \"callerName\": \"{{ $json.callerName }}\",\n  \"callerNumber\": \"{{ $json.callerNumber }}\",\n  \"callerCompany\": \"{{ $json.callerCompany || 'Nicht angegeben' }}\",\n  \"callReason\": \"{{ $json.callReason }}\",\n  \"callUrgency\": \"{{ $json.callUrgency }}\",\n  \"needsCallback\": {{ $json.needsCallback }},\n  \"needsAppointment\": {{ $json.needsAppointment }},\n  \"wantsTransfer\": {{ $json.wantsTransfer }},\n  \"knownContact\": {{ $json.knownCaller }},\n  \"timestamp\": \"{{ $now }}\"\n}", "options": {"redirect": {"redirect": true}}}, "id": "send-notification", "name": "Benachrichtigung senden", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2420, 0], "credentials": {"httpHeaderAuth": {"id": "call-api-creds", "name": "Call API Credentials"}}}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.needsAppointment }}", "value2": true}]}}, "id": "check-appointment-needed", "name": "<PERSON><PERSON><PERSON>?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [2640, 0]}, {"parameters": {"resource": "event", "operation": "create", "calendarId": "primary", "start": "={{ new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() }}", "end": "={{ new Date(Date.now() + 25 * 60 * 60 * 1000).toISOString() }}", "useDefaultReminders": true, "summary": "={{ 'Rückruf: ' + $json.callerName }}", "description": "={{ '<PERSON><PERSON><PERSON> von: ' + $json.callerName + '\\nTelefon: ' + $json.callerNumber + '\\nAnrufgrund: ' + $json.callReason }}", "attendees": {"attendee": [{"email": "={{ $json.callerEmail }}", "name": "={{ $json.callerName }}"}]}}, "id": "create-calendar-event", "name": "Kalendereintrag erstellen", "type": "n8n-nodes-base.googleCalendar", "typeVersion": 1, "position": [2860, -100], "credentials": {"googleCalendarOAuth2Api": {"id": "google-calendar-creds", "name": "Google Kalender Account"}}}, {"parameters": {"resource": "message", "operation": "send", "subject": "={{ '<PERSON><PERSON>rufanfrage von ' + $json.callerName }}", "to": "empfä****************", "options": {"includeAttachments": false}, "text": "={{ 'Anrufer: ' + $json.callerName + '\\nTelefon: ' + $json.callerNumber + '\\nFirma: ' + ($json.callerCompany || 'Nicht angegeben') + '\\nAnrufgrund: ' + $json.callReason + '\\nDringlichkeit: ' + $json.callUrgency + '\\nZeitpunkt: ' + $now }}"}, "id": "send-email-notification", "name": "E-Mail Benachrichtigung senden", "type": "n8n-nodes-base.gmail", "typeVersion": 2, "position": [2860, 100], "credentials": {"gmailOAuth2Api": {"id": "gmail-creds", "name": "Google Mail Account"}}}, {"parameters": {"mode": "passThrough"}, "id": "merge-actions", "name": "Aktionen zusammenführen", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [3080, 0]}, {"parameters": {"promptType": "define", "text": "", "options": {"systemMessage": "# Intelligenter Telefonassistent - Abschluss\n\nDu bist ein professioneller Telefonassistent. Der Anruf geht zu <PERSON> und du musst passend abschließen.\n\n## Anrufdetails\nName: {{ $json.callerName }}\nGrund: {{ $json.callReason }}\nTermin erstellt: {{ $json.needsAppointment ? 'Ja' : 'Nein' }}\nRückrufanfrage: {{ $json.needsCallback ? 'Ja' : 'Nein' }}\n\n## Abschlussrichtlinien\n\n{{#if $json.needsAppointment}}\n- Bestätige die Terminerstellung: \"Ich habe einen Termin für Sie eingetragen. Sie erhalten in Kürze eine Bestätigung per E-Mail.\"\n{{/if}}\n\n{{#if $json.needsCallback}}\n- Bestätige die Rückrufanfrage: \"Ihre Rückrufanfrage wurde notiert. Jemand wird sich bei Ihnen melden.\"\n{{/if}}\n\n{{#if $json.wantsTransfer}}\n- Informiere über die Weiterleitung: \"Ich werde Ihren Anruf jetzt weiterleiten. Bitte bleiben Sie in der Leitung.\"\n{{/if}}\n\n- <PERSON><PERSON> dem Anrufer für seinen Anruf\n- Wünsche einen schönen Tag\n- Verabschiede dich höflich\n\nDeine Antwort sollte kurz, freundlich und abschließend sein."}}, "id": "conclusion-agent", "name": "A<PERSON><PERSON><PERSON>s-Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [3300, 0]}, {"parameters": {"method": "POST", "url": "https://api.example.com/call/action", "authentication": "genericCredentialType", "sendBody": true, "specifyBody": "json", "jsonBody": "{\\n  \"call_id\": \"{{ $json.callId }}\",\\n  \"action\": \"{{ $json.wantsTransfer ? 'transfer' : 'speak' }}\",\\n  \"destination\": \"{{ $json.wantsTransfer ? '+**********' : '' }}\",\\n  \"content\": \"{{ $json.output }}\"\\n}"}, "id": "call-action", "name": "Anrufaktion ausführen", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [3520, 0], "credentials": {"httpHeaderAuth": {"id": "call-api-creds", "name": "Call API Credentials"}}}, {"parameters": {"operation": "append", "dataMode": "defineBelow", "spreadsheetId": "{{ $env.GOOGLE_SHEET_ID }}", "range": "A:G", "options": {"valueInputMode": "USER_ENTERED"}, "data": {"values": [["={{ $json.callId }}", "={{ $json.callerName }}", "={{ $json.callerNumber }}", "={{ $json.callerCompany || '' }}", "={{ $json.callReason }}", "={{ $json.callUrgency }}", "={{ $now }}"]]}}, "id": "log-call", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [3740, 0], "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-creds", "name": "Google Sheets Account"}}}, {"parameters": {"responseData": {"responseDataType": "lastNode", "options": {}}}, "id": "response-node", "name": "Antwort zurückgeben", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [3960, 0]}, {"parameters": {"content": "## Platzhalter URL\n\nDie URL in diesem HTTP Request Node (`https://api.example.com/notification`) ist ein Platzhalter und muss durch die tatsächliche Endpunkt-URL für Benachrichtigungen ersetzt werden.", "height": 150, "width": 300, "color": 6}, "id": "placeholder-notification-note", "name": "Platzhalter Hinweis (Benachrichtigung)", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2420, -200]}, {"parameters": {"content": "## Platzhalter URL und Ziel\n\nDie URL in diesem HTTP Request Node (`https://api.example.com/call/action`) und die Zielnummer (`+**********`) sind Platzhalter und müssen durch die tatsächlichen Werte für die VAPI Call API ersetzt werden.", "height": 150, "width": 350, "color": 6}, "id": "placeholder-call-action-note", "name": "Platzhalter Hinweis (Anrufaktion)", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [3520, -200]}, {"parameters": {"content": "## Platzhalter E-Mail-Adresse\n\nDie Empfänger-E-Mail-Adresse (`empfä****************`) in diesem Gmail Node ist ein Platzhalter und muss durch die tatsächliche E-Mail-Adresse ersetzt werden.", "height": 150, "width": 300, "color": 6}, "id": "placeholder-email-note", "name": "Platzhalter Hinweis (E-Mail)", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2860, 300]}, {"parameters": {"content": "## Verbindung prü<PERSON>\n\nStellen <PERSON>, dass dieser 'Antwort zurückgeben' Node mit dem 'Anrufaktion ausführen' Node verbunden ist, um die korrekte Antwort an die VAPI API zurückzugeben.", "height": 150, "width": 300, "color": 5}, "id": "response-node-connection-note", "name": "Verbindungshinweis (Antwort)", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [3960, -200]}], "connections": []}