# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Data directories
data/
logs/

# Build files
dist/
build/
*.tsbuildinfo

# IDE files
.idea/
.vscode/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Docker
.docker/

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Logs
*.log

# Dependency directories
jspm_packages/
bower_components/

# Coverage directory
coverage/

# Debug files
.nyc_output/

# Misc
.cache/
.project
.settings/
.tmproj
*.esproj
nbproject/
*.sublime-project
*.sublime-workspace
