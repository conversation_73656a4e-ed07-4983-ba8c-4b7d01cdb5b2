
# Application Configuration
VITE_APP_ENV=development
VITE_APP_NAME="Eigen CRM Schmiede"
VITE_APP_VERSION=1.0.0

# Database Configuration (choose one)
# Option 1: Local Supabase (recommended for development)
VITE_SUPABASE_URL=http://localhost:54321
VITE_SUPABASE_ANON_KEY=your_local_anon_key_here

# Option 2: Cloud Supabase (for production)
# VITE_SUPABASE_URL=https://your-project.supabase.co
# VITE_SUPABASE_ANON_KEY=your_cloud_anon_key_here

# AI Configuration
# Default provider: ollama (local), openai, anthropic, or auto
VITE_AI_PROVIDER=ollama

# Local AI (Ollama) - for on-premises deployment
VITE_OLLAMA_URL=http://localhost:11434
VITE_OLLAMA_MODEL=llama3.1:8b
VITE_OLLAMA_EMBEDDING_MODEL=nomic-embed-text

# Cloud AI Providers (optional)
# VITE_OPENAI_API_KEY=sk-your_openai_api_key_here
# VITE_OPENAI_MODEL=gpt-4
# VITE_ANTHROPIC_API_KEY=sk-ant-your_anthropic_key_here
# VITE_ANTHROPIC_MODEL=claude-3-sonnet-20240229

# Feature Flags
VITE_ENABLE_VOICE=true
VITE_ENABLE_REALTIME=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_DEBUG=false
VITE_ENABLE_AI_TESTING=true

# Security
VITE_ENCRYPTION_KEY=your_32_character_encryption_key_here

# Performance Settings
VITE_CACHE_DURATION=300000
VITE_MAX_UPLOAD_SIZE=10485760
VITE_API_TIMEOUT=30000

# External Integrations (optional)
# VITE_N8N_WEBHOOK_URL=https://your-n8n-instance.com/webhook
# VITE_SMTP_HOST=smtp.gmail.com
# VITE_SMTP_PORT=587
# VITE_SMTP_USER=<EMAIL>
# VITE_SMTP_PASS=your-app-password

# Development Settings
VITE_LOG_LEVEL=debug
VITE_MOCK_DATA=true
VITE_DEV_TOOLS=true

# Production Settings (when VITE_APP_ENV=production)
# VITE_LOG_LEVEL=error
# VITE_MOCK_DATA=false
# VITE_DEV_TOOLS=false
# VITE_ENABLE_DEBUG=false
