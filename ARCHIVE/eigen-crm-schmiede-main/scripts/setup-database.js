
#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - VITE_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  console.error('\nPlease check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupDatabase() {
  try {
    console.log('🚀 Setting up ConstructionOS database...\n');

    // Read and execute schema
    console.log('📋 Creating database schema...');
    const schemaSQL = fs.readFileSync(path.join(__dirname, '../database/schema.sql'), 'utf8');
    
    // Split the schema into individual statements
    const statements = schemaSQL
      .split(';')
      .map(s => s.trim())
      .filter(s => s.length > 0);

    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await supabase.rpc('exec_sql', { sql: statement });
        } catch (error) {
          // Ignore certain expected errors
          if (!error.message.includes('already exists') && 
              !error.message.includes('extension "uuid-ossp" already exists')) {
            console.warn('⚠️  Warning:', error.message.split('\n')[0]);
          }
        }
      }
    }

    console.log('✅ Schema created successfully');

    // Read and execute seed data
    console.log('🌱 Seeding demo data...');
    const seedSQL = fs.readFileSync(path.join(__dirname, '../database/seed.sql'), 'utf8');
    
    const seedStatements = seedSQL
      .split(';')
      .map(s => s.trim())
      .filter(s => s.length > 0);

    for (const statement of seedStatements) {
      if (statement.trim()) {
        try {
          await supabase.rpc('exec_sql', { sql: statement });
        } catch (error) {
          if (!error.message.includes('duplicate key value')) {
            console.warn('⚠️  Warning:', error.message.split('\n')[0]);
          }
        }
      }
    }

    console.log('✅ Demo data seeded successfully');

    // Test connection
    console.log('🔍 Testing database connection...');
    const { data, error } = await supabase.from('projects').select('count');
    
    if (error) {
      throw error;
    }

    console.log('✅ Database connection successful');
    console.log('\n🎉 Database setup completed successfully!');
    console.log('\nYou can now start the development server with: npm run dev');

  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    process.exit(1);
  }
}

// Create exec_sql function if it doesn't exist
async function createExecFunction() {
  try {
    const functionSQL = `
      CREATE OR REPLACE FUNCTION exec_sql(sql text)
      RETURNS void AS $$
      BEGIN
        EXECUTE sql;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
    `;
    
    await supabase.rpc('exec', { sql: functionSQL });
  } catch (error) {
    // Function might already exist, that's okay
  }
}

// Run setup
createExecFunction().then(setupDatabase);
