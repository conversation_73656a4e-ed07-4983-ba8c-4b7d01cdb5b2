
# Deployment Guide

This guide covers deploying Eigen CRM Schmiede to various production environments.

## Table of Contents
- [Deployment Options](#deployment-options)
- [Environment Preparation](#environment-preparation)
- [Cloud Deployment](#cloud-deployment)
- [Self-Hosted Deployment](#self-hosted-deployment)
- [Docker Deployment](#docker-deployment)
- [Monitoring & Maintenance](#monitoring--maintenance)

## Deployment Options

### 1. Lovable Platform (Recommended for Quick Deploy)
- ✅ Zero configuration
- ✅ Automatic SSL
- ✅ Global CDN
- ✅ Instant deployments

### 2. Self-Hosted with Docker
- ✅ Full control
- ✅ Custom domains
- ✅ Local AI models
- ✅ Enhanced security

### 3. Cloud Platforms
- ✅ Scalable infrastructure
- ✅ Managed services
- ✅ Auto-scaling
- ✅ Global availability

## Environment Preparation

### Production Environment Variables
Create a production `.env` file:

```bash
# Application
VITE_APP_ENV=production
VITE_APP_NAME="Eigen CRM Schmiede"
VITE_APP_VERSION=1.0.0
VITE_ENABLE_DEBUG=false

# Database - Cloud Supabase
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_production_anon_key

# AI Configuration - Choose based on deployment
VITE_AI_PROVIDER=openai  # or ollama for self-hosted
VITE_OPENAI_API_KEY=sk-your_production_key

# Security
VITE_ENCRYPTION_KEY=your_secure_32_character_production_key

# Features
VITE_ENABLE_VOICE=true
VITE_ENABLE_REALTIME=true
VITE_ENABLE_ANALYTICS=true

# Performance
VITE_CACHE_DURATION=3600000
VITE_MAX_UPLOAD_SIZE=10485760
```

### Build Optimization
```bash
# Install dependencies
npm ci --production

# Build optimized version
npm run build

# Verify build
npm run preview
```

## Cloud Deployment

### Lovable Platform

#### Quick Deploy
1. Open your project in Lovable
2. Click "Publish" in the top-right
3. Choose your domain settings
4. Click "Deploy"

#### Custom Domain
1. Go to Project > Settings > Domains
2. Click "Connect Domain"
3. Follow DNS configuration instructions
4. Verify domain ownership

### Vercel Deployment

#### Setup
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

#### vercel.json Configuration
```json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "dist"
      }
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ],
  "env": {
    "VITE_SUPABASE_URL": "@supabase_url",
    "VITE_SUPABASE_ANON_KEY": "@supabase_anon_key"
  }
}
```

### Netlify Deployment

#### netlify.toml Configuration
```toml
[build]
  publish = "dist"
  command = "npm run build"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[build.environment]
  VITE_SUPABASE_URL = "https://your-project.supabase.co"
  VITE_SUPABASE_ANON_KEY = "your_anon_key"
```

## Self-Hosted Deployment

### Server Requirements
- **CPU**: 2+ cores
- **RAM**: 4GB+ (8GB+ with local AI)
- **Storage**: 50GB+ SSD
- **OS**: Ubuntu 20.04+ or similar
- **Network**: Stable internet connection

### Docker Deployment

#### Dockerfile
```dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  crm-app:
    build: .
    ports:
      - "80:80"
      - "443:443"
    environment:
      - VITE_SUPABASE_URL=${VITE_SUPABASE_URL}
      - VITE_SUPABASE_ANON_KEY=${VITE_SUPABASE_ANON_KEY}
    volumes:
      - ./ssl:/etc/ssl/certs
    restart: unless-stopped

  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    restart: unless-stopped

  supabase:
    image: supabase/postgres:15.1.0.147
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  ollama_data:
  postgres_data:
```

#### Deploy with Docker
```bash
# Clone repository
git clone <repository-url>
cd eigen-crm-schmiede

# Copy environment file
cp .env.production .env

# Start services
docker-compose up -d

# Check status
docker-compose ps
```

### Manual Server Setup

#### Install Dependencies
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Nginx
sudo apt install nginx

# Install Docker (for Supabase/Ollama)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
```

#### Application Setup
```bash
# Clone and build
git clone <repository-url>
cd eigen-crm-schmiede
npm install
npm run build

# Copy to web directory
sudo cp -r dist/* /var/www/html/

# Configure Nginx
sudo cp nginx.conf /etc/nginx/sites-available/crm
sudo ln -s /etc/nginx/sites-available/crm /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## SSL Certificate Setup

### Let's Encrypt (Free)
```bash
# Install certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Custom Certificate
```bash
# Place certificates
sudo cp your-cert.crt /etc/ssl/certs/
sudo cp your-key.key /etc/ssl/private/

# Update Nginx config
sudo nano /etc/nginx/sites-available/crm
```

## Monitoring & Maintenance

### Health Monitoring
```bash
# Application health check
curl -f http://your-domain.com/health || alert

# Database monitoring
curl -f http://your-domain.com/api/health/db || alert

# AI service monitoring
curl -f http://localhost:11434/api/version || alert
```

### Log Management
```bash
# Application logs
docker logs crm-app

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# System logs
journalctl -u nginx -f
```

### Backup Strategy
```bash
# Database backup
docker exec supabase pg_dump -U postgres > backup_$(date +%Y%m%d).sql

# Application backup
tar -czf app_backup_$(date +%Y%m%d).tar.gz /var/www/html/

# Automated backup script
#!/bin/bash
# Add to crontab: 0 2 * * * /path/to/backup.sh
```

### Updates
```bash
# Pull latest changes
git pull origin main

# Rebuild and deploy
npm run build
sudo cp -r dist/* /var/www/html/

# Restart services if needed
docker-compose restart
```

### Performance Optimization
```bash
# Enable Nginx caching
# Add to nginx.conf:
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# Enable gzip compression
gzip on;
gzip_types text/plain text/css application/json application/javascript;
```

## Troubleshooting

### Common Issues
1. **Build Failures**: Check Node.js version and dependencies
2. **Environment Variables**: Verify all required vars are set
3. **Database Connection**: Check Supabase URL and keys
4. **AI Integration**: Verify Ollama is running and accessible
5. **SSL Issues**: Check certificate validity and Nginx config

### Debug Commands
```bash
# Check application logs
docker logs crm-app --tail 100

# Test database connection
psql -h localhost -U postgres -d postgres

# Test AI service
curl http://localhost:11434/api/tags

# Check system resources
htop
df -h
free -h
```

For more troubleshooting help, see [TROUBLESHOOTING.md](./TROUBLESHOOTING.md).
