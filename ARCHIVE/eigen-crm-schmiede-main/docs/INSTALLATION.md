
# Installation Guide

This guide will walk you through setting up the Eigen CRM Schmiede application in different environments.

## Table of Contents
- [Development Setup](#development-setup)
- [Local AI Setup (Ollama)](#local-ai-setup-ollama)
- [Database Setup](#database-setup)
- [Environment Configuration](#environment-configuration)
- [Production Setup](#production-setup)

## Development Setup

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Git
- Docker (for local database)

### Step 1: <PERSON>lone and Install
```bash
git clone <repository-url>
cd eigen-crm-schmiede
npm install
```

### Step 2: Environment Configuration
```bash
cp .env.example .env.local
```

Edit `.env.local` with your settings:
```bash
# Basic Configuration
VITE_APP_ENV=development
VITE_AI_PROVIDER=ollama
VITE_ENABLE_VOICE=true
VITE_ENABLE_REALTIME=true

# Database (choose one)
# Option 1: Local Supabase
VITE_SUPABASE_URL=http://localhost:54321
VITE_SUPABASE_ANON_KEY=your_local_anon_key

# Option 2: Cloud Supabase
# VITE_SUPABASE_URL=https://your-project.supabase.co
# VITE_SUPABASE_ANON_KEY=your_cloud_anon_key

# AI Configuration
VITE_OLLAMA_URL=http://localhost:11434
```

### Step 3: Start Development
```bash
npm run dev
```

## Local AI Setup (Ollama)

### Install Ollama
```bash
# macOS
brew install ollama

# Linux
curl -fsSL https://ollama.ai/install.sh | sh

# Windows
# Download from https://ollama.ai/download
```

### Download AI Models
```bash
# Recommended models for CRM
ollama pull llama3.1:8b      # General intelligence
ollama pull nomic-embed-text # Text embeddings
ollama pull codellama:7b     # Code analysis (optional)
```

### Start Ollama Service
```bash
ollama serve
```

### Verify Installation
```bash
curl http://localhost:11434/api/version
```

## Database Setup

### Option 1: Local Supabase (Recommended for Development)

#### Prerequisites
- Docker and Docker Compose
- Supabase CLI

#### Install Supabase CLI
```bash
npm install -g @supabase/cli
```

#### Initialize and Start
```bash
# Initialize Supabase in project
supabase init

# Start local Supabase
supabase start

# Apply database schema
supabase db reset

# Seed with sample data (optional)
npm run db:seed
```

#### Get Connection Details
```bash
supabase status
```

Copy the API URL and anon key to your `.env.local`.

### Option 2: Cloud Supabase

#### Setup
1. Visit [supabase.com](https://supabase.com)
2. Create a new project
3. Wait for setup to complete
4. Go to Settings > API
5. Copy your URL and anon key

#### Database Schema
Run the schema migrations:
```sql
-- Copy contents from database/schema.sql
-- Execute in Supabase SQL Editor
```

## Environment Configuration

### Complete .env.local Example
```bash
# Application
VITE_APP_ENV=development
VITE_APP_NAME="Eigen CRM Schmiede"
VITE_APP_VERSION=1.0.0

# Database
VITE_SUPABASE_URL=http://localhost:54321
VITE_SUPABASE_ANON_KEY=your_anon_key_here

# AI Configuration
VITE_AI_PROVIDER=ollama
VITE_OLLAMA_URL=http://localhost:11434
VITE_OLLAMA_MODEL=llama3.1:8b

# Optional: Cloud AI Providers
# VITE_OPENAI_API_KEY=sk-...
# VITE_ANTHROPIC_API_KEY=sk-ant-...

# Features
VITE_ENABLE_VOICE=true
VITE_ENABLE_REALTIME=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_DEBUG=true

# Security
VITE_ENCRYPTION_KEY=your_32_character_encryption_key

# Performance
VITE_CACHE_DURATION=300000
VITE_MAX_UPLOAD_SIZE=10485760
```

### Environment Validation
The application will validate your environment on startup. Check the browser console for any configuration warnings.

## Production Setup

### Build Application
```bash
npm run build
```

### Production Environment Variables
```bash
# Application
VITE_APP_ENV=production
VITE_ENABLE_DEBUG=false

# Database - Use Cloud Supabase
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_production_anon_key

# AI - Consider cloud providers for scale
VITE_AI_PROVIDER=openai
VITE_OPENAI_API_KEY=your_production_openai_key

# Security
VITE_ENCRYPTION_KEY=your_secure_32_character_key
```

### Docker Deployment
```bash
# Build image
docker build -t eigen-crm .

# Run container
docker run -p 8080:8080 \
  -e VITE_SUPABASE_URL=your_url \
  -e VITE_SUPABASE_ANON_KEY=your_key \
  eigen-crm
```

## Verification

### Health Check
Visit `http://localhost:8080` and verify:
- ✅ Application loads without errors
- ✅ Dashboard displays correctly
- ✅ AI System Health shows "Online"
- ✅ Database connection is active
- ✅ Voice control works (if enabled)

### AI Integration Test
1. Go to Dashboard
2. Find "AI Integration Validation" panel
3. Click "Run Integration Tests"
4. Verify all tests pass

### Common Issues
See [Troubleshooting Guide](./TROUBLESHOOTING.md) for solutions to common setup problems.

## Next Steps
- [Development Guide](./DEVELOPMENT.md) - Learn about the codebase
- [API Documentation](./API.md) - Understand the backend
- [Deployment Guide](./DEPLOYMENT.md) - Deploy to production
