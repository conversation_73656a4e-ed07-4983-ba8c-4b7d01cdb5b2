
import React, { useState } from 'react';
import { PeopleHeader } from './people/PeopleHeader';
import { PeopleStats } from './people/PeopleStats';
import { PeopleFilters } from './people/PeopleFilters';
import { PersonCardDisplay } from './people/PersonCardDisplay';
import { PeopleEmptyState } from './people/PeopleEmptyState';
import { PersonForm } from './people/PersonForm';
import { Person, PersonFormData } from '@/types/person';
import { useContacts, useCreateContact, useUpdateContact } from '@/hooks/useRealData';

export const People = () => {
  const { data: contactsData, isLoading } = useContacts();
  const createContactMutation = useCreateContact();
  const updateContactMutation = useUpdateContact();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [showForm, setShowForm] = useState(false);
  const [editingPerson, setEditingPerson] = useState<Person | null>(null);

  // Ensure contactsData is an array
  const contacts = Array.isArray(contactsData) ? contactsData : [];

  // Convert database contacts to Person format
  const people: Person[] = contacts.map(contact => ({
    id: contact.id,
    firstName: contact.first_name,
    lastName: contact.last_name,
    email: contact.email || '',
    phone: contact.phone || '',
    position: contact.position || '',
    company: '', // Will be populated from company relationship
    companyId: contact.company_id || '',
    avatar: `${contact.first_name?.[0] || ''}${contact.last_name?.[0] || ''}`,
    type: (contact.type as 'client' | 'contractor' | 'employee' | 'vendor') || 'client',
    status: (contact.status as 'active' | 'inactive') || 'active',
    tags: [],
    notes: contact.notes || '',
    address: {},
    socialMedia: {},
    createdAt: contact.created_at,
    updatedAt: contact.updated_at
  }));

  const filteredPeople = people.filter(person => {
    const matchesSearch = 
      person.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      person.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      person.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (person.company && person.company.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesType = typeFilter === 'all' || person.type === typeFilter;
    const matchesStatus = statusFilter === 'all' || person.status === statusFilter;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  const handleAddPerson = async (data: PersonFormData) => {
    try {
      await createContactMutation.mutateAsync({
        first_name: data.firstName,
        last_name: data.lastName,
        email: data.email,
        phone: data.phone,
        position: data.position,
        type: data.type,
        status: data.status,
        notes: data.notes
      });
      setShowForm(false);
    } catch (error) {
      console.error('Error creating contact:', error);
    }
  };

  const handleEditPerson = async (data: PersonFormData) => {
    if (editingPerson) {
      try {
        await updateContactMutation.mutateAsync({
          id: editingPerson.id,
          updates: {
            first_name: data.firstName,
            last_name: data.lastName,
            email: data.email,
            phone: data.phone,
            position: data.position,
            type: data.type,
            status: data.status,
            notes: data.notes
          }
        });
        setEditingPerson(null);
      } catch (error) {
        console.error('Error updating contact:', error);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="text-center">Loading people...</div>
      </div>
    );
  }

  if (showForm) {
    return (
      <PersonForm 
        onSubmit={handleAddPerson}
        onCancel={() => setShowForm(false)}
      />
    );
  }

  if (editingPerson) {
    const formData = {
      firstName: editingPerson.firstName,
      lastName: editingPerson.lastName,
      email: editingPerson.email,
      phone: editingPerson.phone,
      position: editingPerson.position,
      company: editingPerson.company,
      type: editingPerson.type,
      status: editingPerson.status,
      street: editingPerson.address?.street || '',
      city: editingPerson.address?.city || '',
      state: editingPerson.address?.state || '',
      zipCode: editingPerson.address?.zipCode || '',
      country: editingPerson.address?.country || '',
      linkedin: editingPerson.socialMedia?.linkedin || '',
      twitter: editingPerson.socialMedia?.twitter || '',
      website: editingPerson.socialMedia?.website || '',
      notes: editingPerson.notes || ''
    };
    
    return (
      <PersonForm 
        onSubmit={handleEditPerson}
        onCancel={() => setEditingPerson(null)}
        initialData={formData}
        isEdit={true}
      />
    );
  }

  return (
    <div className="p-6 space-y-6">
      <PeopleHeader onAddPerson={() => setShowForm(true)} />
      
      <PeopleStats people={people} />

      <PeopleFilters
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        typeFilter={typeFilter}
        onTypeFilterChange={setTypeFilter}
        statusFilter={statusFilter}
        onStatusFilterChange={setStatusFilter}
      />

      <div className="grid gap-4">
        {filteredPeople.map((person) => (
          <PersonCardDisplay
            key={person.id}
            person={person}
            onEdit={setEditingPerson}
          />
        ))}
      </div>

      {filteredPeople.length === 0 && (
        <PeopleEmptyState onAddPerson={() => setShowForm(true)} />
      )}
    </div>
  );
};
