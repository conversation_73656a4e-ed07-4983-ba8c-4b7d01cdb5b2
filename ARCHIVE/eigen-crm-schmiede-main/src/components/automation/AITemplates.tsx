
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Phone, Mail, TrendingUp, Globe, CheckCircle } from 'lucide-react';

interface AITemplate {
  name: string;
  description: string;
  type: string;
  icon: any;
  features: string[];
}

interface AITemplatesProps {
  onCreateWorkflow: (type: string) => void;
}

export const AITemplates: React.FC<AITemplatesProps> = ({ onCreateWorkflow }) => {
  const aiAutomationTemplates: AITemplate[] = [
    {
      name: 'AI Voice Prospect Qualification',
      description: 'Automated voice calls using AI agents for lead qualification',
      type: 'voice-call-automation',
      icon: Phone,
      features: ['Natural conversation flow', 'Real-time sentiment analysis', 'Automatic CRM updates']
    },
    {
      name: 'Predictive Email Sequences',
      description: 'AI-powered email campaigns with dynamic personalization',
      type: 'ai-email-sequence',
      icon: Mail,
      features: ['Dynamic content generation', 'Send time optimization', 'A/B testing automation']
    },
    {
      name: 'Revenue Intelligence Automation',
      description: 'Autonomous deal scoring and opportunity identification',
      type: 'revenue-optimization',
      icon: TrendingUp,
      features: ['Predictive deal scoring', 'Risk identification', 'Revenue forecasting']
    },
    {
      name: 'Market Intelligence Monitor',
      description: 'Continuous competitor and market trend monitoring',
      type: 'market-intelligence',
      icon: Globe,
      features: ['Competitor price tracking', 'Market trend analysis', 'Opportunity alerts']
    }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>AI-Powered Automation Templates</CardTitle>
        <CardDescription>Next-generation autonomous revenue acceleration</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {aiAutomationTemplates.map((template) => {
            const Icon = template.icon;
            return (
              <div 
                key={template.name} 
                className="p-4 border rounded-lg hover:bg-muted/50 cursor-pointer transition-all"
                onClick={() => onCreateWorkflow(template.type)}
              >
                <div className="flex items-start gap-3">
                  <Icon className="w-8 h-8 text-primary mt-1" />
                  <div className="flex-1">
                    <h4 className="font-medium">{template.name}</h4>
                    <p className="text-sm text-muted-foreground mb-2">{template.description}</p>
                    <div className="space-y-1">
                      {template.features.map((feature, index) => (
                        <div key={index} className="flex items-center gap-1 text-xs text-muted-foreground">
                          <CheckCircle className="w-3 h-3 text-green-500" />
                          {feature}
                        </div>
                      ))}
                    </div>
                    <Badge variant="outline" className="mt-2 text-xs">
                      AI-Powered
                    </Badge>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};
