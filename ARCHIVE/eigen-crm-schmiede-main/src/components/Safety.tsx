
import React, { useState } from 'react';
import { SafetyHeader } from './safety/SafetyHeader';
import { SafetyStatsCards } from './safety/SafetyStatsCards';
import { SafetyTabs } from './safety/SafetyTabs';

export const Safety = () => {
  const [activeTab, setActiveTab] = useState('incidents');

  // Mock data for safety overview
  const totalIncidents = 3;
  const openIncidents = 1;
  const daysWithoutIncident = 45;
  const complianceScore = 94;
  const safetyTrainings = 12;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-red-50 to-orange-50 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        <SafetyHeader />
        
        <SafetyStatsCards
          totalIncidents={totalIncidents}
          openIncidents={openIncidents}
          daysWithoutIncident={daysWithoutIncident}
          complianceScore={complianceScore}
          safetyTrainings={safetyTrainings}
        />

        <SafetyTabs activeTab={activeTab} onTabChange={setActiveTab} />
      </div>
    </div>
  );
};
