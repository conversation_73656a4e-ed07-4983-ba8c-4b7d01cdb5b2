
import React from 'react';
import { CommunicationHero } from './CommunicationHero';
import { CommunicationStatusBar } from './CommunicationStatusBar';
import { CommunicationActionButtons } from './CommunicationActionButtons';
import { CommunicationQuickStats } from './CommunicationQuickStats';

export const CommunicationHeader = () => {
  return (
    <div className="bg-white/90 backdrop-blur-md rounded-2xl border border-white/30 shadow-2xl p-8 mb-6 animate-fade-in">
      <div className="flex items-center justify-between">
        <CommunicationHero />
        <CommunicationActionButtons />
      </div>

      <CommunicationStatusBar />
      <CommunicationQuickStats />
    </div>
  );
};
