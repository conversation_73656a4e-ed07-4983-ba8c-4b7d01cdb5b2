
import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Pin, Eye, MessageCircle, Heart, AlertTriangle, Info, CheckCircle } from 'lucide-react';

interface AnnouncementBoardProps {
  announcements: Array<{
    id: string;
    title: string;
    content: string;
    author: string;
    timestamp: string;
    priority: 'high' | 'medium' | 'low';
    isPinned: boolean;
    views: number;
    reactions: number;
    comments: number;
  }>;
  onCreateAnnouncement: (announcement: { title: string; content: string; priority: string }) => void;
}

export const AnnouncementBoard = ({ announcements, onCreateAnnouncement }: AnnouncementBoardProps) => {
  const [isCreating, setIsCreating] = useState(false);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [priority, setPriority] = useState('medium');

  const handleCreate = () => {
    if (title.trim() && content.trim()) {
      onCreateAnnouncement({ title, content, priority });
      setTitle('');
      setContent('');
      setPriority('medium');
      setIsCreating(false);
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'medium': return <Info className="h-4 w-4 text-yellow-500" />;
      case 'low': return <CheckCircle className="h-4 w-4 text-green-500" />;
      default: return <Info className="h-4 w-4 text-blue-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-red-200 bg-red-50';
      case 'medium': return 'border-yellow-200 bg-yellow-50';
      case 'low': return 'border-green-200 bg-green-50';
      default: return 'border-blue-200 bg-blue-50';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-900">Announcements</h2>
          <p className="text-slate-600 mt-1">Important updates and company news</p>
        </div>
        <Button
          onClick={() => setIsCreating(true)}
          className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
        >
          <Plus className="h-4 w-4 mr-2" />
          New Announcement
        </Button>
      </div>

      {isCreating && (
        <Card className="bg-white/90 backdrop-blur-md border-white/30 shadow-xl">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-slate-900">Create Announcement</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Input
                placeholder="Announcement title..."
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="bg-white border-slate-200"
              />
            </div>
            <div>
              <Textarea
                placeholder="Write your announcement content..."
                value={content}
                onChange={(e) => setContent(e.target.value)}
                rows={4}
                className="bg-white border-slate-200"
              />
            </div>
            <div className="flex items-center justify-between">
              <Select value={priority} onValueChange={setPriority}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                </SelectContent>
              </Select>
              <div className="flex space-x-2">
                <Button variant="outline" onClick={() => setIsCreating(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreate} className="bg-purple-600 hover:bg-purple-700">
                  Publish
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="space-y-4">
        {announcements.map((announcement) => (
          <Card 
            key={announcement.id} 
            className={`bg-white/90 backdrop-blur-md border-white/30 shadow-xl hover:shadow-2xl transition-all duration-300 ${
              announcement.isPinned ? 'ring-2 ring-purple-200' : ''
            }`}
          >
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  {announcement.isPinned && (
                    <div className="bg-purple-100 p-2 rounded-lg">
                      <Pin className="h-4 w-4 text-purple-600" />
                    </div>
                  )}
                  <div className={`p-2 rounded-lg ${getPriorityColor(announcement.priority)}`}>
                    {getPriorityIcon(announcement.priority)}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-slate-900">{announcement.title}</h3>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-sm text-slate-600">by {announcement.author}</span>
                      <span className="text-xs text-slate-500">•</span>
                      <span className="text-xs text-slate-500">{announcement.timestamp}</span>
                      <Badge variant="outline" className="text-xs">
                        {announcement.priority}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
              
              <p className="text-slate-700 mb-4 leading-relaxed">{announcement.content}</p>
              
              <div className="flex items-center justify-between pt-4 border-t border-slate-200">
                <div className="flex items-center space-x-6 text-sm text-slate-600">
                  <div className="flex items-center space-x-1">
                    <Eye className="h-4 w-4" />
                    <span>{announcement.views}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Heart className="h-4 w-4" />
                    <span>{announcement.reactions}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <MessageCircle className="h-4 w-4" />
                    <span>{announcement.comments}</span>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button size="sm" variant="ghost" className="hover:bg-red-100">
                    <Heart className="h-4 w-4 mr-1" />
                    Like
                  </Button>
                  <Button size="sm" variant="ghost" className="hover:bg-blue-100">
                    <MessageCircle className="h-4 w-4 mr-1" />
                    Comment
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
