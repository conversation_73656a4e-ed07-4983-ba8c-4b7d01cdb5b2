
import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Video, Users, Settings, Zap } from 'lucide-react';

export const CommunicationActionButtons = () => {
  return (
    <div className="flex items-center space-x-3">
      <Button 
        variant="outline" 
        className="border-slate-200 hover:bg-slate-50 hover:scale-105 hover:shadow-lg transition-all duration-300 group"
      >
        <Settings className="w-4 h-4 mr-2 group-hover:rotate-90 transition-transform duration-300" />
        Settings
      </Button>
      
      <Button 
        variant="outline" 
        className="border-purple-200 text-purple-600 hover:bg-purple-50 hover:scale-105 hover:shadow-lg transition-all duration-300 group"
      >
        <Video className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-300" />
        Start Call
        <Badge className="ml-2 bg-purple-100 text-purple-700 text-xs">HD</Badge>
      </Button>
      
      <Button 
        className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white shadow-xl hover:shadow-2xl hover:scale-105 transition-all duration-300 group"
      >
        <Users className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-300" />
        Create Channel
        <Zap className="w-3 h-3 ml-2 animate-pulse" />
      </Button>
    </div>
  );
};
