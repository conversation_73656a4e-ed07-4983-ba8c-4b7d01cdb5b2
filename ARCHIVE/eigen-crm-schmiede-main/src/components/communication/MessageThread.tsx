
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Send, Smile, Paperclip, MoreVertical, Heart, ThumbsUp } from 'lucide-react';

interface MessageThreadProps {
  channelName: string;
  messages: Array<{
    id: string;
    sender: string;
    content: string;
    timestamp: string;
    avatar: string;
    type: 'text' | 'file' | 'system';
    reactions?: string[];
  }>;
  onSendMessage: (message: string) => void;
}

export const MessageThread = ({ channelName, messages, onSendMessage }: MessageThreadProps) => {
  const [newMessage, setNewMessage] = useState('');

  const handleSend = () => {
    if (newMessage.trim()) {
      onSendMessage(newMessage);
      setNewMessage('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <Card className="bg-white/90 backdrop-blur-md border-white/30 shadow-xl h-[600px] flex flex-col">
      <CardHeader className="border-b border-slate-200/50 pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl font-semibold text-slate-900 flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            #{channelName}
          </CardTitle>
          <Button variant="ghost" size="sm" className="hover:bg-slate-100">
            <MoreVertical className="h-4 w-4" />
          </Button>
        </div>
        <p className="text-sm text-slate-600">Real-time team communication</p>
      </CardHeader>
      
      <CardContent className="flex-1 p-0 overflow-hidden flex flex-col">
        <div className="flex-1 overflow-y-auto p-6 space-y-4">
          {messages.map((message) => (
            <div key={message.id} className="group flex space-x-3 hover:bg-slate-50/50 p-2 rounded-lg transition-colors">
              <Avatar className="h-10 w-10 ring-2 ring-white shadow-md">
                <AvatarImage src={message.avatar} />
                <AvatarFallback className="bg-gradient-to-br from-purple-400 to-pink-400 text-white font-semibold">
                  {message.sender.slice(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <span className="font-semibold text-slate-900">{message.sender}</span>
                  <span className="text-xs text-slate-500">{message.timestamp}</span>
                </div>
                <div className={`${
                  message.type === 'system' 
                    ? 'italic text-slate-600' 
                    : 'text-slate-800'
                }`}>
                  {message.content}
                </div>
                {message.reactions && message.reactions.length > 0 && (
                  <div className="flex items-center space-x-2 mt-2">
                    <Button size="sm" variant="ghost" className="h-6 px-2 text-xs hover:bg-red-100 group">
                      <Heart className="h-3 w-3 mr-1 text-red-500" />
                      <span>2</span>
                    </Button>
                    <Button size="sm" variant="ghost" className="h-6 px-2 text-xs hover:bg-blue-100 group">
                      <ThumbsUp className="h-3 w-3 mr-1 text-blue-500" />
                      <span>5</span>
                    </Button>
                  </div>
                )}
              </div>
              <div className="opacity-0 group-hover:opacity-100 transition-opacity flex items-center space-x-1">
                <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                  <Heart className="h-3 w-3" />
                </Button>
                <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                  <ThumbsUp className="h-3 w-3" />
                </Button>
              </div>
            </div>
          ))}
        </div>
        
        <div className="border-t border-slate-200/50 p-4 bg-slate-50/50">
          <div className="flex space-x-3">
            <Input
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={`Message #${channelName}...`}
              className="flex-1 bg-white border-slate-200 focus:border-purple-300 focus:ring-purple-200"
            />
            <Button variant="ghost" size="sm" className="hover:bg-slate-200">
              <Paperclip className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" className="hover:bg-slate-200">
              <Smile className="h-4 w-4" />
            </Button>
            <Button 
              onClick={handleSend}
              disabled={!newMessage.trim()}
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
