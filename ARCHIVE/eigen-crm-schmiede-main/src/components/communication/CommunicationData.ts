
export const mockChannels = [
  { id: 'general', name: 'General', unread: 3, type: 'channel' as const },
  { id: 'project-alpha', name: 'Project Alpha', unread: 1, type: 'channel' as const },
  { id: 'safety-updates', name: 'Safety Updates', unread: 0, type: 'channel' as const },
  { id: 'equipment', name: 'Equipment', unread: 2, type: 'channel' as const }
];

export const mockDirectMessages = [
  { id: 'john-smith', name: '<PERSON>', status: 'online' as const, unread: 1, type: 'dm' as const },
  { id: 'sarah-davis', name: '<PERSON>', status: 'away' as const, unread: 0, type: 'dm' as const },
  { id: 'mike-johnson', name: '<PERSON>', status: 'offline' as const, unread: 3, type: 'dm' as const }
];

export const mockMessages = [
  {
    id: '1',
    sender: '<PERSON>',
    content: 'Morning team! Just wanted to update everyone on the foundation work progress. We\'re ahead of schedule and should complete by Friday.',
    timestamp: '09:15 AM',
    avatar: 'JS',
    type: 'text' as const,
    reactions: ['👍', '🎉']
  },
  {
    id: '2',
    sender: '<PERSON>',
    content: 'Thanks <PERSON>! How are we looking for the concrete pour tomorrow? Do we have all the materials ready?',
    timestamp: '09:18 AM',
    avatar: 'SD',
    type: 'text' as const
  },
  {
    id: '3',
    sender: 'You',
    content: 'Weather looks good for tomorrow. Equipment will be on site by 6 AM and materials are confirmed for delivery at 7 AM.',
    timestamp: '09:20 AM',
    avatar: 'ME',
    type: 'text' as const
  },
  {
    id: '4',
    sender: 'System',
    content: 'Mike Johnson joined the channel',
    timestamp: '09:22 AM',
    avatar: 'SYS',
    type: 'system' as const
  }
];

export const mockAnnouncements = [
  {
    id: '1',
    title: 'Safety Meeting - Tomorrow 2 PM',
    content: 'Mandatory safety briefing for all site personnel. We\'ll be covering new protocols and discussing recent incidents. Location: Main office trailer.',
    author: 'Safety Team',
    timestamp: '2 hours ago',
    priority: 'high' as const,
    isPinned: true,
    views: 45,
    reactions: 12,
    comments: 3
  },
  {
    id: '2',
    title: 'Equipment Maintenance Schedule',
    content: 'Crane A-001 will be out of service Thursday for routine maintenance. Alternative equipment has been arranged.',
    author: 'Fleet Manager',
    timestamp: '5 hours ago',
    priority: 'medium' as const,
    isPinned: false,
    views: 23,
    reactions: 8,
    comments: 1
  },
  {
    id: '3',
    title: 'New Project Assignment',
    content: 'The downtown office complex project has been assigned to our team. Kickoff meeting scheduled for Monday.',
    author: 'Project Manager',
    timestamp: '1 day ago',
    priority: 'medium' as const,
    isPinned: false,
    views: 67,
    reactions: 15,
    comments: 7
  }
];
