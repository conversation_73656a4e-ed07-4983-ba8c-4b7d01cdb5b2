
import React, { memo } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Truck, Car, MapPin, Fuel } from 'lucide-react';
import type { Vehicle } from '@/types/fleet';

interface VehicleCardProps {
  vehicle: Vehicle;
  onStatusChange: (vehicleId: string, status: string) => void;
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'available': return 'bg-green-100 text-green-800';
    case 'in_use': return 'bg-blue-100 text-blue-800';
    case 'maintenance': return 'bg-yellow-100 text-yellow-800';
    case 'out_of_service': return 'bg-red-100 text-red-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getVehicleIcon = (type: string) => {
  switch (type) {
    case 'truck': return <Truck className="h-5 w-5" />;
    case 'car': return <Car className="h-5 w-5" />;
    default: return <Truck className="h-5 w-5" />;
  }
};

export const VehicleCard = memo(({ vehicle, onStatusChange }: VehicleCardProps) => {
  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getVehicleIcon(vehicle.type)}
            <CardTitle className="text-lg">{vehicle.name}</CardTitle>
          </div>
          <Badge className={getStatusColor(vehicle.status)}>
            {vehicle.status.replace('_', ' ')}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-slate-600">Make/Model</p>
            <p className="font-medium">{vehicle.make} {vehicle.model}</p>
          </div>
          <div>
            <p className="text-slate-600">License Plate</p>
            <p className="font-medium">{vehicle.license_plate || 'N/A'}</p>
          </div>
          {vehicle.location && (
            <div>
              <p className="text-slate-600">Location</p>
              <p className="font-medium flex items-center">
                <MapPin className="h-3 w-3 mr-1" />
                {vehicle.location}
              </p>
            </div>
          )}
          {vehicle.fuel_level && (
            <div>
              <p className="text-slate-600">Fuel Level</p>
              <p className="font-medium flex items-center">
                <Fuel className="h-3 w-3 mr-1" />
                {vehicle.fuel_level}%
              </p>
            </div>
          )}
        </div>
        <div className="flex space-x-2">
          <Select
            value={vehicle.status}
            onValueChange={(value) => onStatusChange(vehicle.id, value)}
          >
            <SelectTrigger className="flex-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="available">Available</SelectItem>
              <SelectItem value="in_use">In Use</SelectItem>
              <SelectItem value="maintenance">Maintenance</SelectItem>
              <SelectItem value="out_of_service">Out of Service</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>
  );
});

VehicleCard.displayName = 'VehicleCard';
