
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Shield, 
  AlertTriangle, 
  Eye,
  Lock,
  Activity,
  RefreshCw,
  TrendingUp,
  Clock
} from 'lucide-react';
import { securityMonitor } from '@/services/SecurityMonitor';
import { performanceTracker } from '@/services/PerformanceTracker';

export const SecurityDashboard: React.FC = () => {
  const [securityEvents, setSecurityEvents] = useState<any[]>([]);
  const [securitySummary, setSecuritySummary] = useState<Record<string, number>>({});
  const [performanceSummary, setPerformanceSummary] = useState<any>({});
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  const loadSecurityData = () => {
    const events = securityMonitor.getSecurityEvents();
    const summary = securityMonitor.getSecuritySummary();
    const perfSummary = performanceTracker.getSummary();
    
    setSecurityEvents(events.slice(-20)); // Last 20 events
    setSecuritySummary(summary);
    setPerformanceSummary(perfSummary);
    setLastUpdate(new Date());
  };

  useEffect(() => {
    loadSecurityData();
    
    // Refresh every 30 seconds
    const interval = setInterval(loadSecurityData, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getEventSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'xss_attempt': return <AlertTriangle className="w-4 h-4 text-red-600" />;
      case 'rate_limit_exceeded': return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'suspicious_activity': return <Eye className="w-4 h-4 text-orange-600" />;
      case 'unauthorized_access': return <Lock className="w-4 h-4 text-red-600" />;
      default: return <Shield className="w-4 h-4 text-blue-600" />;
    }
  };

  const getTotalEvents = () => {
    return Object.values(securitySummary).reduce((sum, count) => sum + count, 0);
  };

  const getCriticalEvents = () => {
    return securityEvents.filter(event => event.severity === 'critical').length;
  };

  return (
    <div className="space-y-6">
      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5 text-green-600" />
              Security & Performance Monitor
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={loadSecurityData}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Security Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-green-700">Security Status</p>
                    <p className="text-2xl font-bold text-green-900">
                      {getCriticalEvents() === 0 ? 'Secure' : 'Alert'}
                    </p>
                  </div>
                  <Shield className="w-8 h-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-blue-700">Total Events</p>
                    <p className="text-2xl font-bold text-blue-900">{getTotalEvents()}</p>
                  </div>
                  <Activity className="w-8 h-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-yellow-700">Critical Events</p>
                    <p className="text-2xl font-bold text-yellow-900">{getCriticalEvents()}</p>
                  </div>
                  <AlertTriangle className="w-8 h-8 text-yellow-600" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-purple-50 to-violet-50 border-purple-200">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-purple-700">Performance</p>
                    <p className="text-2xl font-bold text-purple-900">
                      {Object.keys(performanceSummary).length}
                    </p>
                    <p className="text-xs text-purple-600">tracked ops</p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Critical Alerts */}
          {getCriticalEvents() > 0 && (
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                {getCriticalEvents()} critical security event(s) detected. Immediate attention required.
              </AlertDescription>
            </Alert>
          )}

          {/* Recent Security Events */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Security Events</CardTitle>
            </CardHeader>
            <CardContent>
              {securityEvents.length === 0 ? (
                <div className="text-center py-8 text-slate-500">
                  <Shield className="w-12 h-12 mx-auto mb-4 text-green-500" />
                  <p>No security events detected</p>
                  <p className="text-sm">System is operating normally</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {securityEvents.map((event, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        {getEventIcon(event.type)}
                        <div>
                          <div className="font-medium capitalize">
                            {event.type.replace('_', ' ')}
                          </div>
                          <div className="text-sm text-slate-600">
                            Source: {event.source} • {event.timestamp.toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                      <Badge variant="outline" className={getEventSeverityColor(event.severity)}>
                        {event.severity}
                      </Badge>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Performance Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Performance Metrics</CardTitle>
            </CardHeader>
            <CardContent>
              {Object.keys(performanceSummary).length === 0 ? (
                <div className="text-center py-4 text-slate-500">
                  No performance data available
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Object.entries(performanceSummary).map(([operation, stats]: [string, any]) => (
                    <div key={operation} className="p-4 bg-slate-50 rounded-lg">
                      <div className="font-medium">{operation}</div>
                      <div className="text-sm text-slate-600 space-y-1">
                        <div>Count: {stats.count}</div>
                        <div>Avg: {stats.averageTime.toFixed(2)}ms</div>
                        <div>Max: {stats.maxTime.toFixed(2)}ms</div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          <div className="text-xs text-slate-600 text-center">
            Last updated: {lastUpdate.toLocaleTimeString()}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
