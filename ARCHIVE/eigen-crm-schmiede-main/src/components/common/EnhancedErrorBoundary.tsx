
import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle, RefreshCw, Home, MessageCircle, Bug } from 'lucide-react';
import { errorHandler } from '@/services/ErrorHandler';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  allowRetry?: boolean;
  component?: string;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId?: string;
  retryCount: number;
}

export class EnhancedErrorBoundary extends Component<Props, State> {
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = { 
      hasError: false, 
      retryCount: 0 
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return { hasError: true, error, errorId };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Enhanced ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({ errorInfo });
    
    // Report to error handler
    errorHandler.handleError(error, {
      component: this.props.component || 'ErrorBoundary',
      action: 'componentError',
      severity: 'high',
      category: 'ui'
    });
    
    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  private handleRetry = () => {
    if (this.state.retryCount < this.maxRetries) {
      this.setState({ 
        hasError: false, 
        error: undefined, 
        errorInfo: undefined, 
        errorId: undefined,
        retryCount: this.state.retryCount + 1
      });
    }
  };

  private handleGoHome = () => {
    window.location.href = '/dashboard';
  };

  private handleReportIssue = () => {
    const reportData = {
      errorId: this.state.errorId,
      error: this.state.error?.message,
      stack: this.state.error?.stack,
      component: this.props.component,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent
    };
    
    // In a real app, this would open a support ticket or email client
    console.log('Issue report:', reportData);
    
    const mailtoLink = `mailto:<EMAIL>?subject=Error Report - ${this.state.errorId}&body=${encodeURIComponent(JSON.stringify(reportData, null, 2))}`;
    window.open(mailtoLink);
  };

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const canRetry = this.props.allowRetry !== false && this.state.retryCount < this.maxRetries;

      return (
        <div className="flex items-center justify-center min-h-[400px] p-8">
          <Card className="max-w-lg mx-auto border-red-200 bg-red-50/30">
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-red-100/50 rounded-full flex items-center justify-center mb-4">
                <AlertTriangle className="w-6 h-6 text-red-600" />
              </div>
              <CardTitle className="text-red-900 font-medium">
                Oops! Something went wrong
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertDescription className="text-red-700">
                  We've encountered an unexpected error. Our team has been automatically notified.
                </AlertDescription>
              </Alert>
              
              {this.state.errorId && (
                <div className="text-xs text-red-600 font-mono bg-red-100/50 p-3 rounded border">
                  <strong>Error ID:</strong> {this.state.errorId}
                </div>
              )}

              {this.state.retryCount > 0 && (
                <div className="text-sm text-red-600 bg-red-100/50 p-2 rounded">
                  Retry attempts: {this.state.retryCount}/{this.maxRetries}
                </div>
              )}

              {this.props.showDetails && process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="text-xs text-left bg-red-100/50 p-3 rounded border">
                  <summary className="cursor-pointer font-medium text-red-800 mb-2">
                    Technical Details
                  </summary>
                  <div className="space-y-2">
                    <div>
                      <strong>Error:</strong>
                      <pre className="mt-1 text-red-700 overflow-auto max-h-20">
                        {this.state.error.toString()}
                      </pre>
                    </div>
                    {this.state.errorInfo && (
                      <div>
                        <strong>Component Stack:</strong>
                        <pre className="mt-1 text-red-700 overflow-auto max-h-20">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </details>
              )}
              
              <div className="flex flex-wrap gap-2 justify-center">
                {canRetry && (
                  <Button 
                    onClick={this.handleRetry}
                    size="sm"
                    className="bg-red-600 hover:bg-red-700 text-white"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Try Again ({this.maxRetries - this.state.retryCount} left)
                  </Button>
                )}
                
                <Button 
                  onClick={this.handleGoHome}
                  variant="outline"
                  size="sm"
                  className="border-red-300 text-red-700 hover:bg-red-100/50"
                >
                  <Home className="w-4 h-4 mr-2" />
                  Go to Dashboard
                </Button>
                
                <Button 
                  onClick={this.handleReportIssue}
                  variant="outline"
                  size="sm"
                  className="border-red-300 text-red-700 hover:bg-red-100/50"
                >
                  <Bug className="w-4 h-4 mr-2" />
                  Report Issue
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}
