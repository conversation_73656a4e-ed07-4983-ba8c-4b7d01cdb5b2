
import React from 'react';
import { Card, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { TrendingUp } from 'lucide-react';
import { GanttChart } from './GanttChart';
import { ResourceAllocation } from './ResourceAllocation';
import { BudgetAnalysis } from './BudgetAnalysis';
import { ProjectFilters } from './ProjectFilters';
import { ProjectsList } from './ProjectsList';
import { Project } from '@/types/project';

interface ProjectTabsProps {
  filteredProjects: Project[];
  filters: any;
  onFiltersChange: (filters: any) => void;
  onResetFilters: () => void;
  onEditProject: (project: Project) => void;
  onAddProject: () => void;
}

export const ProjectTabs: React.FC<ProjectTabsProps> = ({
  filteredProjects,
  filters,
  onFiltersChange,
  onResetFilters,
  onEditProject,
  onAddProject
}) => {
  return (
    <Tabs defaultValue="overview" className="space-y-6">
      <TabsList className="grid w-full grid-cols-5">
        <TabsTrigger value="overview">Overview</TabsTrigger>
        <TabsTrigger value="gantt">Gantt Chart</TabsTrigger>
        <TabsTrigger value="resources">Resources</TabsTrigger>
        <TabsTrigger value="budget">Budget</TabsTrigger>
        <TabsTrigger value="analytics">Analytics</TabsTrigger>
      </TabsList>

      <TabsContent value="overview" className="space-y-6">
        <ProjectFilters 
          filters={filters}
          onFiltersChange={onFiltersChange}
          onReset={onResetFilters}
        />
        <ProjectsList
          projects={filteredProjects}
          onEditProject={onEditProject}
          onAddProject={onAddProject}
        />
      </TabsContent>

      <TabsContent value="gantt">
        <GanttChart projects={filteredProjects} />
      </TabsContent>

      <TabsContent value="resources">
        <ResourceAllocation projects={filteredProjects} />
      </TabsContent>

      <TabsContent value="budget">
        <BudgetAnalysis projects={filteredProjects} />
      </TabsContent>

      <TabsContent value="analytics">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Project Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <TrendingUp className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Advanced Analytics Coming Soon</h3>
              <p className="text-muted-foreground">
                Detailed performance metrics, forecasting, and insights will be available here.
              </p>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
};
