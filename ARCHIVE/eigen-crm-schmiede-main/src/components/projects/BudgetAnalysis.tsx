
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { DollarSign, TrendingUp, TrendingDown, AlertTriangle } from 'lucide-react';
import { Project } from '@/types/project';

interface BudgetAnalysisProps {
  projects: Project[];
}

export const BudgetAnalysis: React.FC<BudgetAnalysisProps> = ({ projects }) => {
  const totalEstimated = projects.reduce((sum, p) => sum + p.estimatedBudget, 0);
  const totalActual = projects.reduce((sum, p) => sum + p.actualBudget, 0);
  const variance = totalActual - totalEstimated;
  const variancePercentage = (variance / totalEstimated) * 100;

  const getBudgetStatus = (project: Project) => {
    const spent = (project.actualBudget / project.estimatedBudget) * 100;
    if (spent > 100) return { status: 'over', color: 'red' };
    if (spent > 90) return { status: 'warning', color: 'yellow' };
    return { status: 'good', color: 'green' };
  };

  const getVarianceColor = (variance: number) => {
    if (variance > 0) return 'text-red-600';
    if (variance < 0) return 'text-green-600';
    return 'text-gray-600';
  };

  return (
    <div className="space-y-6">
      {/* Budget Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Budget</p>
                <p className="text-2xl font-bold">${(totalEstimated / 1000000).toFixed(1)}M</p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Actual Spent</p>
                <p className="text-2xl font-bold">${(totalActual / 1000000).toFixed(1)}M</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Variance</p>
                <p className={`text-2xl font-bold ${getVarianceColor(variance)}`}>
                  {variance >= 0 ? '+' : ''}${(Math.abs(variance) / 1000000).toFixed(1)}M
                </p>
              </div>
              {variance >= 0 ? (
                <TrendingUp className="h-8 w-8 text-red-600" />
              ) : (
                <TrendingDown className="h-8 w-8 text-green-600" />
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Variance %</p>
                <p className={`text-2xl font-bold ${getVarianceColor(variance)}`}>
                  {variancePercentage >= 0 ? '+' : ''}{variancePercentage.toFixed(1)}%
                </p>
              </div>
              {Math.abs(variancePercentage) > 10 ? (
                <AlertTriangle className="h-8 w-8 text-orange-600" />
              ) : (
                <DollarSign className="h-8 w-8 text-blue-600" />
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Project Budget Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="w-5 h-5" />
            Project Budget Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {projects.map((project) => {
              const budgetStatus = getBudgetStatus(project);
              const spentPercentage = (project.actualBudget / project.estimatedBudget) * 100;
              const projectVariance = project.actualBudget - project.estimatedBudget;
              
              return (
                <div key={project.id} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h3 className="font-medium">{project.name}</h3>
                      <p className="text-sm text-muted-foreground">{project.clientName}</p>
                    </div>
                    <div className="text-right">
                      <Badge 
                        className={
                          budgetStatus.status === 'over' ? 'bg-red-100 text-red-800' :
                          budgetStatus.status === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }
                      >
                        {budgetStatus.status === 'over' ? 'Over Budget' :
                         budgetStatus.status === 'warning' ? 'Near Limit' : 'On Track'}
                      </Badge>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-3">
                    <div>
                      <p className="text-xs text-muted-foreground">Estimated</p>
                      <p className="font-medium">${project.estimatedBudget.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Actual</p>
                      <p className="font-medium">${project.actualBudget.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Remaining</p>
                      <p className="font-medium">
                        ${Math.max(0, project.estimatedBudget - project.actualBudget).toLocaleString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Variance</p>
                      <p className={`font-medium ${getVarianceColor(projectVariance)}`}>
                        {projectVariance >= 0 ? '+' : ''}${projectVariance.toLocaleString()}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Budget Utilization</span>
                      <span>{spentPercentage.toFixed(1)}%</span>
                    </div>
                    <div className="relative">
                      <Progress value={Math.min(spentPercentage, 100)} className="h-2" />
                      <div 
                        className={`absolute top-0 left-0 h-2 rounded-full ${
                          spentPercentage > 100 ? 'bg-red-500' :
                          spentPercentage > 90 ? 'bg-yellow-500' : 'bg-green-500'
                        }`}
                        style={{ width: `${Math.min(spentPercentage, 100)}%` }}
                      />
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
