
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Calendar, Clock, Users, Truck } from 'lucide-react';
import { format, differenceInDays, isAfter, isBefore } from 'date-fns';

interface Project {
  id: string;
  name: string;
  status: 'planning' | 'active' | 'completed' | 'delayed';
  progress: number;
  startDate: string;
  endDate: string;
  manager: string;
  assignedCrew: number;
  assignedVehicles: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

interface ProjectTimelineProps {
  projects: Project[];
}

export const ProjectTimeline: React.FC<ProjectTimelineProps> = ({ projects }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'planning': return 'bg-blue-500';
      case 'completed': return 'bg-gray-500';
      case 'delayed': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getDaysRemaining = (endDate: string) => {
    const end = new Date(endDate);
    const today = new Date();
    return differenceInDays(end, today);
  };

  const isOverdue = (endDate: string, progress: number) => {
    return isAfter(new Date(), new Date(endDate)) && progress < 100;
  };

  const sortedProjects = [...projects].sort((a, b) => 
    new Date(a.startDate).getTime() - new Date(b.startDate).getTime()
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-2xl font-bold">Project Timeline</h3>
        <div className="flex gap-2">
          <Badge variant="outline" className="bg-blue-100 text-blue-800">Planning</Badge>
          <Badge variant="outline" className="bg-green-100 text-green-800">Active</Badge>
          <Badge variant="outline" className="bg-red-100 text-red-800">Delayed</Badge>
          <Badge variant="outline" className="bg-gray-100 text-gray-800">Completed</Badge>
        </div>
      </div>

      <div className="space-y-4">
        {sortedProjects.map((project, index) => {
          const daysRemaining = getDaysRemaining(project.endDate);
          const overdue = isOverdue(project.endDate, project.progress);
          const totalDays = differenceInDays(new Date(project.endDate), new Date(project.startDate));
          const daysPassed = differenceInDays(new Date(), new Date(project.startDate));
          const timeProgress = Math.max(0, Math.min(100, (daysPassed / totalDays) * 100));

          return (
            <Card key={project.id} className="relative overflow-hidden">
              <div className={`absolute left-0 top-0 bottom-0 w-1 ${getStatusColor(project.status)}`} />
              
              <CardContent className="p-6 pl-8">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h4 className="text-lg font-semibold">{project.name}</h4>
                    <p className="text-sm text-muted-foreground">{project.manager}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge 
                      className={`${getStatusColor(project.status)} text-white`}
                    >
                      {project.status}
                    </Badge>
                    {overdue && (
                      <Badge variant="destructive">Overdue</Badge>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="h-4 w-4 text-blue-600" />
                    <span>Start: {format(new Date(project.startDate), 'MMM d, yyyy')}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="h-4 w-4 text-red-600" />
                    <span>End: {format(new Date(project.endDate), 'MMM d, yyyy')}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Users className="h-4 w-4 text-green-600" />
                    <span>{project.assignedCrew} crew</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Truck className="h-4 w-4 text-purple-600" />
                    <span>{project.assignedVehicles} vehicles</span>
                  </div>
                </div>

                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Project Progress</span>
                      <span>{project.progress}%</span>
                    </div>
                    <Progress value={project.progress} className="h-2" />
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Time Progress</span>
                      <span>
                        {daysRemaining > 0 ? `${daysRemaining} days left` : 
                         daysRemaining === 0 ? 'Due today' : 
                         `${Math.abs(daysRemaining)} days overdue`}
                      </span>
                    </div>
                    <Progress 
                      value={timeProgress} 
                      className={`h-2 ${overdue ? '[&>div]:bg-red-500' : ''}`} 
                    />
                  </div>
                </div>

                {/* Timeline connector */}
                {index < sortedProjects.length - 1 && (
                  <div className="absolute -bottom-4 left-2 w-0.5 h-8 bg-gray-200" />
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};
