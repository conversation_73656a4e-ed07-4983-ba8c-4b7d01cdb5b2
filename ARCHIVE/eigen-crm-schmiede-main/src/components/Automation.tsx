
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { AutomationHeader } from './automation/AutomationHeader';
import { AutomationStats } from './automation/AutomationStats';
import { ExecutionLogs } from './automation/ExecutionLogs';
import { AITemplates } from './automation/AITemplates';
import { BrowserAutomation } from './automation/BrowserAutomation';
import { ProviderStatus } from './automation/ProviderStatus';

export const Automation: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');

  const handleRefresh = () => {
    console.log('Refreshing automation status...');
  };

  const handleCreateWorkflow = (type?: string) => {
    console.log('Creating new workflow:', type);
    setActiveTab('browser');
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        <AutomationHeader 
          onRefresh={handleRefresh}
          onCreateWorkflow={handleCreateWorkflow}
        />
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="browser">Browser Automation</TabsTrigger>
            <TabsTrigger value="templates">AI Templates</TabsTrigger>
            <TabsTrigger value="providers">LLM Providers</TabsTrigger>
            <TabsTrigger value="logs">Execution Logs</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <AutomationStats activeWorkflows={8} aiWorkflows={5} />
          </TabsContent>

          <TabsContent value="browser" className="space-y-6">
            <BrowserAutomation />
          </TabsContent>

          <TabsContent value="templates" className="space-y-6">
            <AITemplates onCreateWorkflow={handleCreateWorkflow} />
          </TabsContent>

          <TabsContent value="providers" className="space-y-6">
            <ProviderStatus />
          </TabsContent>

          <TabsContent value="logs" className="space-y-6">
            <ExecutionLogs />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Automation;
