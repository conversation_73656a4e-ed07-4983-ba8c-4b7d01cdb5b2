
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Database } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useSettingsStore } from '@/stores/settingsStore';
import { BackupTab } from './backup/BackupTab';
import { RestoreTab } from './backup/RestoreTab';
import { SyncTab } from './backup/SyncTab';

export const SettingsBackup: React.FC = () => {
  const [importData, setImportData] = useState('');
  const [isImporting, setIsImporting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const { toast } = useToast();
  
  const { 
    exportSettings, 
    importSettings, 
    resetSettings, 
    syncSettings, 
    lastSync 
  } = useSettingsStore();

  const handleExport = async () => {
    setIsExporting(true);
    try {
      const settingsData = exportSettings();
      const blob = new Blob([settingsData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `crm-settings-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast({
        title: "Settings Exported",
        description: "Your settings have been downloaded successfully",
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export settings",
        variant: "destructive"
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleImport = async () => {
    if (!importData.trim()) {
      toast({
        title: "No Data",
        description: "Please paste your settings data",
        variant: "destructive"
      });
      return;
    }

    setIsImporting(true);
    try {
      importSettings(importData);
      setImportData('');
      
      toast({
        title: "Settings Imported",
        description: "Your settings have been restored successfully",
      });
    } catch (error) {
      toast({
        title: "Import Failed",
        description: "Invalid settings format or corrupted data",
        variant: "destructive"
      });
    } finally {
      setIsImporting(false);
    }
  };

  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        setImportData(content);
      };
      reader.readAsText(file);
    }
  };

  const handleSync = async () => {
    setIsSyncing(true);
    try {
      await syncSettings();
      toast({
        title: "Settings Synced",
        description: "Your settings have been synchronized",
      });
    } catch (error) {
      toast({
        title: "Sync Failed",
        description: "Failed to sync settings",
        variant: "destructive"
      });
    } finally {
      setIsSyncing(false);
    }
  };

  const handleReset = () => {
    if (window.confirm('Are you sure? This will reset all settings to default values.')) {
      resetSettings();
      toast({
        title: "Settings Reset",
        description: "All settings have been reset to default values",
      });
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="w-5 h-5" />
            Settings Backup & Restore
          </CardTitle>
          <CardDescription>
            Export, import, and synchronize your CRM settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="backup" className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="backup">Backup</TabsTrigger>
              <TabsTrigger value="restore">Restore</TabsTrigger>
              <TabsTrigger value="sync">Sync</TabsTrigger>
            </TabsList>

            <TabsContent value="backup">
              <BackupTab 
                onExport={handleExport}
                isExporting={isExporting}
              />
            </TabsContent>

            <TabsContent value="restore">
              <RestoreTab
                importData={importData}
                setImportData={setImportData}
                onImport={handleImport}
                onFileImport={handleFileImport}
                isImporting={isImporting}
              />
            </TabsContent>

            <TabsContent value="sync">
              <SyncTab
                lastSync={lastSync}
                onSync={handleSync}
                onReset={handleReset}
                isSyncing={isSyncing}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};
