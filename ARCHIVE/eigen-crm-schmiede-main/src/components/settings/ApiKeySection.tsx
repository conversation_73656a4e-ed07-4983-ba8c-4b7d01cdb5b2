import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, Eye, EyeOff, CheckCircle, AlertCircle, Loader2, RefreshCw, Key } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useSettingsStore } from '@/stores/settingsStore';

interface ApiKeyStatus {
  perplexity: 'connected' | 'disconnected' | 'testing' | 'error';
  vapi: 'connected' | 'disconnected' | 'testing' | 'error';
}

interface ApiKeyValidation {
  isValid: boolean;
  error?: string;
  responseTime?: number;
  details?: string;
}

export const ApiKeySection: React.FC = () => {
  const [showApiKeys, setShowApiKeys] = useState(false);
  const [localKeys, setLocalKeys] = useState({
    perplexity: '',
    vapi: ''
  });
  const [status, setStatus] = useState<ApiKeyStatus>({
    perplexity: 'disconnected',
    vapi: 'disconnected'
  });
  const [validationResults, setValidationResults] = useState<Map<string, ApiKeyValidation>>(new Map());
  const [autoValidate, setAutoValidate] = useState(true);
  const { toast } = useToast();
  
  const { apiKeys, updateApiKeys } = useSettingsStore();

  useEffect(() => {
    // Load API keys from settings store
    setLocalKeys({
      perplexity: apiKeys.perplexity || '',
      vapi: apiKeys.vapi || ''
    });
    
    // Update status based on stored keys
    setStatus({
      perplexity: apiKeys.perplexity ? 'connected' : 'disconnected',
      vapi: apiKeys.vapi ? 'connected' : 'disconnected'
    });
    
    // Auto-validate if enabled
    if (autoValidate) {
      if (apiKeys.perplexity) {
        setTimeout(() => handleTestConnection('perplexity', apiKeys.perplexity), 1000);
      }
      if (apiKeys.vapi) {
        setTimeout(() => handleTestConnection('vapi', apiKeys.vapi), 1500);
      }
    }
  }, [apiKeys, autoValidate]);

  const validatePerplexityKey = async (apiKey: string): Promise<ApiKeyValidation> => {
    const startTime = Date.now();
    
    try {
      const response = await fetch('https://api.perplexity.ai/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'llama-3.1-sonar-small-128k-online',
          messages: [{ role: 'user', content: 'Test connection' }],
          max_tokens: 1
        })
      });

      const responseTime = Date.now() - startTime;

      if (response.ok) {
        return {
          isValid: true,
          responseTime,
          details: 'API key is valid and service is accessible'
        };
      } else if (response.status === 401) {
        return {
          isValid: false,
          error: 'Invalid API key',
          responseTime,
          details: 'The provided API key is not valid'
        };
      } else if (response.status === 429) {
        return {
          isValid: true,
          responseTime,
          details: 'API key is valid but rate limited'
        };
      } else {
        const errorData = await response.json().catch(() => ({}));
        return {
          isValid: false,
          error: `HTTP ${response.status}`,
          responseTime,
          details: errorData.message || 'Service temporarily unavailable'
        };
      }
    } catch (error) {
      return {
        isValid: false,
        error: 'Network error',
        responseTime: Date.now() - startTime,
        details: 'Unable to reach Perplexity API. Check your internet connection.'
      };
    }
  };

  const validateVapiKey = async (apiKey: string): Promise<ApiKeyValidation> => {
    const startTime = Date.now();
    
    try {
      // VAPI validation endpoint - checking account info
      const response = await fetch('https://api.vapi.ai/account', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      const responseTime = Date.now() - startTime;

      if (response.ok) {
        const data = await response.json();
        return {
          isValid: true,
          responseTime,
          details: `Account verified: ${data.name || 'Valid account'}`
        };
      } else if (response.status === 401) {
        return {
          isValid: false,
          error: 'Invalid API key',
          responseTime,
          details: 'The provided VAPI key is not valid'
        };
      } else {
        const errorData = await response.json().catch(() => ({}));
        return {
          isValid: false,
          error: `HTTP ${response.status}`,
          responseTime,
          details: errorData.message || 'VAPI service error'
        };
      }
    } catch (error) {
      return {
        isValid: false,
        error: 'Network error',
        responseTime: Date.now() - startTime,
        details: 'Unable to reach VAPI. Check your internet connection.'
      };
    }
  };

  const handleTestConnection = async (service: 'perplexity' | 'vapi', customKey?: string) => {
    const apiKey = customKey || (service === 'perplexity' ? localKeys.perplexity : localKeys.vapi);
    
    if (!apiKey) {
      toast({
        title: "No API Key",
        description: `Please enter a ${service} API key first`,
        variant: "destructive"
      });
      return;
    }

    setStatus(prev => ({ ...prev, [service]: 'testing' }));
    
    toast({
      title: "Testing Connection",
      description: `Validating ${service} API key...`,
    });

    try {
      const validation = service === 'perplexity' 
        ? await validatePerplexityKey(apiKey)
        : await validateVapiKey(apiKey);

      setValidationResults(prev => new Map(prev.set(service, validation)));
      
      setStatus(prev => ({ 
        ...prev, 
        [service]: validation.isValid ? 'connected' : 'error'
      }));
      
      toast({
        title: validation.isValid ? "Connection Successful" : "Connection Failed",
        description: validation.isValid 
          ? `${service} API is working correctly (${validation.responseTime}ms)`
          : `${service} validation failed: ${validation.error}`,
        variant: validation.isValid ? "default" : "destructive"
      });

    } catch (error) {
      setStatus(prev => ({ ...prev, [service]: 'error' }));
      
      toast({
        title: "Test Failed",
        description: `Failed to test ${service} API key`,
        variant: "destructive"
      });
    }
  };

  const handleSaveApiKeys = () => {
    let savedCount = 0;
    const keysToUpdate: any = {};
    
    if (localKeys.perplexity.trim()) {
      keysToUpdate.perplexity = localKeys.perplexity.trim();
      savedCount++;
    }
    
    if (localKeys.vapi.trim()) {
      keysToUpdate.vapi = localKeys.vapi.trim();
      savedCount++;
    }
    
    if (savedCount > 0) {
      // Update the settings store
      updateApiKeys(keysToUpdate);
      
      // Auto-validate if enabled
      if (autoValidate) {
        if (keysToUpdate.perplexity) {
          handleTestConnection('perplexity');
        }
        if (keysToUpdate.vapi) {
          setTimeout(() => handleTestConnection('vapi'), 500);
        }
      } else {
        setStatus(prev => ({
          ...prev,
          ...(keysToUpdate.perplexity && { perplexity: 'connected' }),
          ...(keysToUpdate.vapi && { vapi: 'connected' })
        }));
      }
      
      toast({
        title: "API Keys Saved",
        description: `${savedCount} API key(s) saved and persisted successfully.`,
      });
    } else {
      toast({
        title: "No Changes",
        description: "No API keys to save.",
        variant: "destructive"
      });
    }
  };

  const handleRefreshAll = async () => {
    const keysToTest = [];
    if (localKeys.perplexity) keysToTest.push('perplexity');
    if (localKeys.vapi) keysToTest.push('vapi');
    
    if (keysToTest.length === 0) {
      toast({
        title: "No API Keys",
        description: "Please configure API keys first",
        variant: "destructive"
      });
      return;
    }

    toast({
      title: "Refreshing Connections",
      description: `Testing ${keysToTest.length} API key(s)...`,
    });

    for (const service of keysToTest) {
      await handleTestConnection(service as 'perplexity' | 'vapi');
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 300));
    }
  };

  const getStatusBadge = (serviceStatus: string, service: string) => {
    const validation = validationResults.get(service);
    
    switch (serviceStatus) {
      case 'connected':
        return (
          <Badge className="bg-green-100 text-green-800 border-green-200">
            <CheckCircle className="w-3 h-3 mr-1" />
            Connected
            {validation?.responseTime && ` (${validation.responseTime}ms)`}
          </Badge>
        );
      case 'testing':
        return (
          <Badge className="bg-blue-100 text-blue-800 border-blue-200">
            <Loader2 className="w-3 h-3 mr-1 animate-spin" />
            Testing...
          </Badge>
        );
      case 'error':
        return (
          <Badge className="bg-red-100 text-red-800 border-red-200">
            <AlertCircle className="w-3 h-3 mr-1" />
            Error
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            <AlertCircle className="w-3 h-3 mr-1" />
            Disconnected
          </Badge>
        );
    }
  };

  const getValidationAlert = (service: string) => {
    const validation = validationResults.get(service);
    if (!validation) return null;

    return (
      <Alert className={`mt-2 ${validation.isValid ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
        <AlertDescription className="text-sm">
          <strong>{validation.isValid ? 'Success:' : 'Error:'}</strong> {validation.details}
          {validation.error && ` (${validation.error})`}
        </AlertDescription>
      </Alert>
    );
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Key className="w-5 h-5" />
              Enhanced API Key Management
            </CardTitle>
            <CardDescription>
              Configure and validate external API keys with persistent storage
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefreshAll}
              disabled={status.perplexity === 'testing' || status.vapi === 'testing'}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Test All
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowApiKeys(!showApiKeys)}
            >
              {showApiKeys ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between p-3 border rounded-lg bg-muted/50">
          <div>
            <h4 className="font-medium">Auto-validation & Persistence</h4>
            <p className="text-sm text-muted-foreground">
              Automatically test and securely store API keys
            </p>
          </div>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={autoValidate}
              onChange={(e) => setAutoValidate(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm">Enable</span>
          </label>
        </div>

        <div className="space-y-6">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="perplexity-key" className="text-base font-medium">
                Perplexity API Key
              </Label>
              {getStatusBadge(status.perplexity, 'perplexity')}
            </div>
            <div className="flex gap-2">
              <Input
                id="perplexity-key"
                type={showApiKeys ? "text" : "password"}
                placeholder="pplx-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                value={localKeys.perplexity}
                onChange={(e) => setLocalKeys(prev => ({ ...prev, perplexity: e.target.value }))}
                className="flex-1"
              />
              <Button 
                variant="outline"
                onClick={() => handleTestConnection('perplexity')}
                disabled={!localKeys.perplexity || status.perplexity === 'testing'}
                size="sm"
              >
                {status.perplexity === 'testing' ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  'Test'
                )}
              </Button>
            </div>
            <p className="text-sm text-muted-foreground">
              Required for AI-powered research and company intelligence
            </p>
            {getValidationAlert('perplexity')}
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="vapi-key" className="text-base font-medium">
                VAPI API Key
              </Label>
              {getStatusBadge(status.vapi, 'vapi')}
            </div>
            <div className="flex gap-2">
              <Input
                id="vapi-key"
                type={showApiKeys ? "text" : "password"}
                placeholder="vapi_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                value={localKeys.vapi}
                onChange={(e) => setLocalKeys(prev => ({ ...prev, vapi: e.target.value }))}
                className="flex-1"
              />
              <Button 
                variant="outline"
                onClick={() => handleTestConnection('vapi')}
                disabled={!localKeys.vapi || status.vapi === 'testing'}
                size="sm"
              >
                {status.vapi === 'testing' ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  'Test'
                )}
              </Button>
            </div>
            <p className="text-sm text-muted-foreground">
              Required for AI-powered voice calls and communication
            </p>
            {getValidationAlert('vapi')}
          </div>
        </div>

        <div className="pt-4 border-t">
          <Button onClick={handleSaveApiKeys} className="w-full" size="lg">
            <Shield className="w-4 h-4 mr-2" />
            Save & Persist API Keys
          </Button>
          <p className="text-xs text-muted-foreground text-center mt-2">
            Keys are stored securely and synchronized across sessions
          </p>
        </div>

        {validationResults.size > 0 && (
          <div className="pt-4 border-t">
            <h4 className="font-medium mb-3">Validation Summary</h4>
            <div className="space-y-2">
              {Array.from(validationResults.entries()).map(([service, validation]) => (
                <div key={service} className="flex items-center justify-between p-2 border rounded text-sm">
                  <span className="font-medium capitalize">{service}</span>
                  <div className="flex items-center gap-2">
                    {validation.isValid ? (
                      <CheckCircle className="w-4 h-4 text-green-600" />
                    ) : (
                      <AlertCircle className="w-4 h-4 text-red-600" />
                    )}
                    <span className="text-muted-foreground">
                      {validation.responseTime}ms
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
