
import { Integration, ConnectionTestResult } from '@/types/integration';

export const simulateConnectionTest = async (integration: Integration): Promise<ConnectionTestResult> => {
  // Simulate network delay
  const baseDelay = Math.random() * 1000 + 500;
  await new Promise(resolve => setTimeout(resolve, baseDelay));

  const responseTime = Math.round(baseDelay);

  // Simulate different test results based on integration type
  switch (integration.id) {
    case 'perplexity':
      const hasPerplexityKey = localStorage.getItem('perplexity_api_key');
      return {
        success: !!hasPerplexityKey,
        responseTime,
        error: hasPerplexityKey ? undefined : 'API key not configured'
      };
      
    case 'vapi':
      const hasVapiKey = localStorage.getItem('vapi_api_key');
      return {
        success: !!hasVapiKey,
        responseTime,
        error: hasVapiKey ? undefined : 'API key not configured'
      };
      
    case 'vector-db':
      return {
        success: true,
        responseTime: Math.round(Math.random() * 200 + 100)
      };
      
    case 'email':
      return {
        success: false,
        responseTime,
        error: 'SMTP configuration required'
      };
      
    case 'automation':
      return {
        success: Math.random() > 0.2, // 80% success rate
        responseTime,
        error: Math.random() > 0.2 ? undefined : 'Webhook endpoint unreachable'
      };
      
    default:
      return {
        success: Math.random() > 0.3,
        responseTime,
        error: Math.random() > 0.3 ? undefined : 'Service unavailable'
      };
  }
};
