
import React from 'react';
import { Progress } from '@/components/ui/progress';
import { Integration } from '@/types/integration';

interface IntegrationHealthPanelProps {
  integrations: Integration[];
  lastRefresh: Date;
}

export const IntegrationHealthPanel: React.FC<IntegrationHealthPanelProps> = ({
  integrations,
  lastRefresh
}) => {
  const connectedCount = integrations.filter(i => i.status === 'connected').length;
  const totalCount = integrations.length;
  const healthPercentage = Math.round((connectedCount / totalCount) * 100);

  return (
    <div className="mb-6 p-4 rounded-lg border bg-muted/50">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h4 className="font-medium">System Health</h4>
          <p className="text-sm text-muted-foreground">
            {connectedCount} of {totalCount} integrations active
          </p>
        </div>
        <div className="text-right">
          <div className={`text-2xl font-bold ${healthPercentage >= 80 ? 'text-green-600' : healthPercentage >= 50 ? 'text-yellow-600' : 'text-red-600'}`}>
            {healthPercentage}%
          </div>
          <p className="text-xs text-muted-foreground">Overall Health</p>
        </div>
      </div>
      <Progress value={healthPercentage} className="h-2" />
      <div className="text-xs text-muted-foreground text-center mt-4 pt-4 border-t">
        Last refreshed: {lastRefresh.toLocaleString()}
      </div>
    </div>
  );
};
