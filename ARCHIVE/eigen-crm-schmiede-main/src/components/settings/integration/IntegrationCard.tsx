
import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Activity,
  Settings
} from 'lucide-react';
import { Integration, ConnectionTest } from '@/types/integration';

interface IntegrationCardProps {
  integration: Integration;
  test?: ConnectionTest;
  onTest: (integration: Integration) => void;
  onConfigure: (integration: Integration) => void;
}

export const IntegrationCard: React.FC<IntegrationCardProps> = ({
  integration,
  test,
  onTest,
  onConfigure
}) => {
  const IconComponent = integration.icon;

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Connected</Badge>;
      case 'testing':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Testing...</Badge>;
      case 'error':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Error</Badge>;
      default:
        return <Badge variant="outline">Disconnected</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'testing':
        return <Clock className="w-4 h-4 text-blue-600 animate-pulse" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-600" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-600" />;
    }
  };

  return (
    <div className="p-4 rounded-lg border">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {getStatusIcon(integration.status)}
            <IconComponent className="w-4 h-4 text-muted-foreground" />
          </div>
          <div>
            <h4 className="font-medium text-sm">{integration.name}</h4>
            {integration.version && (
              <p className="text-xs text-muted-foreground">v{integration.version}</p>
            )}
          </div>
        </div>
        {getStatusBadge(integration.status)}
      </div>
      
      <p className="text-xs text-muted-foreground mb-3">
        {integration.description}
      </p>
      
      <div className="space-y-2 mb-3">
        {integration.lastSync && (
          <div className="flex justify-between text-xs">
            <span>Last sync:</span>
            <span>{integration.lastSync.toLocaleTimeString()}</span>
          </div>
        )}
        {integration.responseTime && (
          <div className="flex justify-between text-xs">
            <span>Response time:</span>
            <span className={integration.responseTime < 1000 ? 'text-green-600' : 'text-yellow-600'}>
              {integration.responseTime}ms
            </span>
          </div>
        )}
        {integration.errorMessage && (
          <div className="text-xs text-red-600">
            Error: {integration.errorMessage}
          </div>
        )}
      </div>
      
      <div className="flex gap-2">
        <Button 
          size="sm" 
          variant="outline" 
          className="flex-1"
          onClick={() => onTest(integration)}
          disabled={test?.isRunning}
        >
          <Activity className="w-3 h-3 mr-1" />
          {test?.isRunning ? 'Testing...' : 'Test'}
        </Button>
        <Button 
          size="sm" 
          variant="outline" 
          onClick={() => onConfigure(integration)}
        >
          <Settings className="w-3 h-3 mr-1" />
          Configure
        </Button>
      </div>
    </div>
  );
};
