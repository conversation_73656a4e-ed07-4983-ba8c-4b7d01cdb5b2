
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Brain, Database, Upload, RefreshCw, FileText, Search } from 'lucide-react';
import { vectorDB } from '@/services/VectorDatabase';
import { RAGService } from '@/services/RAGService';
import { DocumentIndexingService } from '@/services/DocumentIndexingService';

export const RAGSettings = () => {
  const [isRAGEnabled, setIsRAGEnabled] = useState(true);
  const [isInitializing, setIsInitializing] = useState(false);
  const [documentCount, setDocumentCount] = useState(0);
  const [indexingProgress, setIndexingProgress] = useState(0);
  const [isIndexingSample, setIsIndexingSample] = useState(false);

  useEffect(() => {
    setDocumentCount(vectorDB.getDocumentCount());
  }, []);

  const handleInitializeRAG = async () => {
    setIsInitializing(true);
    try {
      await vectorDB.initialize();
      setDocumentCount(vectorDB.getDocumentCount());
    } catch (error) {
      console.error('Failed to initialize RAG:', error);
    } finally {
      setIsInitializing(false);
    }
  };

  const handleIndexSampleDocuments = async () => {
    setIsIndexingSample(true);
    setIndexingProgress(0);
    
    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setIndexingProgress(prev => Math.min(prev + 20, 90));
      }, 500);

      await RAGService.indexSampleDocuments();
      
      clearInterval(progressInterval);
      setIndexingProgress(100);
      setDocumentCount(vectorDB.getDocumentCount());
      
      setTimeout(() => {
        setIndexingProgress(0);
        setIsIndexingSample(false);
      }, 1000);
    } catch (error) {
      console.error('Failed to index sample documents:', error);
      setIsIndexingSample(false);
      setIndexingProgress(0);
    }
  };

  const handleClearDatabase = () => {
    vectorDB.clearAll();
    setDocumentCount(0);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            RAG & Vector Database
          </CardTitle>
          <CardDescription>
            Configure Retrieval-Augmented Generation for intelligent document search and context-aware responses
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable RAG System</Label>
              <p className="text-sm text-muted-foreground">
                Use document knowledge base for enhanced AI responses
              </p>
            </div>
            <Switch
              checked={isRAGEnabled}
              onCheckedChange={setIsRAGEnabled}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 rounded-lg border bg-card">
              <div className="flex items-center gap-2 mb-2">
                <Database className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium">Vector Database</span>
              </div>
              <div className="text-2xl font-bold">{documentCount}</div>
              <p className="text-xs text-muted-foreground">Indexed documents</p>
            </div>

            <div className="p-4 rounded-lg border bg-card">
              <div className="flex items-center gap-2 mb-2">
                <FileText className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium">Document Types</span>
              </div>
              <div className="text-2xl font-bold">4</div>
              <p className="text-xs text-muted-foreground">Supported formats</p>
            </div>

            <div className="p-4 rounded-lg border bg-card">
              <div className="flex items-center gap-2 mb-2">
                <Search className="h-4 w-4 text-purple-600" />
                <span className="text-sm font-medium">Search Quality</span>
              </div>
              <div className="text-2xl font-bold">85%</div>
              <p className="text-xs text-muted-foreground">Average relevance</p>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Vector Database Status</h4>
                <p className="text-sm text-muted-foreground">
                  Initialize the embedding model for semantic search
                </p>
              </div>
              <Button
                onClick={handleInitializeRAG}
                disabled={isInitializing}
                variant="outline"
              >
                {isInitializing ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Database className="h-4 w-4 mr-2" />
                )}
                {isInitializing ? 'Initializing...' : 'Initialize'}
              </Button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Sample Documents</h4>
                <p className="text-sm text-muted-foreground">
                  Index sample construction documents for testing
                </p>
              </div>
              <Button
                onClick={handleIndexSampleDocuments}
                disabled={isIndexingSample}
                variant="outline"
              >
                {isIndexingSample ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Upload className="h-4 w-4 mr-2" />
                )}
                {isIndexingSample ? 'Indexing...' : 'Index Samples'}
              </Button>
            </div>

            {isIndexingSample && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Indexing progress</span>
                  <span>{indexingProgress}%</span>
                </div>
                <Progress value={indexingProgress} className="w-full" />
              </div>
            )}

            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Clear Database</h4>
                <p className="text-sm text-muted-foreground">
                  Remove all indexed documents and reset
                </p>
              </div>
              <Button
                onClick={handleClearDatabase}
                variant="destructive"
                size="sm"
              >
                Clear All
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium">Supported Document Types</h4>
            <div className="flex flex-wrap gap-2">
              {['PDF', 'DOCX', 'TXT', 'Markdown'].map((type) => (
                <Badge key={type} variant="secondary">
                  {type}
                </Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>RAG Configuration</CardTitle>
          <CardDescription>Fine-tune RAG behavior and performance</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Search Results Limit</Label>
              <select className="w-full p-2 border rounded-md">
                <option value="3">3 documents</option>
                <option value="5" selected>5 documents</option>
                <option value="10">10 documents</option>
              </select>
            </div>

            <div className="space-y-2">
              <Label>Similarity Threshold</Label>
              <select className="w-full p-2 border rounded-md">
                <option value="0.2">Low (0.2)</option>
                <option value="0.3" selected>Medium (0.3)</option>
                <option value="0.5">High (0.5)</option>
              </select>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Project-Specific Search</Label>
              <p className="text-sm text-muted-foreground">
                Prioritize documents from the current project context
              </p>
            </div>
            <Switch defaultChecked />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Auto-Index Uploads</Label>
              <p className="text-sm text-muted-foreground">
                Automatically index new document uploads
              </p>
            </div>
            <Switch defaultChecked />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
