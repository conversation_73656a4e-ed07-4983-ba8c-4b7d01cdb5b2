
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Brain, Zap, DollarSign, Shield, Server, Activity } from 'lucide-react';
import { useEnhancedAI } from '@/contexts/EnhancedAIContext';
import { RoutingProfile } from '@/types/provider';

export const ProviderManagement: React.FC = () => {
  const { router, currentProfile, setRoutingProfile, getUsageStats } = useEnhancedAI();
  const [usageStats, setUsageStats] = useState<Map<string, any>>(new Map());
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    setUsageStats(getUsageStats());
  }, [refreshKey, getUsageStats]);

  const handleProfileChange = (profile: RoutingProfile) => {
    setRoutingProfile(profile);
  };

  const refreshStats = () => {
    setRefreshKey(prev => prev + 1);
  };

  const getProfileIcon = (profile: RoutingProfile) => {
    switch (profile) {
      case 'privacy':
        return <Shield className="w-4 h-4" />;
      case 'efficiency':
        return <Zap className="w-4 h-4" />;
      case 'cost':
        return <DollarSign className="w-4 h-4" />;
      default:
        return <Brain className="w-4 h-4" />;
    }
  };

  const getProviderStatusColor = (providerId: string) => {
    const quota = router.getRemainingQuota(providerId);
    if (!quota) return 'gray';
    
    const requestsRemaining = quota.requestsPerMinute;
    if (requestsRemaining > 10) return 'green';
    if (requestsRemaining > 0) return 'yellow';
    return 'red';
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5" />
            AI Provider Management
          </CardTitle>
          <CardDescription>
            Configure intelligent routing between AI providers based on rate limits, privacy, efficiency, and cost
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Routing Profile</Label>
              <Select value={currentProfile} onValueChange={handleProfileChange}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="privacy">
                    <div className="flex items-center gap-2">
                      <Shield className="w-4 h-4" />
                      Privacy First
                    </div>
                  </SelectItem>
                  <SelectItem value="efficiency">
                    <div className="flex items-center gap-2">
                      <Zap className="w-4 h-4" />
                      Efficiency First
                    </div>
                  </SelectItem>
                  <SelectItem value="cost">
                    <div className="flex items-center gap-2">
                      <DollarSign className="w-4 h-4" />
                      Cost Optimized
                    </div>
                  </SelectItem>
                  <SelectItem value="balanced">
                    <div className="flex items-center gap-2">
                      <Brain className="w-4 h-4" />
                      Balanced
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Shield className="w-4 h-4 text-blue-500" />
                  <span className="text-sm font-medium">Privacy</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Prioritizes local and privacy-focused providers
                </p>
              </Card>
              
              <Card className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Zap className="w-4 h-4 text-green-500" />
                  <span className="text-sm font-medium">Efficiency</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Optimizes for speed and reliability
                </p>
              </Card>
              
              <Card className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <DollarSign className="w-4 h-4 text-yellow-500" />
                  <span className="text-sm font-medium">Cost</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Minimizes costs using free tiers first
                </p>
              </Card>
              
              <Card className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Brain className="w-4 h-4 text-purple-500" />
                  <span className="text-sm font-medium">Balanced</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Balanced approach across all factors
                </p>
              </Card>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="providers">
        <TabsList>
          <TabsTrigger value="providers">Providers</TabsTrigger>
          <TabsTrigger value="usage">Usage Stats</TabsTrigger>
          <TabsTrigger value="routing">Routing Logic</TabsTrigger>
        </TabsList>

        <TabsContent value="providers" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {router['configManager'].getAllConfigs().map((provider) => {
              const usage = usageStats.get(provider.id);
              const quota = router.getRemainingQuota(provider.id);
              const statusColor = getProviderStatusColor(provider.id);
              
              return (
                <Card key={provider.id}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-base">{provider.name}</CardTitle>
                      <div className="flex items-center gap-2">
                        {provider.isLocal && <Badge variant="outline">Local</Badge>}
                        <div className={`w-3 h-3 rounded-full bg-${statusColor}-500`} />
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Privacy:</span>
                        <div className="font-medium">{provider.privacyScore}/10</div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Cost:</span>
                        <div className="font-medium">{provider.costPerRequest}¢</div>
                      </div>
                    </div>
                    
                    {quota && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-xs">
                          <span>Requests/min</span>
                          <span>{quota.requestsPerMinute}/{provider.rateLimits.requestsPerMinute}</span>
                        </div>
                        <Progress 
                          value={(quota.requestsPerMinute / provider.rateLimits.requestsPerMinute) * 100} 
                          className="h-1"
                        />
                      </div>
                    )}
                    
                    <div className="flex flex-wrap gap-1">
                      {provider.capabilities.map((cap) => (
                        <Badge key={cap} variant="secondary" className="text-xs">
                          {cap}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="usage">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Usage Statistics</CardTitle>
                <Button onClick={refreshStats} variant="outline" size="sm">
                  <Activity className="w-4 h-4 mr-2" />
                  Refresh
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {usageStats.size === 0 ? (
                <p className="text-muted-foreground text-center py-8">
                  No usage data available yet. Start making AI requests to see statistics.
                </p>
              ) : (
                <div className="space-y-4">
                  {Array.from(usageStats.entries()).map(([providerId, usage]) => (
                    <div key={providerId} className="border rounded-lg p-4">
                      <h4 className="font-medium mb-2">{providerId}</h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground">Today:</span>
                          <div className="font-medium">{usage.requestsToday} requests</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">This minute:</span>
                          <div className="font-medium">{usage.requestsThisMinute} requests</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Tokens used:</span>
                          <div className="font-medium">{usage.tokensToday.toLocaleString()}</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Total cost:</span>
                          <div className="font-medium">${usage.totalCost.toFixed(2)}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="routing">
          <Card>
            <CardHeader>
              <CardTitle>Routing Logic</CardTitle>
              <CardDescription>
                How the system selects providers based on your current profile: {currentProfile}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="p-4">
                    <h4 className="font-medium mb-2">Selection Criteria</h4>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      <li>• Task capability match</li>
                      <li>• Rate limit availability</li>
                      <li>• Cost constraints</li>
                      <li>• Data sensitivity requirements</li>
                    </ul>
                  </Card>
                  
                  <Card className="p-4">
                    <h4 className="font-medium mb-2">Scoring Factors</h4>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      <li>• Privacy score (0-100)</li>
                      <li>• Efficiency rating</li>
                      <li>• Cost optimization</li>
                      <li>• Specialization bonus</li>
                    </ul>
                  </Card>
                  
                  <Card className="p-4">
                    <h4 className="font-medium mb-2">Fallback Strategy</h4>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      <li>• Auto-retry on failure</li>
                      <li>• Alternative provider selection</li>
                      <li>• Graceful degradation</li>
                      <li>• Error reporting</li>
                    </ul>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
