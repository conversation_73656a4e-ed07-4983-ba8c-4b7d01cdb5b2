
import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Upload, RefreshCw, AlertTriangle } from 'lucide-react';

interface RestoreTabProps {
  importData: string;
  setImportData: (data: string) => void;
  onImport: () => void;
  onFileImport: (event: React.ChangeEvent<HTMLInputElement>) => void;
  isImporting: boolean;
}

export const RestoreTab: React.FC<RestoreTabProps> = ({
  importData,
  setImportData,
  onImport,
  onFileImport,
  isImporting
}) => {
  return (
    <div className="space-y-4">
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Importing settings will overwrite your current configuration
        </AlertDescription>
      </Alert>

      <div className="space-y-4">
        <div>
          <Label htmlFor="file-upload">Upload Settings File</Label>
          <Input
            id="file-upload"
            type="file"
            accept=".json"
            onChange={onFileImport}
            className="mt-2"
          />
        </div>

        <div className="text-center text-muted-foreground">
          <span>or</span>
        </div>

        <div>
          <Label htmlFor="settings-data">Paste Settings Data</Label>
          <Textarea
            id="settings-data"
            placeholder="Paste your exported settings JSON here..."
            value={importData}
            onChange={(e) => setImportData(e.target.value)}
            rows={8}
            className="mt-2 font-mono text-sm"
          />
        </div>

        <Button 
          onClick={onImport}
          disabled={!importData.trim() || isImporting}
          className="w-full"
        >
          {isImporting ? (
            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <Upload className="w-4 h-4 mr-2" />
          )}
          Import Settings
        </Button>
      </div>
    </div>
  );
};
