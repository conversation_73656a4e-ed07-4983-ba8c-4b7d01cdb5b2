
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Database, CheckCircle, AlertTriangle, Trash2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface DatabaseStats {
  companies: number;
  contacts: number;
  opportunities: number;
  conversations: number;
  totalStorage: number;
}

export const DatabaseSettings: React.FC = () => {
  const [stats, setStats] = useState<DatabaseStats>({
    companies: 0,
    contacts: 0,
    opportunities: 0,
    conversations: 0,
    totalStorage: 0
  });
  const [isClearing, setIsClearing] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadDatabaseStats();
  }, []);

  const loadDatabaseStats = () => {
    // Calculate storage from localStorage
    let totalStorage = 0;
    const companies = JSON.parse(localStorage.getItem('companies') || '[]');
    const contacts = JSON.parse(localStorage.getItem('contacts') || '[]');
    const opportunities = JSON.parse(localStorage.getItem('opportunities') || '[]');
    const conversations = JSON.parse(localStorage.getItem('ai_conversations') || '[]');

    // Calculate approximate storage size
    Object.keys(localStorage).forEach(key => {
      totalStorage += localStorage.getItem(key)?.length || 0;
    });

    setStats({
      companies: companies.length,
      contacts: contacts.length,
      opportunities: opportunities.length,
      conversations: conversations.length,
      totalStorage: Math.round(totalStorage / 1024) // Convert to KB
    });
  };

  const clearLocalData = async () => {
    setIsClearing(true);
    
    // Clear specific AI-related data
    const keysToRemove = [
      'companies',
      'contacts', 
      'opportunities',
      'ai_conversations',
      'ai_agents_state'
    ];
    
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
    });
    
    setTimeout(() => {
      setIsClearing(false);
      loadDatabaseStats();
      toast({
        title: "Data Cleared",
        description: "Local database has been cleared successfully.",
      });
    }, 1500);
  };

  const exportData = () => {
    const data = {
      companies: JSON.parse(localStorage.getItem('companies') || '[]'),
      contacts: JSON.parse(localStorage.getItem('contacts') || '[]'),
      opportunities: JSON.parse(localStorage.getItem('opportunities') || '[]'),
      conversations: JSON.parse(localStorage.getItem('ai_conversations') || '[]'),
      exportDate: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { 
      type: 'application/json' 
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `crm-export-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    toast({
      title: "Data Exported",
      description: "Your data has been downloaded as a JSON file.",
    });
  };

  const storagePercentage = Math.min((stats.totalStorage / 5000) * 100, 100); // Assume 5MB limit

  return (
    <Card>
      <CardHeader>
        <CardTitle>Database Configuration</CardTitle>
        <CardDescription>Manage your local data and storage</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="p-4 rounded-lg bg-blue-50 border border-blue-200">
          <div className="flex items-start gap-3">
            <Database className="w-5 h-5 text-blue-600 mt-0.5" />
            <div>
              <p className="text-sm text-blue-800 font-medium">
                Currently using Local Storage
              </p>
              <p className="text-sm text-blue-600 mt-1">
                Connect to Supabase for full database functionality, real-time sync, and unlimited storage.
              </p>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <div className="flex items-center justify-between mb-2">
              <Label>Storage Usage</Label>
              <span className="text-sm text-muted-foreground">{stats.totalStorage} KB</span>
            </div>
            <Progress value={storagePercentage} className="h-2" />
            <p className="text-xs text-muted-foreground mt-1">
              {storagePercentage < 80 ? 'Storage usage is normal' : 'Consider clearing old data or upgrading to Supabase'}
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 rounded-lg border">
              <div className="text-2xl font-bold text-blue-600">{stats.companies}</div>
              <div className="text-sm text-muted-foreground">Companies</div>
            </div>
            <div className="text-center p-3 rounded-lg border">
              <div className="text-2xl font-bold text-green-600">{stats.contacts}</div>
              <div className="text-sm text-muted-foreground">Contacts</div>
            </div>
            <div className="text-center p-3 rounded-lg border">
              <div className="text-2xl font-bold text-purple-600">{stats.opportunities}</div>
              <div className="text-sm text-muted-foreground">Opportunities</div>
            </div>
            <div className="text-center p-3 rounded-lg border">
              <div className="text-2xl font-bold text-orange-600">{stats.conversations}</div>
              <div className="text-sm text-muted-foreground">Conversations</div>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <Label>Data Management</Label>
          <div className="flex gap-2">
            <Button variant="outline" onClick={exportData}>
              Export Data
            </Button>
            <Button 
              variant="destructive" 
              onClick={clearLocalData}
              disabled={isClearing}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              {isClearing ? 'Clearing...' : 'Clear All Data'}
            </Button>
          </div>
          <p className="text-sm text-muted-foreground">
            Export your data before clearing to avoid permanent loss
          </p>
        </div>

        <div className="space-y-2">
          <Label>Available Tables</Label>
          <div className="text-sm text-muted-foreground space-y-1">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-3 h-3 text-green-500" />
              <span>companies - Company information and research data</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="w-3 h-3 text-green-500" />
              <span>contacts - Contact details and lead information</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="w-3 h-3 text-green-500" />
              <span>opportunities - Deal pipeline and sales data</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="w-3 h-3 text-green-500" />
              <span>conversations - AI chat history and context</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
