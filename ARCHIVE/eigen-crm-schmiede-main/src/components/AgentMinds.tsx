
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { 
  Brain, 
  Plus, 
  Search, 
  Play,
  Square,
  Settings,
  Zap,
  Users,
  Target,
  Activity,
  Eye,
  Edit,
  Trash2
} from 'lucide-react';

interface AgentMind {
  id: string;
  name: string;
  description: string;
  type: 'sales' | 'research' | 'support' | 'analysis' | 'automation';
  status: 'active' | 'inactive' | 'training';
  capabilities: string[];
  performance: {
    tasksCompleted: number;
    successRate: number;
    avgResponseTime: string;
  };
  lastActivity: string;
  createdAt: string;
}

const mockAgentMinds: AgentMind[] = [
  {
    id: '1',
    name: 'Sales Research Agent',
    description: 'Automatically researches prospects and identifies opportunities',
    type: 'sales',
    status: 'active',
    capabilities: ['Lead Research', 'Company Analysis', 'Contact Finding'],
    performance: {
      tasksCompleted: 247,
      successRate: 94,
      avgResponseTime: '2.3s'
    },
    lastActivity: '2 minutes ago',
    createdAt: '2024-01-15'
  },
  {
    id: '2',
    name: 'Market Intelligence',
    description: 'Monitors market trends and competitive landscape',
    type: 'research',
    status: 'active',
    capabilities: ['Market Analysis', 'Competitor Tracking', 'Trend Detection'],
    performance: {
      tasksCompleted: 156,
      successRate: 89,
      avgResponseTime: '4.1s'
    },
    lastActivity: '15 minutes ago',
    createdAt: '2024-01-10'
  },
  {
    id: '3',
    name: 'Customer Support Bot',
    description: 'Handles customer inquiries and support tickets',
    type: 'support',
    status: 'training',
    capabilities: ['Ticket Management', 'FAQ Response', 'Escalation Handling'],
    performance: {
      tasksCompleted: 89,
      successRate: 76,
      avgResponseTime: '1.8s'
    },
    lastActivity: '1 hour ago',
    createdAt: '2024-01-20'
  }
];

export const AgentMinds = () => {
  const [agents, setAgents] = useState<AgentMind[]>(mockAgentMinds);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<string>('all');

  const filteredAgents = agents.filter(agent => {
    const matchesSearch = agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         agent.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === 'all' || agent.type === selectedType;
    return matchesSearch && matchesType;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'training': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'sales': return Target;
      case 'research': return Brain;
      case 'support': return Users;
      case 'analysis': return Activity;
      case 'automation': return Zap;
      default: return Brain;
    }
  };

  const stats = {
    total: agents.length,
    active: agents.filter(a => a.status === 'active').length,
    training: agents.filter(a => a.status === 'training').length,
    avgSuccessRate: Math.round(agents.reduce((acc, a) => acc + a.performance.successRate, 0) / agents.length)
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground flex items-center gap-2">
            <Brain className="w-8 h-8" />
            Agent Minds
          </h1>
          <p className="text-muted-foreground">Manage and monitor your AI agent workforce</p>
        </div>
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          Create Agent
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Agents</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <Badge className="bg-green-100 text-green-800">Live</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.active}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Training</CardTitle>
            <Badge className="bg-yellow-100 text-yellow-800">Learning</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.training}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Success Rate</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.avgSuccessRate}%</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search agents..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Tabs value={selectedType} onValueChange={setSelectedType}>
          <TabsList>
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="sales">Sales</TabsTrigger>
            <TabsTrigger value="research">Research</TabsTrigger>
            <TabsTrigger value="support">Support</TabsTrigger>
            <TabsTrigger value="analysis">Analysis</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Agents Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredAgents.map((agent) => {
          const TypeIcon = getTypeIcon(agent.type);
          return (
            <Card key={agent.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <TypeIcon className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{agent.name}</CardTitle>
                      <p className="text-sm text-muted-foreground capitalize">{agent.type}</p>
                    </div>
                  </div>
                  <Badge variant="outline" className={getStatusColor(agent.status)}>
                    {agent.status}
                  </Badge>
                </div>
                <CardDescription>{agent.description}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Tasks Completed</span>
                  <span className="font-medium">{agent.performance.tasksCompleted}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Success Rate</span>
                  <span className="font-medium">{agent.performance.successRate}%</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Avg Response</span>
                  <span className="font-medium">{agent.performance.avgResponseTime}</span>
                </div>
                
                <div className="flex flex-wrap gap-1 mb-4">
                  {agent.capabilities.slice(0, 2).map(capability => (
                    <Badge key={capability} variant="secondary" className="text-xs">
                      {capability}
                    </Badge>
                  ))}
                  {agent.capabilities.length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{agent.capabilities.length - 2} more
                    </Badge>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-xs text-muted-foreground">
                    Last activity: {agent.lastActivity}
                  </span>
                  <div className="flex items-center gap-1">
                    <Button size="sm" variant="outline">
                      <Eye className="w-3 h-3" />
                    </Button>
                    <Button size="sm" variant="outline">
                      <Edit className="w-3 h-3" />
                    </Button>
                    <Button size="sm" variant="outline">
                      {agent.status === 'active' ? (
                        <Square className="w-3 h-3" />
                      ) : (
                        <Play className="w-3 h-3" />
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredAgents.length === 0 && (
        <div className="text-center py-12">
          <Brain className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">No agents found</h3>
          <p className="text-muted-foreground mb-4">
            {searchTerm || selectedType !== 'all' 
              ? 'Try adjusting your search or filters'
              : 'Create your first AI agent to get started'
            }
          </p>
          {!searchTerm && selectedType === 'all' && (
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Create First Agent
            </Button>
          )}
        </div>
      )}
    </div>
  );
};
