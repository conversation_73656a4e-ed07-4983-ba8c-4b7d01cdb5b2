
import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Settings as SettingsIcon, Key, Bell, Download, Upload, RefreshCw } from 'lucide-react'
import { useSettingsStore } from '@/stores/settingsStore'
import { BackupTab } from '@/components/settings/backup/BackupTab'
import { RestoreTab } from '@/components/settings/backup/RestoreTab'
import { SyncTab } from '@/components/settings/backup/SyncTab'

export const Settings = () => {
  const { 
    apiKeys, 
    notifications, 
    updateApiKeys, 
    updateNotifications,
    exportSettings,
    importSettings,
    syncSettings,
    resetSettings,
    lastSync
  } = useSettingsStore()
  
  const [isExporting, setIsExporting] = useState(false)
  const [isImporting, setIsImporting] = useState(false)
  const [isSyncing, setIsSyncing] = useState(false)
  const [importData, setImportData] = useState('')

  const handleExport = async () => {
    setIsExporting(true)
    try {
      const data = exportSettings()
      const blob = new Blob([data], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `crm-settings-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } finally {
      setIsExporting(false)
    }
  }

  const handleImport = async () => {
    if (!importData.trim()) return
    setIsImporting(true)
    try {
      importSettings(importData)
      setImportData('')
    } finally {
      setIsImporting(false)
    }
  }

  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const content = e.target?.result as string
        setImportData(content)
      }
      reader.readAsText(file)
    }
  }

  const handleSync = async () => {
    setIsSyncing(true)
    try {
      await syncSettings()
    } finally {
      setIsSyncing(false)
    }
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground flex items-center gap-2">
            <SettingsIcon className="w-8 h-8" />
            Settings
          </h1>
          <p className="text-muted-foreground">Manage your CRM configuration</p>
        </div>
        <Badge variant="outline">
          Last sync: {lastSync ? lastSync.toLocaleDateString() : 'Never'}
        </Badge>
      </div>

      <Tabs defaultValue="api-keys" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="api-keys">API Keys</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="backup">Backup</TabsTrigger>
          <TabsTrigger value="sync">Sync</TabsTrigger>
        </TabsList>

        <TabsContent value="api-keys" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="w-5 h-5" />
                API Keys
              </CardTitle>
              <CardDescription>
                Configure your API keys for external services
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="openai-key">OpenAI API Key</Label>
                  <Input
                    id="openai-key"
                    type="password"
                    placeholder="sk-..."
                    value={apiKeys.openai || ''}
                    onChange={(e) => updateApiKeys({ openai: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="anthropic-key">Anthropic API Key</Label>
                  <Input
                    id="anthropic-key"
                    type="password"
                    placeholder="sk-ant-..."
                    value={apiKeys.anthropic || ''}
                    onChange={(e) => updateApiKeys({ anthropic: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="perplexity-key">Perplexity API Key</Label>
                  <Input
                    id="perplexity-key"
                    type="password"
                    placeholder="pplx-..."
                    value={apiKeys.perplexity || ''}
                    onChange={(e) => updateApiKeys({ perplexity: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="vapi-key">VAPI Key</Label>
                  <Input
                    id="vapi-key"
                    type="password"
                    placeholder="vapi-..."
                    value={apiKeys.vapi || ''}
                    onChange={(e) => updateApiKeys({ vapi: e.target.value })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="w-5 h-5" />
                Notification Settings
              </CardTitle>
              <CardDescription>
                Configure how you receive notifications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="email-notifications">Email Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Receive notifications via email
                  </p>
                </div>
                <Switch
                  id="email-notifications"
                  checked={notifications.email}
                  onCheckedChange={(checked) => updateNotifications({ email: checked })}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="push-notifications">Push Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Receive push notifications in your browser
                  </p>
                </div>
                <Switch
                  id="push-notifications"
                  checked={notifications.push}
                  onCheckedChange={(checked) => updateNotifications({ push: checked })}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="slack-notifications">Slack Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Send notifications to Slack
                  </p>
                </div>
                <Switch
                  id="slack-notifications"
                  checked={notifications.slack}
                  onCheckedChange={(checked) => updateNotifications({ slack: checked })}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="backup" className="space-y-4">
          <BackupTab onExport={handleExport} isExporting={isExporting} />
          <RestoreTab
            importData={importData}
            setImportData={setImportData}
            onImport={handleImport}
            onFileImport={handleFileImport}
            isImporting={isImporting}
          />
        </TabsContent>

        <TabsContent value="sync" className="space-y-4">
          <SyncTab
            lastSync={lastSync}
            onSync={handleSync}
            onReset={resetSettings}
            isSyncing={isSyncing}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
