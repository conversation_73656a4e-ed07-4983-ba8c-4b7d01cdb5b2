
import React from 'react';
import { ProjectForm } from './projects/ProjectForm';
import { ProjectsHeader } from './projects/ProjectsHeader';
import { ProjectsStatsCards } from './projects/ProjectsStatsCards';
import { ProjectTabs } from './projects/ProjectTabs';
import { useProjectManagement } from '@/hooks/useProjectManagement';
import { useProjectFilters } from '@/hooks/useProjectFilters';
import { ProjectFormData } from '@/types/project';
import { mockProjects } from '@/data/mockProjects';

export const Projects = () => {
  const {
    projects,
    showForm,
    setShowForm,
    editingProject,
    setEditingProject,
    handleAddProject,
    handleEditProject
  } = useProjectManagement(mockProjects);

  const {
    filters,
    setFilters,
    filteredProjects,
    resetFilters
  } = useProjectFilters(projects);

  if (showForm) {
    return (
      <ProjectForm 
        onSubmit={handleAddProject}
        onCancel={() => setShowForm(false)}
      />
    );
  }

  if (editingProject) {
    const formData: ProjectFormData = {
      ...editingProject,
      estimatedBudget: editingProject.estimatedBudget.toString()
    };
    
    return (
      <ProjectForm 
        onSubmit={handleEditProject}
        onCancel={() => setEditingProject(null)}
        initialData={formData}
        isEdit={true}
      />
    );
  }

  return (
    <div className="p-6 space-y-6">
      <ProjectsHeader onAddProject={() => setShowForm(true)} />
      
      <ProjectsStatsCards projects={projects} />

      <ProjectTabs
        filteredProjects={filteredProjects}
        filters={filters}
        onFiltersChange={setFilters}
        onResetFilters={resetFilters}
        onEditProject={setEditingProject}
        onAddProject={() => setShowForm(true)}
      />
    </div>
  );
};
