
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle,
  TrendingUp,
  Calendar,
  FileCheck
} from 'lucide-react';

export const ComplianceMonitoring = () => {
  const complianceData = [
    { area: 'PPE Compliance', score: 98, trend: 'up', status: 'excellent' },
    { area: 'Equipment Safety', score: 92, trend: 'stable', status: 'good' },
    { area: 'Training Requirements', score: 88, trend: 'down', status: 'needs-attention' },
    { area: 'Emergency Procedures', score: 100, trend: 'up', status: 'excellent' },
    { area: 'Documentation', score: 85, trend: 'up', status: 'good' }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'bg-green-100 text-green-800';
      case 'good': return 'bg-blue-100 text-blue-800';
      case 'needs-attention': return 'bg-yellow-100 text-yellow-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'excellent': return <CheckCircle className="h-4 w-4" />;
      case 'good': return <CheckCircle className="h-4 w-4" />;
      case 'needs-attention': return <AlertTriangle className="h-4 w-4" />;
      case 'critical': return <XCircle className="h-4 w-4" />;
      default: return <CheckCircle className="h-4 w-4" />;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'down': return <TrendingUp className="h-4 w-4 text-red-600 rotate-180" />;
      case 'stable': return <div className="h-4 w-4 bg-gray-400 rounded-full" />;
      default: return <div className="h-4 w-4 bg-gray-400 rounded-full" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold">Compliance Monitoring</h2>
        <p className="text-muted-foreground">Track compliance across safety areas</p>
      </div>

      {/* Overall Compliance Score */}
      <Card>
        <CardHeader>
          <CardTitle>Overall Compliance Score</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-4xl font-bold text-blue-600">94%</p>
              <p className="text-muted-foreground">Current compliance level</p>
            </div>
            <div className="text-right">
              <div className="flex items-center gap-2 text-green-600">
                <TrendingUp className="h-5 w-5" />
                <span className="font-semibold">+2%</span>
              </div>
              <p className="text-sm text-muted-foreground">vs last month</p>
            </div>
          </div>
          <Progress value={94} className="h-3" />
        </CardContent>
      </Card>

      {/* Compliance Areas */}
      <Card>
        <CardHeader>
          <CardTitle>Compliance by Area</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {complianceData.map((area, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <h4 className="font-medium">{area.area}</h4>
                    <Badge className={getStatusColor(area.status)}>
                      {getStatusIcon(area.status)}
                      <span className="ml-1">{area.status.replace('-', ' ')}</span>
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    {getTrendIcon(area.trend)}
                    <span className="font-semibold">{area.score}%</span>
                  </div>
                </div>
                <Progress value={area.score} className="h-2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Audits */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Audits</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                <FileCheck className="h-5 w-5 text-blue-600" />
                <div>
                  <h4 className="font-medium">OSHA Safety Inspection</h4>
                  <p className="text-sm text-muted-foreground">Quarterly safety compliance review</p>
                </div>
              </div>
              <div className="text-right">
                <Badge className="bg-green-100 text-green-800">Passed</Badge>
                <p className="text-sm text-muted-foreground mt-1">Jan 15, 2024</p>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                <FileCheck className="h-5 w-5 text-blue-600" />
                <div>
                  <h4 className="font-medium">Equipment Safety Audit</h4>
                  <p className="text-sm text-muted-foreground">Heavy machinery safety compliance</p>
                </div>
              </div>
              <div className="text-right">
                <Badge className="bg-yellow-100 text-yellow-800">Minor Issues</Badge>
                <p className="text-sm text-muted-foreground mt-1">Jan 10, 2024</p>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                <FileCheck className="h-5 w-5 text-blue-600" />
                <div>
                  <h4 className="font-medium">Training Compliance Review</h4>
                  <p className="text-sm text-muted-foreground">Employee certification status</p>
                </div>
              </div>
              <div className="text-right">
                <Badge className="bg-green-100 text-green-800">Compliant</Badge>
                <p className="text-sm text-muted-foreground mt-1">Jan 5, 2024</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Upcoming Requirements */}
      <Card>
        <CardHeader>
          <CardTitle>Upcoming Compliance Requirements</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center gap-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <Calendar className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="font-medium">Monthly Safety Training</p>
                <p className="text-sm text-muted-foreground">Due: February 15, 2024</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <Calendar className="h-5 w-5 text-blue-600" />
              <div>
                <p className="font-medium">Equipment Inspection</p>
                <p className="text-sm text-muted-foreground">Due: February 20, 2024</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
              <Calendar className="h-5 w-5 text-green-600" />
              <div>
                <p className="font-medium">Safety Protocol Review</p>
                <p className="text-sm text-muted-foreground">Due: March 1, 2024</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
