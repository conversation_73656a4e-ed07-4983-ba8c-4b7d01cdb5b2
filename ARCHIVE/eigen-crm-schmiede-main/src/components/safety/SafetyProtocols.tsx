
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Shield, 
  CheckCircle, 
  Clock,
  AlertTriangle,
  FileText,
  Download,
  Eye
} from 'lucide-react';

interface SafetyProtocol {
  id: string;
  title: string;
  description: string;
  category: string;
  version: string;
  lastUpdated: string;
  status: 'active' | 'draft' | 'archived';
  compliance: number;
  applicableRoles: string[];
}

const mockProtocols: SafetyProtocol[] = [
  {
    id: '1',
    title: 'Personal Protective Equipment (PPE) Requirements',
    description: 'Mandatory PPE requirements for all construction site personnel',
    category: 'Personal Safety',
    version: '2.1',
    lastUpdated: '2024-01-10',
    status: 'active',
    compliance: 98,
    applicableRoles: ['All Personnel']
  },
  {
    id: '2',
    title: 'Heavy Equipment Operation Safety',
    description: 'Safety protocols for operating heavy construction equipment',
    category: 'Equipment Safety',
    version: '1.8',
    lastUpdated: '2023-12-15',
    status: 'active',
    compliance: 95,
    applicableRoles: ['Equipment Operators', 'Site Supervisors']
  },
  {
    id: '3',
    title: 'Emergency Evacuation Procedures',
    description: 'Step-by-step evacuation procedures for various emergency scenarios',
    category: 'Emergency Response',
    version: '3.0',
    lastUpdated: '2024-01-20',
    status: 'active',
    compliance: 100,
    applicableRoles: ['All Personnel']
  }
];

export const SafetyProtocols = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      case 'archived': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getComplianceColor = (compliance: number) => {
    if (compliance >= 95) return 'text-green-600';
    if (compliance >= 85) return 'text-yellow-600';
    return 'text-red-600';
  };

  const filteredProtocols = mockProtocols.filter(protocol => {
    const matchesSearch = protocol.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         protocol.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || protocol.category === categoryFilter;
    return matchesSearch && matchesCategory;
  });

  const categories = [...new Set(mockProtocols.map(p => p.category))];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Safety Protocols</h2>
          <p className="text-muted-foreground">Manage safety protocols and procedures</p>
        </div>
        <Button className="bg-gradient-to-r from-red-600 to-orange-600">
          <FileText className="h-4 w-4 mr-2" />
          Create Protocol
        </Button>
      </div>

      {/* Search and Filter */}
      <Card>
        <CardContent className="p-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search protocols..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Protocols Grid */}
      <div className="grid gap-6">
        {filteredProtocols.map((protocol) => (
          <Card key={protocol.id} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <Shield className="h-6 w-6 text-blue-600" />
                    <h3 className="text-xl font-semibold">{protocol.title}</h3>
                    <Badge className={getStatusColor(protocol.status)}>
                      {protocol.status}
                    </Badge>
                  </div>
                  <p className="text-muted-foreground mb-3">{protocol.description}</p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Category</p>
                      <p className="text-sm">{protocol.category}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Version</p>
                      <p className="text-sm">v{protocol.version}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Last Updated</p>
                      <p className="text-sm">{new Date(protocol.lastUpdated).toLocaleDateString()}</p>
                    </div>
                  </div>

                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <p className="text-sm font-medium">Compliance Rate</p>
                      <p className={`text-sm font-semibold ${getComplianceColor(protocol.compliance)}`}>
                        {protocol.compliance}%
                      </p>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          protocol.compliance >= 95 ? 'bg-green-500' : 
                          protocol.compliance >= 85 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${protocol.compliance}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="mb-4">
                    <p className="text-sm font-medium mb-2">Applicable Roles</p>
                    <div className="flex flex-wrap gap-2">
                      {protocol.applicableRoles.map((role, index) => (
                        <Badge key={index} variant="outline">{role}</Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Eye className="h-4 w-4 mr-1" />
                  View
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-1" />
                  Download
                </Button>
                <Button variant="outline" size="sm">
                  Edit
                </Button>
                <Button variant="outline" size="sm">
                  Track Compliance
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Compliance Alert */}
      <Card className="border-yellow-200 bg-yellow-50">
        <CardContent className="p-6">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-600" />
            <h3 className="font-semibold text-yellow-800">Protocol Review Required</h3>
          </div>
          <p className="text-sm text-yellow-700 mt-2">
            2 protocols are due for review this month. Schedule reviews to maintain compliance.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};
