
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { 
  AlertTriangle, 
  CheckCircle, 
  Calendar,
  Target,
  BookOpen
} from 'lucide-react';

interface SafetyStatsCardsProps {
  totalIncidents: number;
  openIncidents: number;
  daysWithoutIncident: number;
  complianceScore: number;
  safetyTrainings: number;
}

export const SafetyStatsCards = ({
  totalIncidents,
  openIncidents,
  daysWithoutIncident,
  complianceScore,
  safetyTrainings
}: SafetyStatsCardsProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Total Incidents</p>
              <p className="text-3xl font-bold text-slate-900">{totalIncidents}</p>
            </div>
            <div className="bg-gradient-to-r from-red-100 to-orange-100 p-3 rounded-xl">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Open Incidents</p>
              <p className="text-3xl font-bold text-slate-900">{openIncidents}</p>
            </div>
            <div className="bg-gradient-to-r from-yellow-100 to-orange-100 p-3 rounded-xl">
              <AlertTriangle className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Days Safe</p>
              <p className="text-3xl font-bold text-slate-900">{daysWithoutIncident}</p>
            </div>
            <div className="bg-gradient-to-r from-green-100 to-emerald-100 p-3 rounded-xl">
              <Calendar className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Compliance Score</p>
              <p className="text-3xl font-bold text-slate-900">{complianceScore}%</p>
            </div>
            <div className="bg-gradient-to-r from-blue-100 to-indigo-100 p-3 rounded-xl">
              <Target className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Safety Trainings</p>
              <p className="text-3xl font-bold text-slate-900">{safetyTrainings}</p>
            </div>
            <div className="bg-gradient-to-r from-purple-100 to-violet-100 p-3 rounded-xl">
              <BookOpen className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
