
import { Outlet } from "react-router-dom"
import { Sidebar } from "@/components/Sidebar"
import { UserMenu } from "@/components/auth/UserMenu"

export function Layout() {
  return (
    <div className="flex h-screen bg-background">
      <Sidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex h-14 items-center justify-between px-4">
            <div className="flex items-center space-x-2">
              <h1 className="text-lg font-semibold">Eigen CRM Schmiede</h1>
            </div>
            <UserMenu />
          </div>
        </header>
        <main className="flex-1 overflow-y-auto">
          <Outlet />
        </main>
      </div>
    </div>
  )
}
