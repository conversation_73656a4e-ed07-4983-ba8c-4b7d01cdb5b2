
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { CheckCircle, FileText, AlertCircle } from 'lucide-react';
import { LoadingSpinner } from '@/components/common/LoadingStates';
import { useLoadingStore } from '@/stores/loadingStore';
import type { FinanceMetrics } from '@/types/finance';

interface FinanceOverviewProps {
  metrics: FinanceMetrics | undefined;
}

export const FinanceOverview: React.FC<FinanceOverviewProps> = ({ metrics }) => {
  const { isLoading } = useLoadingStore();

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-slate-900">Cash Flow</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-slate-600">Income</span>
              <span className="text-2xl font-bold text-green-600">
                +${metrics ? (metrics.paidInvoices / 1000).toFixed(0) : '0'}K
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-slate-600">Expenses</span>
              <span className="text-2xl font-bold text-red-600">
                -${metrics ? (metrics.totalExpenses / 1000).toFixed(0) : '0'}K
              </span>
            </div>
            <hr className="border-slate-200" />
            <div className="flex justify-between items-center">
              <span className="font-semibold text-slate-700">Net Profit</span>
              <span className="text-2xl font-bold text-slate-900">
                +${metrics ? (metrics.netProfit / 1000).toFixed(0) : '0'}K
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-slate-900">Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {isLoading('invoices') ? (
              <LoadingSpinner size="sm" text="Loading activity..." />
            ) : (
              <>
                <div className="flex items-start gap-3">
                  <div className="bg-green-100 p-2 rounded-lg">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-slate-900">Payment Received</p>
                    <p className="text-xs text-slate-600">ABC Corporation - $250,000</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="bg-blue-100 p-2 rounded-lg">
                    <FileText className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-slate-900">Invoice Sent</p>
                    <p className="text-xs text-slate-600">City Development - $180,000</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="bg-red-100 p-2 rounded-lg">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-slate-900">Payment Overdue</p>
                    <p className="text-xs text-slate-600">Residential Group - $95,000</p>
                  </div>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
