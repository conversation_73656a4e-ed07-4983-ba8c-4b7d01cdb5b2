
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar } from 'lucide-react';
import { LoadingSpinner } from '@/components/common/LoadingStates';
import { useLoadingStore } from '@/stores/loadingStore';
import type { Expense } from '@/types/finance';

interface ExpensesListProps {
  expenses: Expense[] | undefined;
}

export const ExpensesList: React.FC<ExpensesListProps> = ({ expenses }) => {
  const { isLoading } = useLoadingStore();

  const getExpenseStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800 border-green-200';
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'rejected': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (isLoading('expenses')) {
    return <LoadingSpinner size="lg" text="Loading expenses..." />;
  }

  return (
    <div className="space-y-4">
      {expenses?.map((expense) => (
        <Card key={expense.id} className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 group">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <div className="flex items-center gap-3">
                  <h3 className="text-lg font-bold text-slate-900">{expense.description}</h3>
                  <Badge variant="outline" className={getExpenseStatusColor(expense.status)}>
                    {expense.status}
                  </Badge>
                </div>
                <div className="flex items-center gap-6 text-slate-600">
                  <span>{expense.category}</span>
                  <span>{expense.vendor}</span>
                  <span>{expense.project}</span>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>{expense.date}</span>
                  </div>
                </div>
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-slate-900">${(expense.amount / 1000).toFixed(1)}K</p>
                <Button variant="outline" className="mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  View Receipt
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
