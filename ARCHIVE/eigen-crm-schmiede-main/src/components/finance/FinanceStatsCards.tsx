
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown,
  CreditCard, 
  CheckCircle,
  Clock
} from 'lucide-react';
import type { FinanceMetrics, Invoice } from '@/types/finance';

interface FinanceStatsCardsProps {
  metrics: FinanceMetrics | undefined;
  invoices: Invoice[] | undefined;
}

export const FinanceStatsCards: React.FC<FinanceStatsCardsProps> = ({
  metrics,
  invoices
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Total Revenue</p>
              <p className="text-3xl font-bold text-slate-900">
                ${metrics ? (metrics.totalRevenue / 1000).toFixed(0) : '0'}K
              </p>
            </div>
            <div className="bg-gradient-to-r from-green-100 to-emerald-100 p-3 rounded-xl">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="mt-2 flex items-center gap-1">
            <TrendingUp className="h-4 w-4 text-green-600" />
            <span className="text-sm text-green-600">+12% from last month</span>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Paid Invoices</p>
              <p className="text-3xl font-bold text-slate-900">
                ${metrics ? (metrics.paidInvoices / 1000).toFixed(0) : '0'}K
              </p>
            </div>
            <div className="bg-gradient-to-r from-blue-100 to-indigo-100 p-3 rounded-xl">
              <CheckCircle className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <div className="mt-2">
            <Progress 
              value={metrics ? (metrics.paidInvoices / metrics.totalRevenue) * 100 : 0} 
              className="h-2" 
            />
          </div>
        </CardContent>
      </Card>

      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Pending</p>
              <p className="text-3xl font-bold text-slate-900">
                ${metrics ? (metrics.pendingInvoices / 1000).toFixed(0) : '0'}K
              </p>
            </div>
            <div className="bg-gradient-to-r from-yellow-100 to-orange-100 p-3 rounded-xl">
              <Clock className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
          <div className="mt-2">
            <span className="text-sm text-slate-600">
              {invoices ? invoices.filter(inv => inv.status === 'pending').length : 0} invoices
            </span>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Expenses</p>
              <p className="text-3xl font-bold text-slate-900">
                ${metrics ? (metrics.totalExpenses / 1000).toFixed(0) : '0'}K
              </p>
            </div>
            <div className="bg-gradient-to-r from-red-100 to-rose-100 p-3 rounded-xl">
              <CreditCard className="h-6 w-6 text-red-600" />
            </div>
          </div>
          <div className="mt-2 flex items-center gap-1">
            <TrendingDown className="h-4 w-4 text-red-600" />
            <span className="text-sm text-red-600">-5% from last month</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
