
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Brain, Search, Phone, TrendingUp, Users, Building, BookOpen, Zap, Bot } from 'lucide-react';

interface CommandSuggestionsProps {
  onCommandSelect: (command: string) => void;
}

export const CommandSuggestions: React.FC<CommandSuggestionsProps> = ({ onCommandSelect }) => {
  const commandCategories = [
    {
      title: 'Company Research',
      icon: Building,
      color: 'bg-blue-100 text-blue-800',
      commands: [
        'Research Apple Inc',
        'Find information on Microsoft',
        'Tell me about Tesla',
        'Analyze TechCorp competitive landscape'
      ]
    },
    {
      title: 'Lead Management',
      icon: Users,
      color: 'bg-green-100 text-green-800',
      commands: [
        'Show all high-value leads',
        'Qualify <PERSON>',
        'List prospects in tech',
        'Generate lead scoring report'
      ]
    },
    {
      title: 'Opportunities',
      icon: TrendingU<PERSON>,
      color: 'bg-purple-100 text-purple-800',
      commands: [
        'Show deals above 50k',
        'List opportunities this quarter',
        'Find deals in negotiation',
        'Predict deal closure probability'
      ]
    },
    {
      title: 'Phone & Contact',
      icon: Phone,
      color: 'bg-orange-100 text-orange-800',
      commands: [
        'Call Michael Chen',
        'Schedule call with Emily',
        'Send email to David Kim',
        'Create follow-up sequence'
      ]
    },
    {
      title: 'AI Instructions',
      icon: BookOpen,
      color: 'bg-indigo-100 text-indigo-800',
      commands: [
        'Update AI communication style',
        'Add new lead qualification rules',
        'Create custom command for research',
        'Export current instructions'
      ]
    },
    {
      title: 'Automation',
      icon: Zap,
      color: 'bg-yellow-100 text-yellow-800',
      commands: [
        'Start LinkedIn research automation',
        'Pause email sequence workflow',
        'Create web scraping task',
        'Schedule data entry automation'
      ]
    },
    {
      title: 'Agent Monitoring',
      icon: Bot,
      color: 'bg-pink-100 text-pink-800',
      commands: [
        'Show agent thought processes',
        'Monitor research agent activity',
        'View decision confidence levels',
        'Replay agent reasoning session'
      ]
    }
  ];

  return (
    <Card className="mb-4">
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <Brain className="w-5 h-5" />
          Suggested Commands
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {commandCategories.map((category) => (
          <div key={category.title} className="space-y-2">
            <div className="flex items-center gap-2">
              <category.icon className="w-4 h-4" />
              <Badge variant="outline" className={category.color}>
                {category.title}
              </Badge>
            </div>
            <div className="grid grid-cols-1 gap-1">
              {category.commands.map((command) => (
                <Button
                  key={command}
                  variant="ghost"
                  size="sm"
                  className="justify-start text-left h-auto py-2"
                  onClick={() => onCommandSelect(command)}
                >
                  {command}
                </Button>
              ))}
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};
