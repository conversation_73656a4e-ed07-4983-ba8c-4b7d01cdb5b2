
import React from 'react';
import { cn } from '@/lib/utils';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  text?: string;
}

export const Loading: React.FC<LoadingProps> = ({ 
  size = 'md', 
  className,
  text 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  return (
    <div className={cn('flex items-center justify-center', className)}>
      <div className="flex flex-col items-center gap-3">
        <div 
          className={cn(
            'animate-spin rounded-full border-2 border-sage-200 border-t-sage-500',
            sizeClasses[size]
          )}
        />
        {text && (
          <p className="text-sm text-sage-600 font-medium">{text}</p>
        )}
      </div>
    </div>
  );
};

export const PageLoading: React.FC<{ text?: string }> = ({ text = 'Loading...' }) => (
  <div className="flex items-center justify-center min-h-[400px]">
    <Loading size="lg" text={text} />
  </div>
);

export const CardLoading: React.FC = () => (
  <div className="animate-pulse">
    <div className="h-4 bg-sage-100 rounded w-3/4 mb-2"></div>
    <div className="h-3 bg-sage-50 rounded w-1/2"></div>
  </div>
);
