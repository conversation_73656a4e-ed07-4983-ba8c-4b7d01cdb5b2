
import React from 'react';

interface DynamicViewRendererProps {
  view?: any;
}

// Stub component to fix build error
export const DynamicViewRenderer: React.FC<DynamicViewRendererProps> = ({ view }) => {
  if (!view) {
    return null;
  }
  
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold">Dynamic View: {view.name || view.id}</h1>
      <p className="text-muted-foreground">This is a dynamic view placeholder.</p>
    </div>
  );
};
