
import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Truck, Package, MapPin, Clock, Plus, Search, Building } from 'lucide-react';
import { DatabaseService } from '@/services/DatabaseService';
import { useToast } from '@/hooks/use-toast';
import { useRealtimeUpdates } from '@/services/RealtimeService';

export const Logistics = () => {
  const [activeTab, setActiveTab] = useState('shipments');
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newShipment, setNewShipment] = useState({
    tracking_number: '',
    origin: '',
    destination: '',
    status: 'pending' as const,
    items: [{ name: '', quantity: 1, weight: '' }],
    estimated_delivery: new Date().toISOString().split('T')[0]
  });

  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Set up real-time updates
  useRealtimeUpdates(['shipments', 'suppliers']);

  // Fetch shipments
  const { data: shipmentsData, isLoading: shipmentsLoading } = useQuery({
    queryKey: ['shipments'],
    queryFn: DatabaseService.getShipments,
  });

  // Fetch suppliers
  const { data: suppliersData, isLoading: suppliersLoading } = useQuery({
    queryKey: ['suppliers'],
    queryFn: DatabaseService.getSuppliers,
  });

  // Create shipment mutation
  const createShipmentMutation = useMutation({
    mutationFn: (shipment: any) => DatabaseService.createShipment(shipment),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['shipments'] });
      setIsCreateDialogOpen(false);
      setNewShipment({
        tracking_number: '',
        origin: '',
        destination: '',
        status: 'pending',
        items: [{ name: '', quantity: 1, weight: '' }],
        estimated_delivery: new Date().toISOString().split('T')[0]
      });
      toast({
        title: "Shipment Created",
        description: "New shipment has been added to tracking.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to create shipment.",
        variant: "destructive",
      });
      console.error('Error creating shipment:', error);
    },
  });

  // Update shipment status mutation
  const updateShipmentMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: any }) => 
      DatabaseService.updateShipment(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['shipments'] });
      toast({
        title: "Shipment Updated",
        description: "Shipment status has been updated.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update shipment.",
        variant: "destructive",
      });
      console.error('Error updating shipment:', error);
    },
  });

  // Ensure data is arrays
  const shipments = Array.isArray(shipmentsData) ? shipmentsData : [];
  const suppliers = Array.isArray(suppliersData) ? suppliersData : [];

  const handleCreateShipment = () => {
    const shipmentData = {
      ...newShipment,
      tracking_number: newShipment.tracking_number || `TRK-${Date.now()}`
    };
    createShipmentMutation.mutate(shipmentData);
  };

  const handleStatusChange = (shipmentId: string, newStatus: string) => {
    updateShipmentMutation.mutate({ 
      id: shipmentId, 
      updates: { status: newStatus } 
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'in_transit': return 'bg-blue-100 text-blue-800';
      case 'delivered': return 'bg-green-100 text-green-800';
      case 'delayed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSupplierStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredShipments = shipments.filter(shipment =>
    shipment.tracking_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    shipment.origin?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    shipment.destination?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const stats = {
    totalShipments: shipments.length,
    inTransit: shipments.filter(s => s.status === 'in_transit').length,
    delivered: shipments.filter(s => s.status === 'delivered').length,
    delayed: shipments.filter(s => s.status === 'delayed').length,
    activeSuppliers: suppliers.filter(s => s.status === 'active').length
  };

  if (shipmentsLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-orange-50 to-yellow-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse">Loading logistics data...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-orange-50 to-yellow-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-slate-900">Logistics & Supply Chain</h1>
            <p className="text-slate-600 mt-2">Track shipments and manage suppliers</p>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-orange-600 hover:bg-orange-700">
                <Plus className="h-4 w-4 mr-2" />
                New Shipment
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Shipment</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="tracking">Tracking Number</Label>
                  <Input
                    id="tracking"
                    value={newShipment.tracking_number}
                    onChange={(e) => setNewShipment({ ...newShipment, tracking_number: e.target.value })}
                    placeholder="Auto-generated if empty"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="origin">Origin</Label>
                    <Input
                      id="origin"
                      value={newShipment.origin}
                      onChange={(e) => setNewShipment({ ...newShipment, origin: e.target.value })}
                      placeholder="Warehouse A"
                    />
                  </div>
                  <div>
                    <Label htmlFor="destination">Destination</Label>
                    <Input
                      id="destination"
                      value={newShipment.destination}
                      onChange={(e) => setNewShipment({ ...newShipment, destination: e.target.value })}
                      placeholder="Construction Site B"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="estimated_delivery">Estimated Delivery</Label>
                  <Input
                    id="estimated_delivery"
                    type="date"
                    value={newShipment.estimated_delivery}
                    onChange={(e) => setNewShipment({ ...newShipment, estimated_delivery: e.target.value })}
                  />
                </div>
                <div>
                  <Label>Items</Label>
                  <div className="space-y-2">
                    {newShipment.items.map((item, index) => (
                      <div key={index} className="flex gap-2">
                        <Input
                          placeholder="Item name"
                          value={item.name}
                          onChange={(e) => {
                            const newItems = [...newShipment.items];
                            newItems[index] = { ...item, name: e.target.value };
                            setNewShipment({ ...newShipment, items: newItems });
                          }}
                        />
                        <Input
                          type="number"
                          placeholder="Qty"
                          value={item.quantity}
                          onChange={(e) => {
                            const newItems = [...newShipment.items];
                            newItems[index] = { ...item, quantity: parseInt(e.target.value) || 1 };
                            setNewShipment({ ...newShipment, items: newItems });
                          }}
                          className="w-20"
                        />
                      </div>
                    ))}
                  </div>
                </div>
                <Button onClick={handleCreateShipment} className="w-full">
                  Create Shipment
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Total Shipments</p>
                  <p className="text-2xl font-bold text-slate-900">{stats.totalShipments}</p>
                </div>
                <Package className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">In Transit</p>
                  <p className="text-2xl font-bold text-blue-600">{stats.inTransit}</p>
                </div>
                <Truck className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Delivered</p>
                  <p className="text-2xl font-bold text-green-600">{stats.delivered}</p>
                </div>
                <MapPin className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Delayed</p>
                  <p className="text-2xl font-bold text-red-600">{stats.delayed}</p>
                </div>
                <Clock className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600">Active Suppliers</p>
                  <p className="text-2xl font-bold text-purple-600">{stats.activeSuppliers}</p>
                </div>
                <Building className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search */}
        <Card>
          <CardContent className="p-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
              <Input
                placeholder="Search shipments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* Shipments List */}
        <div className="space-y-4">
          {filteredShipments.map((shipment) => (
            <Card key={shipment.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Package className="h-5 w-5 text-orange-500" />
                    <div>
                      <CardTitle className="text-lg">{shipment.tracking_number}</CardTitle>
                      <p className="text-sm text-slate-600">{shipment.origin} → {shipment.destination}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={getStatusColor(shipment.status)}>
                      {shipment.status.replace('_', ' ')}
                    </Badge>
                    <span className="text-sm text-slate-500">{shipment.estimated_delivery}</span>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <p className="text-slate-600">Status</p>
                    <p className="font-medium capitalize">{shipment.status.replace('_', ' ')}</p>
                  </div>
                  <div>
                    <p className="text-slate-600">Estimated Delivery</p>
                    <p className="font-medium">{shipment.estimated_delivery}</p>
                  </div>
                  <div>
                    <p className="text-slate-600">Items</p>
                    <p className="font-medium">{shipment.items?.length || 0} items</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Suppliers Section */}
        {suppliers.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Active Suppliers</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {suppliers.filter(supplier => supplier.status === 'active').map((supplier) => (
                  <Card key={supplier.id} className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">{supplier.name}</h4>
                        <p className="text-sm text-slate-600">{supplier.contact_email}</p>
                      </div>
                      <Badge className={getSupplierStatusColor(supplier.status)}>
                        {supplier.status}
                      </Badge>
                    </div>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};
