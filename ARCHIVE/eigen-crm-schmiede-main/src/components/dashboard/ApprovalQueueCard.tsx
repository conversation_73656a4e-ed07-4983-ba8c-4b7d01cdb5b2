
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Clock, Check, X, AlertTriangle, Info, ArrowRight } from 'lucide-react';
import { useAIStore } from '@/stores/aiStore';
import { AutonomyService } from '@/services/AutonomyService';
import { useToast } from '@/hooks/use-toast';

export const ApprovalQueueCard: React.FC = () => {
  const { approvalRequests, agents } = useAIStore();
  const { toast } = useToast();

  const pendingRequests = approvalRequests.filter(req => req.status === 'pending');

  const handleApprove = async (requestId: string) => {
    const success = await AutonomyService.approveRequest(requestId, 'user');
    if (success) {
      toast({
        title: "Action Approved",
        description: "The agent action has been approved and will proceed.",
      });
    }
  };

  const handleReject = async (requestId: string) => {
    const success = await AutonomyService.rejectRequest(requestId, 'user');
    if (success) {
      toast({
        title: "Action Rejected",
        description: "The agent action has been rejected and will not proceed.",
        variant: "destructive"
      });
    }
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'bg-green-500';
      case 'medium': return 'bg-yellow-500';
      case 'high': return 'bg-orange-500';
      case 'critical': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  if (pendingRequests.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Check className="w-5 h-5 text-green-500" />
            Approvals
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <p className="text-sm text-muted-foreground">No pending approvals</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const nextRequest = pendingRequests[0];
  const agent = agents.find(a => a.id === nextRequest.agentId);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Clock className="w-5 h-5 text-orange-500" />
            Pending Approval
          </div>
          {pendingRequests.length > 1 && (
            <Badge variant="outline">+{pendingRequests.length - 1} more</Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <h4 className="font-medium">{nextRequest.actionName}</h4>
                <Badge className={`${getRiskColor(nextRequest.riskLevel)} text-white`}>
                  {nextRequest.riskLevel}
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                {agent?.name || 'Unknown'} • {nextRequest.actionType}
              </p>
              <p className="text-sm">{nextRequest.description}</p>
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              size="sm"
              onClick={() => handleApprove(nextRequest.id)}
              className="flex-1"
            >
              <Check className="w-3 h-3 mr-1" />
              Approve
            </Button>
            <Button
              size="sm"
              variant="destructive"
              onClick={() => handleReject(nextRequest.id)}
              className="flex-1"
            >
              <X className="w-3 h-3 mr-1" />
              Reject
            </Button>
          </div>

          {pendingRequests.length > 1 && (
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={() => window.location.hash = '#approvals'}
            >
              View All ({pendingRequests.length})
              <ArrowRight className="w-3 h-3 ml-1" />
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
