
import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { TrendingUp, TrendingDown, DollarSign, Users, Clock, Target } from 'lucide-react'

export const DashboardMetrics = () => {
  const metrics = [
    {
      title: 'Project Completion Rate',
      value: '87%',
      trend: 'up',
      change: '+5%',
      description: 'Projects completed on time',
      icon: Target
    },
    {
      title: 'Average Project Duration',
      value: '4.2 months',
      trend: 'down',
      change: '-0.3 months',
      description: 'Time to project completion',
      icon: Clock
    },
    {
      title: 'Team Utilization',
      value: '92%',
      trend: 'up',
      change: '+3%',
      description: 'Workforce efficiency',
      icon: Users
    },
    {
      title: 'Revenue per Project',
      value: '$42k',
      trend: 'up',
      change: '+8%',
      description: 'Average project value',
      icon: DollarSign
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {metrics.map((metric) => {
        const IconComponent = metric.icon
        const TrendIcon = metric.trend === 'up' ? TrendingUp : TrendingDown
        const trendColor = metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
        
        return (
          <Card key={metric.title} className="hover:shadow-lg transition-shadow duration-300">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {metric.title}
                </CardTitle>
                <IconComponent className="h-5 w-5 text-blue-600" />
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-2">
                <div className="text-2xl font-bold">{metric.value}</div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className={`${trendColor} border-current`}>
                    <TrendIcon className="h-3 w-3 mr-1" />
                    {metric.change}
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground">{metric.description}</p>
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
