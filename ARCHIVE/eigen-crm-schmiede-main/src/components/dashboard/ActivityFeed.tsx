
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Activity, 
  Bot, 
  Building, 
  Users, 
  FileText, 
  Shield,
  CheckCircle,
  AlertCircle,
  Clock,
  Zap,
  TrendingUp,
  DollarSign,
  Target
} from 'lucide-react';

interface ActivityItem {
  id: string;
  type: 'agent' | 'system' | 'user' | 'security' | 'revenue' | 'automation';
  title: string;
  description: string;
  timestamp: Date;
  status: 'success' | 'warning' | 'info' | 'error';
  icon: any;
  value?: string;
}

export const ActivityFeed: React.FC = () => {
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [isLive, setIsLive] = useState(true);

  const generateActivity = (): ActivityItem => {
    const activityTypes = [
      {
        type: 'agent' as const,
        title: 'AI Agent Completed Research',
        description: 'Market analysis for Downtown Office Complex project finished',
        status: 'success' as const,
        icon: Bot,
        value: '$2.4M opportunity identified'
      },
      {
        type: 'revenue' as const,
        title: 'Deal Progress Updated',
        description: 'Residential Tower project moved to negotiation stage',
        status: 'success' as const,
        icon: DollarSign,
        value: '+$890K pipeline'
      },
      {
        type: 'automation' as const,
        title: 'Workflow Automation Triggered',
        description: 'Lead qualification process initiated for 5 new prospects',
        status: 'info' as const,
        icon: Zap,
        value: '5 leads processed'
      },
      {
        type: 'system' as const,
        title: 'Safety Inspection Scheduled',
        description: 'Automated safety check scheduled for Highway Bridge Project',
        status: 'info' as const,
        icon: Shield
      },
      {
        type: 'agent' as const,
        title: 'Competitive Intelligence Update',
        description: 'New competitor analysis available for Metro Construction Ltd',
        status: 'warning' as const,
        icon: TrendingUp,
        value: 'Risk level: Medium'
      },
      {
        type: 'user' as const,
        title: 'New Project Created',
        description: 'Green Energy Facility project added to portfolio',
        status: 'success' as const,
        icon: Building,
        value: '$1.2M estimated value'
      }
    ];

    const randomActivity = activityTypes[Math.floor(Math.random() * activityTypes.length)];
    
    return {
      id: Date.now().toString() + Math.random(),
      ...randomActivity,
      timestamp: new Date()
    };
  };

  useEffect(() => {
    // Initialize with some activities
    const initialActivities = Array.from({ length: 6 }, (_, i) => ({
      ...generateActivity(),
      timestamp: new Date(Date.now() - (i + 1) * 15 * 60 * 1000) // Spread activities over time
    }));
    setActivities(initialActivities);

    // Simulate real-time updates
    if (isLive) {
      const interval = setInterval(() => {
        if (Math.random() < 0.3) { // 30% chance every 10 seconds
          const newActivity = generateActivity();
          setActivities(prev => [newActivity, ...prev.slice(0, 9)]); // Keep only 10 most recent
        }
      }, 10000);

      return () => clearInterval(interval);
    }
  }, [isLive]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'error':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-3 h-3" />;
      case 'warning':
        return <AlertCircle className="w-3 h-3" />;
      case 'error':
        return <AlertCircle className="w-3 h-3" />;
      default:
        return <Clock className="w-3 h-3" />;
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMins = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));

    if (diffInMins < 1) {
      return 'Just now';
    } else if (diffInMins < 60) {
      return `${diffInMins}m ago`;
    } else {
      return `${diffInHours}h ago`;
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5" />
            Recent Activity
          </CardTitle>
          <div className="flex items-center gap-2">
            <div className={`flex items-center gap-1 ${isLive ? 'text-green-600' : 'text-gray-500'}`}>
              <div className={`w-2 h-2 rounded-full ${isLive ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`} />
              <span className="text-xs font-medium">{isLive ? 'Live' : 'Paused'}</span>
            </div>
            <Button
              size="sm"
              variant="outline"
              onClick={() => setIsLive(!isLive)}
              className="text-xs"
            >
              {isLive ? 'Pause' : 'Resume'}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {activities.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Activity className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>No recent activity</p>
            </div>
          ) : (
            activities.map((activity) => {
              const IconComponent = activity.icon;
              return (
                <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg border hover:bg-muted/50 transition-all duration-200 animate-fade-in">
                  <div className="p-2 rounded-lg bg-muted">
                    <IconComponent className="w-4 h-4 text-muted-foreground" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <p className="font-medium text-sm truncate">{activity.title}</p>
                      <Badge className={`${getStatusColor(activity.status)} text-xs`}>
                        {getStatusIcon(activity.status)}
                        <span className="ml-1 capitalize">{activity.status}</span>
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground mb-1">{activity.description}</p>
                    {activity.value && (
                      <p className="text-xs font-medium text-blue-600 mb-1">{activity.value}</p>
                    )}
                    <p className="text-xs text-muted-foreground">{formatTimeAgo(activity.timestamp)}</p>
                  </div>
                </div>
              );
            })
          )}
        </div>
        {activities.length > 0 && (
          <div className="mt-4 pt-3 border-t">
            <Button variant="outline" size="sm" className="w-full">
              View All Activity
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
