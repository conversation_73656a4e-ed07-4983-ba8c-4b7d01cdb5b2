
import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { 
  CheckCircle, 
  FileText, 
  Users, 
  AlertTriangle, 
  Calendar,
  Clock
} from 'lucide-react'

export const RecentActivity = () => {
  const activities = [
    {
      type: 'task_completed',
      user: '<PERSON>',
      action: 'completed task',
      target: 'Foundation inspection for Building A',
      time: '2 hours ago',
      icon: CheckCircle,
      color: 'text-green-600'
    },
    {
      type: 'document_uploaded',
      user: '<PERSON>',
      action: 'uploaded document',
      target: 'Safety compliance report',
      time: '4 hours ago',
      icon: FileText,
      color: 'text-blue-600'
    },
    {
      type: 'team_added',
      user: '<PERSON>',
      action: 'added team member',
      target: 'Shopping Mall Renovation project',
      time: '1 day ago',
      icon: Users,
      color: 'text-purple-600'
    },
    {
      type: 'issue_reported',
      user: '<PERSON>',
      action: 'reported safety issue',
      target: 'Downtown Office Complex - Floor 3',
      time: '1 day ago',
      icon: AlertTriangle,
      color: 'text-orange-600'
    },
    {
      type: 'meeting_scheduled',
      user: '<PERSON>',
      action: 'scheduled meeting',
      target: 'Weekly progress review',
      time: '2 days ago',
      icon: Calendar,
      color: 'text-indigo-600'
    }
  ]

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const getActivityBadge = (type: string) => {
    switch (type) {
      case 'task_completed': return 'bg-green-100 text-green-800'
      case 'document_uploaded': return 'bg-blue-100 text-blue-800'
      case 'team_added': return 'bg-purple-100 text-purple-800'
      case 'issue_reported': return 'bg-orange-100 text-orange-800'
      case 'meeting_scheduled': return 'bg-indigo-100 text-indigo-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Activity Timeline</h3>
        <Badge variant="outline" className="bg-blue-50 text-blue-700">
          <Clock className="w-3 h-3 mr-1" />
          Last 7 days
        </Badge>
      </div>
      
      <div className="space-y-3">
        {activities.map((activity, index) => {
          const IconComponent = activity.icon
          
          return (
            <Card key={index} className="hover:shadow-md transition-shadow duration-300 border-l-4 border-l-blue-500">
              <CardContent className="p-4">
                <div className="flex items-start gap-4">
                  <div className={`p-2 rounded-full bg-white shadow-sm ${activity.color}`}>
                    <IconComponent className="w-4 h-4" />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <Avatar className="h-6 w-6">
                            <AvatarFallback className="text-xs bg-gradient-to-r from-blue-500 to-purple-500 text-white">
                              {getInitials(activity.user)}
                            </AvatarFallback>
                          </Avatar>
                          <span className="font-medium text-sm">{activity.user}</span>
                          <span className="text-sm text-muted-foreground">{activity.action}</span>
                        </div>
                        
                        <p className="text-sm font-medium text-foreground">
                          {activity.target}
                        </p>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Badge className={getActivityBadge(activity.type)}>
                          {activity.type.replace('_', ' ')}
                        </Badge>
                        <span className="text-xs text-muted-foreground whitespace-nowrap">
                          {activity.time}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </div>
  )
}
