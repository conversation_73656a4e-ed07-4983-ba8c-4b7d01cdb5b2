
import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Brain, TrendingUp, <PERSON>ert<PERSON>riangle, Lightbulb, ChevronRight } from 'lucide-react';

interface RevenueInsight {
  id: string;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  category: string;
  actionable: boolean;
}

interface MarketTrend {
  id: string;
  title: string;
  description: string;
  trend: 'up' | 'down' | 'stable';
  confidence: number;
}

interface InsightsPanelProps {
  revenueInsights: RevenueInsight[];
  marketTrends: MarketTrend[];
}

export const InsightsPanel: React.FC<InsightsPanelProps> = ({ revenueInsights, marketTrends }) => {
  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'down': return <TrendingUp className="w-4 h-4 text-red-600 rotate-180" />;
      default: return <TrendingUp className="w-4 h-4 text-gray-600" />;
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5" />
            AI Revenue Insights
          </CardTitle>
          <CardDescription>
            Actionable insights generated by AI analysis
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {revenueInsights.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Lightbulb className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>AI is analyzing your data to generate insights...</p>
            </div>
          ) : (
            revenueInsights.map((insight) => (
              <div key={insight.id} className="p-4 rounded-lg border bg-card">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-sm">{insight.title}</h4>
                  <Badge className={getImpactColor(insight.impact)}>
                    {insight.impact}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground mb-3">
                  {insight.description}
                </p>
                <div className="flex items-center justify-between">
                  <Badge variant="outline" className="text-xs">
                    {insight.category}
                  </Badge>
                  {insight.actionable && (
                    <Button size="sm" variant="ghost" className="h-auto p-1">
                      <ChevronRight className="w-3 h-3" />
                    </Button>
                  )}
                </div>
              </div>
            ))
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Market Intelligence
          </CardTitle>
          <CardDescription>
            Real-time market trends and competitor analysis
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {marketTrends.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <AlertTriangle className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>Market intelligence monitoring is active...</p>
            </div>
          ) : (
            marketTrends.map((trend) => (
              <div key={trend.id} className="p-4 rounded-lg border bg-card">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-sm flex items-center gap-2">
                    {getTrendIcon(trend.trend)}
                    {trend.title}
                  </h4>
                  <Badge variant="outline">
                    {trend.confidence}% confidence
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  {trend.description}
                </p>
              </div>
            ))
          )}
        </CardContent>
      </Card>
    </div>
  );
};
