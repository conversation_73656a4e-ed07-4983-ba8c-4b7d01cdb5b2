
import React from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Brain, Zap } from 'lucide-react'
import { useLanguage } from '@/hooks/useLanguage'

interface DashboardHeaderProps {
  activeAgentsCount: number
  conversationsCount: number
  isLoadingInsights: boolean
  onRefreshInsights: () => void
}

export const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  activeAgentsCount,
  conversationsCount,
  isLoadingInsights,
  onRefreshInsights
}) => {
  const { t } = useLanguage();

  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-3xl font-bold text-foreground">{t('dashboard.title')}</h1>
        <p className="text-muted-foreground">{t('dashboard.subtitle')}</p>
      </div>
      <div className="flex items-center gap-2">
        <Badge variant="outline" className="bg-green-50 text-green-700">
          <Brain className="w-3 h-3 mr-1" />
          {activeAgentsCount} {t('dashboard.activeAgents')}
        </Badge>
        <Badge variant="outline" className="bg-blue-50 text-blue-700">
          <Zap className="w-3 h-3 mr-1" />
          {conversationsCount} {t('dashboard.conversations')}
        </Badge>
        <Button 
          variant="outline" 
          size="sm"
          onClick={onRefreshInsights}
          disabled={isLoadingInsights}
        >
          {isLoadingInsights ? t('dashboard.analyzing') : t('dashboard.refreshInsights')}
        </Button>
      </div>
    </div>
  )
}
