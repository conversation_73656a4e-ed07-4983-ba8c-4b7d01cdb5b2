
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Database, 
  Shield, 
  Activity, 
  Settings, 
  CheckCircle, 
  HardDrive,
  Wifi,
  Users,
  Clock
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

export const SystemStatus = () => {
  const navigate = useNavigate();

  const systemComponents = [
    {
      name: 'Database',
      status: 'operational',
      uptime: '99.9%',
      lastCheck: '2 minutes ago',
      icon: Database,
      color: 'text-green-600'
    },
    {
      name: 'Security',
      status: 'protected', 
      uptime: '100%',
      lastCheck: '1 minute ago',
      icon: Shield,
      color: 'text-blue-600'
    },
    {
      name: 'Storage',
      status: 'healthy',
      uptime: '98.7%',
      lastCheck: '5 minutes ago',
      icon: HardDrive,
      color: 'text-purple-600'
    },
    {
      name: 'Network',
      status: 'connected',
      uptime: '99.5%',
      lastCheck: '1 minute ago',
      icon: Wifi,
      color: 'text-indigo-600'
    }
  ];

  const getStatusBadge = (status: string) => {
    const statusMap: Record<string, string> = {
      'operational': 'bg-green-100 text-green-800',
      'protected': 'bg-blue-100 text-blue-800',
      'healthy': 'bg-purple-100 text-purple-800',
      'connected': 'bg-indigo-100 text-indigo-800'
    }
    return statusMap[status] || 'bg-gray-100 text-gray-800'
  }

  return (
    <Card className="border-white/20 bg-white/80 backdrop-blur-sm shadow-xl">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2 text-xl">
              <Activity className="w-6 h-6 text-green-600" />
              System Health Monitor
            </CardTitle>
            <CardDescription>
              Real-time monitoring of system components and performance
            </CardDescription>
          </div>
          <div className="flex items-center gap-3">
            <Badge className="bg-green-100 text-green-800">
              <CheckCircle className="w-4 h-4 mr-1" />
              All Systems Operational
            </Badge>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => navigate('/settings')}
            >
              <Settings className="w-4 h-4 mr-2" />
              Configure
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* System Components Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {systemComponents.map((component) => {
            const IconComponent = component.icon;
            return (
              <div key={component.name} className="flex items-center gap-4 p-4 rounded-lg border bg-white/50">
                <div className={`p-3 rounded-lg bg-white shadow-sm ${component.color}`}>
                  <IconComponent className="w-6 h-6" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold">{component.name}</h3>
                    <Badge className={getStatusBadge(component.status)}>
                      {component.status}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <span>Uptime: {component.uptime}</span>
                    <span className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {component.lastCheck}
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* System Performance Metrics */}
        <div className="space-y-4">
          <h4 className="font-semibold text-lg">Performance Metrics</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>CPU Usage</span>
                <span>23%</span>
              </div>
              <Progress value={23} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Memory Usage</span>
                <span>67%</span>
              </div>
              <Progress value={67} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Storage Usage</span>
                <span>45%</span>
              </div>
              <Progress value={45} className="h-2" />
            </div>
          </div>
        </div>

        {/* Active Users */}
        <div className="flex items-center justify-between p-4 rounded-lg bg-blue-50 border border-blue-200">
          <div className="flex items-center gap-3">
            <Users className="w-5 h-5 text-blue-600" />
            <div>
              <h4 className="font-semibold text-blue-900">Active Users</h4>
              <p className="text-sm text-blue-700">Currently online team members</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-blue-900">24</div>
            <div className="text-sm text-blue-700">online now</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
