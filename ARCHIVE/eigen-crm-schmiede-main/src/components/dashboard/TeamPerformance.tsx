
import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Star, Award, TrendingUp } from 'lucide-react'

export const TeamPerformance = () => {
  const teamMembers = [
    {
      name: '<PERSON>',
      role: 'Project Manager',
      performance: 95,
      tasksCompleted: 23,
      efficiency: 'excellent',
      projects: 3
    },
    {
      name: '<PERSON>',
      role: 'Site Supervisor',
      performance: 88,
      tasksCompleted: 19,
      efficiency: 'good',
      projects: 2
    },
    {
      name: '<PERSON>',
      role: 'Lead Engineer',
      performance: 92,
      tasksCompleted: 21,
      efficiency: 'excellent',
      projects: 4
    },
    {
      name: '<PERSON>',
      role: 'Safety Officer',
      performance: 89,
      tasksCompleted: 17,
      efficiency: 'good',
      projects: 2
    }
  ]

  const getPerformanceBadge = (efficiency: string) => {
    switch (efficiency) {
      case 'excellent': return 'bg-green-100 text-green-800'
      case 'good': return 'bg-blue-100 text-blue-800'
      case 'average': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Star className="w-4 h-4 text-yellow-500" />
              Top Performer
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">John Smith</div>
            <p className="text-sm text-muted-foreground">95% efficiency rating</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Award className="w-4 h-4 text-blue-500" />
              Tasks This Month
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">80</div>
            <p className="text-sm text-muted-foreground">Completed by team</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <TrendingUp className="w-4 h-4 text-green-500" />
              Team Efficiency
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">91%</div>
            <p className="text-sm text-muted-foreground">Average performance</p>
          </CardContent>
        </Card>
      </div>

      <div className="space-y-3">
        {teamMembers.map((member, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow duration-300">
            <CardContent className="p-4">
              <div className="flex items-center gap-4">
                <Avatar className="h-12 w-12">
                  <AvatarFallback className="bg-gradient-to-r from-blue-500 to-purple-500 text-white">
                    {getInitials(member.name)}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold">{member.name}</h3>
                      <p className="text-sm text-muted-foreground">{member.role}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold">{member.performance}%</div>
                      <div className="text-xs text-muted-foreground">Performance</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4 mt-3">
                    <Badge className={getPerformanceBadge(member.efficiency)}>
                      {member.efficiency}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {member.tasksCompleted} tasks completed
                    </span>
                    <span className="text-sm text-muted-foreground">
                      {member.projects} active projects
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
