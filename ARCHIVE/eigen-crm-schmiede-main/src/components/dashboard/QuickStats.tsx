
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Brain, Zap, Target, Activity } from 'lucide-react';

export const QuickStats: React.FC = () => {
  // Static, calmer values instead of animated ones
  const stats = {
    aiEfficiency: 92,
    activeProcesses: 8,
    conversionRate: 18.2,
    systemLoad: 'Normal'
  };

  const getLoadColor = (load: string) => {
    switch (load) {
      case 'Low': return 'bg-sage-100 text-sage-700 border-sage-200';
      case 'Normal': return 'bg-blue-50 text-blue-700 border-blue-200';
      case 'High': return 'bg-amber-50 text-amber-700 border-amber-200';
      default: return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  return (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
      <Card className="border-sage-200 shadow-sm">
        <CardContent className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-sage-100">
              <Brain className="w-5 h-5 text-sage-600" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground font-medium">AI Efficiency</p>
              <p className="text-2xl font-medium text-foreground">{stats.aiEfficiency}%</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="border-sage-200 shadow-sm">
        <CardContent className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-blue-50">
              <Zap className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground font-medium">Active Processes</p>
              <p className="text-2xl font-medium text-foreground">{stats.activeProcesses}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="border-sage-200 shadow-sm">
        <CardContent className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-sage-100">
              <Target className="w-5 h-5 text-sage-600" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground font-medium">Success Rate</p>
              <p className="text-2xl font-medium text-foreground">{stats.conversionRate}%</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="border-sage-200 shadow-sm">
        <CardContent className="p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-blue-50">
              <Activity className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground font-medium">System Status</p>
              <Badge variant="outline" className={getLoadColor(stats.systemLoad)}>
                {stats.systemLoad}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
