
import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Calendar, MapPin, Users } from 'lucide-react'

export const ProjectTimeline = () => {
  const projects = [
    {
      name: 'Downtown Office Complex',
      location: 'Downtown District',
      progress: 75,
      status: 'on-track',
      dueDate: '2024-08-15',
      teamSize: 12,
      phase: 'Construction'
    },
    {
      name: 'Residential Building A',
      location: 'North Side',
      progress: 45,
      status: 'delayed',
      dueDate: '2024-09-30',
      teamSize: 8,
      phase: 'Foundation'
    },
    {
      name: 'Shopping Mall Renovation',
      location: 'West End',
      progress: 90,
      status: 'ahead',
      dueDate: '2024-07-20',
      teamSize: 15,
      phase: 'Finishing'
    },
    {
      name: 'Infrastructure Project',
      location: 'East District',
      progress: 30,
      status: 'on-track',
      dueDate: '2024-12-01',
      teamSize: 20,
      phase: 'Planning'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'on-track': return 'bg-blue-100 text-blue-800'
      case 'ahead': return 'bg-green-100 text-green-800'
      case 'delayed': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500'
    if (progress >= 50) return 'bg-blue-500'
    return 'bg-yellow-500'
  }

  return (
    <div className="space-y-4">
      {projects.map((project, index) => (
        <Card key={index} className="hover:shadow-md transition-shadow duration-300">
          <CardHeader className="pb-4">
            <div className="flex items-start justify-between">
              <div className="space-y-1">
                <CardTitle className="text-lg">{project.name}</CardTitle>
                <CardDescription className="flex items-center gap-4">
                  <span className="flex items-center gap-1">
                    <MapPin className="w-4 h-4" />
                    {project.location}
                  </span>
                  <span className="flex items-center gap-1">
                    <Users className="w-4 h-4" />
                    {project.teamSize} team members
                  </span>
                  <span className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    Due {new Date(project.dueDate).toLocaleDateString()}
                  </span>
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Badge className={getStatusColor(project.status)}>
                  {project.status.replace('-', ' ')}
                </Badge>
                <Badge variant="outline">{project.phase}</Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="font-medium">Progress</span>
                <span className="text-muted-foreground">{project.progress}%</span>
              </div>
              <div className="relative">
                <Progress value={project.progress} className="h-2" />
                <div 
                  className={`absolute top-0 left-0 h-2 rounded-full transition-all duration-500 ${getProgressColor(project.progress)}`}
                  style={{ width: `${project.progress}%` }}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
