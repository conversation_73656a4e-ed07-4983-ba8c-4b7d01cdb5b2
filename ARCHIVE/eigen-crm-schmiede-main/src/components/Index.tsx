
import React, { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  LayoutDashboard, 
  Briefcase, 
  Users, 
  CheckSquare, 
  Calendar, 
  FileText,
  ArrowRight
} from 'lucide-react'

export const Index = () => {
  const navigate = useNavigate()

  useEffect(() => {
    // Auto-redirect to dashboard after 3 seconds
    const timer = setTimeout(() => {
      navigate('/dashboard')
    }, 3000)

    return () => clearTimeout(timer)
  }, [navigate])

  const features = [
    { name: 'Dashboard', icon: LayoutDashboard, href: '/dashboard', description: 'Overview of your construction projects' },
    { name: 'Projects', icon: Briefcase, href: '/projects', description: 'Manage construction projects' },
    { name: 'People', icon: Users, href: '/people', description: 'Team and workforce management' },
    { name: 'Tasks', icon: CheckSquare, href: '/tasks', description: 'Track project tasks and milestones' },
    { name: 'Calendar', icon: Calendar, href: '/calendar', description: 'Schedule and appointments' },
    { name: 'Documents', icon: FileText, href: '/documents', description: 'Project documents and files' },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
            <span className="text-white font-bold text-2xl">CRM</span>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Construction CRM
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            Streamlined project management for construction teams
          </p>
          <Button 
            onClick={() => navigate('/dashboard')}
            size="lg"
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
          >
            Get Started
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {features.map((feature) => (
            <Card 
              key={feature.name}
              className="hover:shadow-lg transition-shadow cursor-pointer"
              onClick={() => navigate(feature.href)}
            >
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <feature.icon className="w-6 h-6 text-blue-600" />
                  {feature.name}
                </CardTitle>
                <CardDescription>{feature.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <Button variant="outline" size="sm" className="w-full">
                  Open {feature.name}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Auto-redirect notice */}
        <div className="text-center">
          <p className="text-sm text-gray-500">
            Redirecting to dashboard in a few seconds...
          </p>
        </div>
      </div>
    </div>
  )
}
