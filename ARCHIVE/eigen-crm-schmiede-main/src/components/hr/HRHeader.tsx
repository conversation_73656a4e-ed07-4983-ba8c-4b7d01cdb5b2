
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Plus, Search, Filter } from 'lucide-react';

export const HRHeader = () => {
  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl p-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-slate-900 via-emerald-900 to-teal-900 bg-clip-text text-transparent">
            HR & Workforce Management
          </h1>
          <p className="text-slate-600 mt-2 text-lg">Comprehensive employee management and development platform</p>
        </div>
        <div className="flex gap-3">
          <Button className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white shadow-lg hover:shadow-xl transition-all duration-300">
            <Plus className="h-4 w-4 mr-2" />
            Quick Action
          </Button>
          <Button variant="outline" className="border-slate-200 hover:bg-slate-50">
            <Search className="h-4 w-4 mr-2" />
            Search
          </Button>
          <Button variant="outline" className="border-slate-200 hover:bg-slate-50">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
        </div>
      </div>
    </div>
  );
};
