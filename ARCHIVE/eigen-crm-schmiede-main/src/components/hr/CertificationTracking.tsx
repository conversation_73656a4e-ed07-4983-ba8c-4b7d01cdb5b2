
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { 
  Award, 
  AlertTriangle, 
  Calendar, 
  Plus, 
  Search,
  CheckCircle,
  Clock,
  XCircle
} from 'lucide-react';

interface Certification {
  id: string;
  name: string;
  category: string;
  description: string;
  validityPeriod: number; // months
  cost: number;
  required: boolean;
}

interface EmployeeCertification {
  id: string;
  employeeId: string;
  employeeName: string;
  certificationId: string;
  certificationName: string;
  dateObtained: string;
  expiryDate: string;
  status: 'valid' | 'expiring-soon' | 'expired' | 'in-progress';
  score?: number;
}

const mockCertifications: Certification[] = [
  {
    id: '1',
    name: 'OSHA 30',
    category: 'Safety',
    description: '30-hour OSHA safety training certification',
    validityPeriod: 36,
    cost: 250,
    required: true
  },
  {
    id: '2',
    name: 'First Aid/CPR',
    category: 'Safety',
    description: 'Basic first aid and CPR certification',
    validityPeriod: 24,
    cost: 150,
    required: true
  },
  {
    id: '3',
    name: 'Heavy Equipment Operator',
    category: 'Equipment',
    description: 'Certification for operating heavy construction equipment',
    validityPeriod: 60,
    cost: 500,
    required: false
  },
  {
    id: '4',
    name: 'Crane Operator',
    category: 'Equipment',
    description: 'Mobile crane operator certification',
    validityPeriod: 60,
    cost: 750,
    required: false
  }
];

const mockEmployeeCertifications: EmployeeCertification[] = [
  {
    id: '1',
    employeeId: '1',
    employeeName: 'John Smith',
    certificationId: '1',
    certificationName: 'OSHA 30',
    dateObtained: '2023-01-15',
    expiryDate: '2026-01-15',
    status: 'valid'
  },
  {
    id: '2',
    employeeId: '1',
    employeeName: 'John Smith',
    certificationId: '2',
    certificationName: 'First Aid/CPR',
    dateObtained: '2023-06-01',
    expiryDate: '2025-06-01',
    status: 'expiring-soon'
  },
  {
    id: '3',
    employeeId: '2',
    employeeName: 'Maria Garcia',
    certificationId: '3',
    certificationName: 'Heavy Equipment Operator',
    dateObtained: '2022-03-10',
    expiryDate: '2027-03-10',
    status: 'valid'
  },
  {
    id: '4',
    employeeId: '2',
    employeeName: 'Maria Garcia',
    certificationId: '4',
    certificationName: 'Crane Operator',
    dateObtained: '2021-08-15',
    expiryDate: '2024-02-15',
    status: 'expired'
  }
];

export const CertificationTracking = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'valid': return 'bg-green-100 text-green-800';
      case 'expiring-soon': return 'bg-yellow-100 text-yellow-800';
      case 'expired': return 'bg-red-100 text-red-800';
      case 'in-progress': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'valid': return <CheckCircle className="h-4 w-4" />;
      case 'expiring-soon': return <Clock className="h-4 w-4" />;
      case 'expired': return <XCircle className="h-4 w-4" />;
      case 'in-progress': return <Clock className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const filteredCertifications = mockEmployeeCertifications.filter(cert => {
    const matchesSearch = 
      cert.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cert.certificationName.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });

  const validCerts = mockEmployeeCertifications.filter(c => c.status === 'valid').length;
  const expiringSoon = mockEmployeeCertifications.filter(c => c.status === 'expiring-soon').length;
  const expired = mockEmployeeCertifications.filter(c => c.status === 'expired').length;
  const complianceRate = ((validCerts + expiringSoon) / mockEmployeeCertifications.length) * 100;

  const categories = [...new Set(mockCertifications.map(cert => cert.category))];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Certification Tracking</h2>
          <p className="text-muted-foreground">Monitor employee certifications and compliance</p>
        </div>
        <Button className="bg-gradient-to-r from-emerald-600 to-teal-600">
          <Plus className="h-4 w-4 mr-2" />
          Add Certification
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Valid Certifications</p>
                <p className="text-3xl font-bold text-green-600">{validCerts}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Expiring Soon</p>
                <p className="text-3xl font-bold text-yellow-600">{expiringSoon}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Expired</p>
                <p className="text-3xl font-bold text-red-600">{expired}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Compliance Rate</p>
                <p className="text-3xl font-bold">{complianceRate.toFixed(0)}%</p>
              </div>
              <Award className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter */}
      <Card>
        <CardContent className="p-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search certifications..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Available Certifications */}
      <Card>
        <CardHeader>
          <CardTitle>Available Certifications</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            {mockCertifications.map((cert) => (
              <div key={cert.id} className="border rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="font-semibold">{cert.name}</h4>
                      <Badge variant="outline">{cert.category}</Badge>
                      {cert.required && (
                        <Badge variant="destructive">Required</Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">{cert.description}</p>
                    <div className="flex items-center gap-4 text-sm">
                      <span>Valid for: {cert.validityPeriod} months</span>
                      <span>Cost: ${cert.cost}</span>
                    </div>
                  </div>
                  <Button variant="outline" size="sm">
                    Assign
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Employee Certifications */}
      <Card>
        <CardHeader>
          <CardTitle>Employee Certifications</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredCertifications.map((cert) => (
              <div key={cert.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div>
                      <h4 className="font-semibold">{cert.employeeName}</h4>
                      <p className="text-sm text-muted-foreground">{cert.certificationName}</p>
                    </div>
                    <Badge className={getStatusColor(cert.status)}>
                      {getStatusIcon(cert.status)}
                      <span className="ml-1">{cert.status.replace('-', ' ')}</span>
                    </Badge>
                  </div>
                  
                  <div className="text-right text-sm">
                    <p>Obtained: {new Date(cert.dateObtained).toLocaleDateString()}</p>
                    <p className={cert.status === 'expired' ? 'text-red-600' : 'text-muted-foreground'}>
                      Expires: {new Date(cert.expiryDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Expiring Certifications Alert */}
      {expiringSoon > 0 && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              <h3 className="font-semibold text-yellow-800">Certifications Expiring Soon</h3>
            </div>
            <p className="text-sm text-yellow-700 mt-2">
              {expiringSoon} certification(s) will expire within the next 30 days. Schedule renewals to maintain compliance.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
