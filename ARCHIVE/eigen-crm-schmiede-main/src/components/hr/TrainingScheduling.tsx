
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { 
  Calendar as CalendarIcon, 
  Clock, 
  Users, 
  BookOpen,
  Plus,
  Search,
  MapPin,
  User
} from 'lucide-react';

interface TrainingSession {
  id: string;
  title: string;
  description: string;
  instructor: string;
  date: string;
  startTime: string;
  endTime: string;
  location: string;
  maxParticipants: number;
  enrolledParticipants: number;
  category: string;
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled';
  cost: number;
}

interface TrainingEnrollment {
  id: string;
  employeeId: string;
  employeeName: string;
  sessionId: string;
  sessionTitle: string;
  enrollmentDate: string;
  status: 'enrolled' | 'completed' | 'no-show' | 'cancelled';
  score?: number;
}

const mockTrainingSessions: TrainingSession[] = [
  {
    id: '1',
    title: 'Safety Protocol Advanced Training',
    description: 'Advanced safety protocols for construction sites',
    instructor: '<PERSON>',
    date: '2024-02-15',
    startTime: '09:00',
    endTime: '17:00',
    location: 'Training Center A',
    maxParticipants: 20,
    enrolledParticipants: 15,
    category: 'Safety',
    status: 'scheduled',
    cost: 300
  },
  {
    id: '2',
    title: 'Equipment Maintenance Workshop',
    description: 'Hands-on equipment maintenance training',
    instructor: 'Mike Wilson',
    date: '2024-02-20',
    startTime: '08:00',
    endTime: '16:00',
    location: 'Workshop B',
    maxParticipants: 12,
    enrolledParticipants: 10,
    category: 'Equipment',
    status: 'scheduled',
    cost: 250
  },
  {
    id: '3',
    title: 'Leadership Development',
    description: 'Leadership skills for supervisors and managers',
    instructor: 'Lisa Brown',
    date: '2024-02-25',
    startTime: '10:00',
    endTime: '15:00',
    location: 'Conference Room 1',
    maxParticipants: 15,
    enrolledParticipants: 8,
    category: 'Management',
    status: 'scheduled',
    cost: 400
  }
];

const mockEnrollments: TrainingEnrollment[] = [
  {
    id: '1',
    employeeId: '1',
    employeeName: 'John Smith',
    sessionId: '1',
    sessionTitle: 'Safety Protocol Advanced Training',
    enrollmentDate: '2024-01-20',
    status: 'enrolled'
  },
  {
    id: '2',
    employeeId: '2',
    employeeName: 'Maria Garcia',
    sessionId: '2',
    sessionTitle: 'Equipment Maintenance Workshop',
    enrollmentDate: '2024-01-22',
    status: 'enrolled'
  },
  {
    id: '3',
    employeeId: '3',
    employeeName: 'David Johnson',
    sessionId: '3',
    sessionTitle: 'Leadership Development',
    enrollmentDate: '2024-01-25',
    status: 'enrolled'
  }
];

export const TrainingScheduling = () => {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'in-progress': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredSessions = mockTrainingSessions.filter(session => {
    const matchesSearch = 
      session.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      session.instructor.toLowerCase().includes(searchTerm.toLowerCase()) ||
      session.category.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });

  const upcomingSessions = mockTrainingSessions.filter(s => s.status === 'scheduled').length;
  const totalEnrollments = mockEnrollments.length;
  const completionRate = (mockEnrollments.filter(e => e.status === 'completed').length / totalEnrollments) * 100;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Training Scheduling</h2>
          <p className="text-muted-foreground">Schedule and manage employee training sessions</p>
        </div>
        <Button className="bg-gradient-to-r from-emerald-600 to-teal-600">
          <Plus className="h-4 w-4 mr-2" />
          Schedule Training
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Upcoming Sessions</p>
                <p className="text-3xl font-bold">{upcomingSessions}</p>
              </div>
              <CalendarIcon className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Enrollments</p>
                <p className="text-3xl font-bold">{totalEnrollments}</p>
              </div>
              <Users className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Completion Rate</p>
                <p className="text-3xl font-bold">{completionRate.toFixed(0)}%</p>
              </div>
              <BookOpen className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Instructors</p>
                <p className="text-3xl font-bold">5</p>
              </div>
              <User className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Calendar */}
        <Card>
          <CardHeader>
            <CardTitle>Training Calendar</CardTitle>
          </CardHeader>
          <CardContent>
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={setSelectedDate}
              className="rounded-md border"
            />
          </CardContent>
        </Card>

        {/* Training Sessions */}
        <div className="lg:col-span-2 space-y-4">
          {/* Search */}
          <Card>
            <CardContent className="p-4">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search training sessions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </CardContent>
          </Card>

          {/* Sessions List */}
          <div className="space-y-4">
            {filteredSessions.map((session) => (
              <Card key={session.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold mb-2">{session.title}</h3>
                      <p className="text-sm text-muted-foreground mb-2">{session.description}</p>
                      <Badge className={getStatusColor(session.status)}>
                        {session.status}
                      </Badge>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">${session.cost}</p>
                      <p className="text-sm text-muted-foreground">per person</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div className="flex items-center gap-2 text-sm">
                      <CalendarIcon className="h-4 w-4 text-blue-600" />
                      <span>{new Date(session.date).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Clock className="h-4 w-4 text-green-600" />
                      <span>{session.startTime} - {session.endTime}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <MapPin className="h-4 w-4 text-purple-600" />
                      <span>{session.location}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <User className="h-4 w-4 text-orange-600" />
                      <span>{session.instructor}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        {session.enrolledParticipants}/{session.maxParticipants} enrolled
                      </span>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                      <Button size="sm">
                        Enroll Employee
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Enrollments */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Enrollments</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockEnrollments.slice(0, 5).map((enrollment) => (
              <div key={enrollment.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-semibold">{enrollment.employeeName}</h4>
                  <p className="text-sm text-muted-foreground">{enrollment.sessionTitle}</p>
                </div>
                <div className="text-right">
                  <Badge variant="outline">{enrollment.status}</Badge>
                  <p className="text-sm text-muted-foreground mt-1">
                    Enrolled: {new Date(enrollment.enrollmentDate).toLocaleDateString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
