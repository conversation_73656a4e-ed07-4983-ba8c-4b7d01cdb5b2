
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Users, 
  UserCheck, 
  TrendingUp,
  BookOpen,
  AlertTriangle
} from 'lucide-react';

interface HRStatsCardsProps {
  totalEmployees: number;
  activeEmployees: number;
  averagePerformance: number;
  upcomingTraining: number;
  expiringCertifications: number;
}

export const HRStatsCards = ({
  totalEmployees,
  activeEmployees,
  averagePerformance,
  upcomingTraining,
  expiringCertifications
}: HRStatsCardsProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Total Employees</p>
              <p className="text-3xl font-bold text-slate-900">{totalEmployees}</p>
            </div>
            <div className="bg-gradient-to-r from-blue-100 to-indigo-100 p-3 rounded-xl">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Active Workers</p>
              <p className="text-3xl font-bold text-slate-900">{activeEmployees}</p>
            </div>
            <div className="bg-gradient-to-r from-green-100 to-emerald-100 p-3 rounded-xl">
              <UserCheck className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Avg Performance</p>
              <p className="text-3xl font-bold text-slate-900">{averagePerformance}%</p>
            </div>
            <div className="bg-gradient-to-r from-purple-100 to-violet-100 p-3 rounded-xl">
              <TrendingUp className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Training Sessions</p>
              <p className="text-3xl font-bold text-slate-900">{upcomingTraining}</p>
            </div>
            <div className="bg-gradient-to-r from-emerald-100 to-teal-100 p-3 rounded-xl">
              <BookOpen className="h-6 w-6 text-emerald-600" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-white/80 backdrop-blur-sm border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Expiring Certs</p>
              <p className="text-3xl font-bold text-slate-900">{expiringCertifications}</p>
            </div>
            <div className="bg-gradient-to-r from-orange-100 to-red-100 p-3 rounded-xl">
              <AlertTriangle className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
