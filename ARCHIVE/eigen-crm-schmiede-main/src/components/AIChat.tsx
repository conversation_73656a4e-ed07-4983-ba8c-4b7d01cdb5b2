
import React, { useState, useRef, useEffect } from 'react'
import { Send, Bot, User, Loader2, Lightbulb } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useEnhancedAI } from '@/contexts/EnhancedAIContext'
import { useAIStore } from '@/stores/aiStore'
import { CommandSuggestions } from './ai/CommandSuggestions'

export const AIChat: React.FC = () => {
  const [message, setMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(true)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const { sendMessage } = useEnhancedAI()
  const { conversations } = useAIStore()

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [conversations])

  useEffect(() => {
    setShowSuggestions(conversations.length === 0)
  }, [conversations])

  const handleSend = async () => {
    if (!message.trim()) return
    
    setIsLoading(true)
    setShowSuggestions(false)
    try {
      await sendMessage(message)
      setMessage('')
    } catch (error) {
      console.error('Error sending message:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  const handleCommandSelect = (command: string) => {
    setMessage(command)
    setShowSuggestions(false)
  }

  const formatMessage = (text: string) => {
    // Convert markdown-style formatting to JSX
    const parts = text.split(/(\*\*.*?\*\*|\*.*?\*|`.*?`)/g)
    return parts.map((part, index) => {
      if (part.startsWith('**') && part.endsWith('**')) {
        return <strong key={index}>{part.slice(2, -2)}</strong>
      }
      if (part.startsWith('*') && part.endsWith('*')) {
        return <em key={index}>{part.slice(1, -1)}</em>
      }
      if (part.startsWith('`') && part.endsWith('`')) {
        return <code key={index} className="bg-muted px-1 rounded">{part.slice(1, -1)}</code>
      }
      return part
    })
  }

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {showSuggestions && (
          <CommandSuggestions onCommandSelect={handleCommandSelect} />
        )}

        {conversations.length === 0 && !showSuggestions ? (
          <div className="text-center text-muted-foreground mt-8">
            <Bot className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Start a conversation with your AI assistant</p>
            <div className="mt-4 flex items-center justify-center gap-2">
              <Lightbulb className="w-4 h-4" />
              <Button 
                variant="link" 
                size="sm"
                onClick={() => setShowSuggestions(true)}
              >
                Show command suggestions
              </Button>
            </div>
          </div>
        ) : (
          conversations.map((conversation) => (
            <div key={conversation.id} className="space-y-3">
              <Card className="p-3 ml-8 bg-primary text-primary-foreground">
                <div className="flex items-start gap-3">
                  <User className="w-4 h-4 mt-1 flex-shrink-0" />
                  <div className="flex-1">
                    <p className="text-sm">{conversation.userMessage}</p>
                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant="secondary" className="text-xs">
                        {new Date(conversation.timestamp).toLocaleTimeString()}
                      </Badge>
                      {conversation.context?.command?.intent && (
                        <Badge variant="outline" className="text-xs">
                          {conversation.context.command.intent.replace('_', ' ')}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </Card>
              
              {conversation.context?.processing && !conversation.aiResponse && (
                <Card className="p-3 mr-8 bg-muted">
                  <div className="flex items-start gap-3">
                    <Loader2 className="w-4 h-4 mt-1 flex-shrink-0 animate-spin text-primary" />
                    <div className="flex-1">
                      <p className="text-sm text-muted-foreground">
                        AI is analyzing your request...
                      </p>
                      {conversation.context?.command?.agent && (
                        <Badge variant="outline" className="text-xs mt-2">
                          Using {conversation.context.command.agent}
                        </Badge>
                      )}
                    </div>
                  </div>
                </Card>
              )}

              {conversation.aiResponse && (
                <Card className="p-3 mr-8 bg-muted">
                  <div className="flex items-start gap-3">
                    <Bot className="w-4 h-4 mt-1 flex-shrink-0 text-primary" />
                    <div className="flex-1">
                      <div className="text-sm prose prose-sm max-w-none">
                        {formatMessage(conversation.aiResponse)}
                      </div>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant="secondary" className="text-xs">
                          {new Date(conversation.timestamp).toLocaleTimeString()}
                        </Badge>
                        {conversation.context?.command?.confidence && (
                          <Badge variant="outline" className="text-xs">
                            {Math.round(conversation.context.command.confidence * 100)}% confidence
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </Card>
              )}
            </div>
          ))
        )}
        
        {isLoading && conversations.length > 0 && (
          <Card className="p-3 mr-8 bg-muted">
            <div className="flex items-start gap-3">
              <Loader2 className="w-4 h-4 mt-1 flex-shrink-0 animate-spin text-primary" />
              <p className="text-sm text-muted-foreground">AI is thinking...</p>
            </div>
          </Card>
        )}
        
        <div ref={messagesEndRef} />
      </div>
      
      <div className="p-4 border-t bg-background">
        <div className="flex gap-2">
          <Input
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask your AI assistant anything..."
            disabled={isLoading}
            className="flex-1"
          />
          <Button 
            onClick={handleSend} 
            disabled={!message.trim() || isLoading}
            size="icon"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
        
        {!showSuggestions && conversations.length > 0 && (
          <div className="mt-2 text-center">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setShowSuggestions(true)}
              className="text-xs"
            >
              <Lightbulb className="w-3 h-3 mr-1" />
              Show suggestions
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
