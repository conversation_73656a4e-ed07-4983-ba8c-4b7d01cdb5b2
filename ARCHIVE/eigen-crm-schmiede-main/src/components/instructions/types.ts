
export type InstructionCategory = 'agent_behavior' | 'automation' | 'communication' | 'workflow' | 'integration' | 'goals' | 'general' | 'company' | 'process' | 'commands';

export interface Instruction {
  id: string;
  title: string;
  content: string;
  description: string;
  category: InstructionCategory;
  tags: string[];
  version: number;
  isActive: boolean;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

export interface CategoryConfig {
  value: InstructionCategory | 'all';
  label: string;
  icon: any;
  color: string;
}
