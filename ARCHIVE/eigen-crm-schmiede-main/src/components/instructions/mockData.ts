
import { Instruction } from './types';

export const mockInstructions: Instruction[] = [
  {
    id: '1',
    title: 'Company Communication Style',
    content: 'Always maintain a professional yet friendly tone when communicating with prospects. Use active voice and focus on value propositions.',
    description: 'Defines the standard communication approach for all customer interactions',
    category: 'communication',
    tags: ['communication', 'tone', 'prospects'],
    version: 1,
    isActive: true,
    status: 'active',
    createdAt: '2024-01-15',
    updatedAt: '2024-01-15'
  },
  {
    id: '2',
    title: 'Lead Qualification Process',
    content: 'Qualify leads using BANT methodology: Budget, Authority, Need, Timeline. Always ask qualifying questions before scheduling demos.',
    description: 'Standard process for qualifying potential customers before demos',
    category: 'process',
    tags: ['qualification', 'BANT', 'demos'],
    version: 2,
    isActive: true,
    status: 'active',
    createdAt: '2024-01-10',
    updatedAt: '2024-01-20'
  },
  {
    id: '3',
    title: 'Safety Protocol Enforcement',
    content: 'Always prioritize safety in construction environments. Check for proper PPE and safety compliance before any work begins.',
    description: 'Critical safety instructions for construction site operations',
    category: 'agent_behavior',
    tags: ['safety', 'PPE', 'construction'],
    version: 1,
    isActive: true,
    status: 'active',
    createdAt: '2024-01-12',
    updatedAt: '2024-01-12'
  },
  {
    id: '4',
    title: 'Automated Report Generation',
    content: 'Generate daily progress reports automatically at 5 PM with project status, completed tasks, and next day priorities.',
    description: 'Automation rule for daily project reporting',
    category: 'automation',
    tags: ['reports', 'automation', 'daily'],
    version: 1,
    isActive: true,
    status: 'active',
    createdAt: '2024-01-14',
    updatedAt: '2024-01-14'
  },
  {
    id: '5',
    title: 'Project Goal Tracking',
    content: 'Monitor project milestones and alert when deadlines are at risk. Escalate to project manager when delays exceed 2 days.',
    description: 'Goal tracking and escalation procedures',
    category: 'goals',
    tags: ['goals', 'milestones', 'escalation'],
    version: 1,
    isActive: false,
    status: 'inactive',
    createdAt: '2024-01-16',
    updatedAt: '2024-01-16'
  }
];
