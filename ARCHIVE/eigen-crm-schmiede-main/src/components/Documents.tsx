
import React, { useState } from 'react';
import { DocumentsHeader } from './documents/DocumentsHeader';
import { DocumentsStatsCards } from './documents/DocumentsStatsCards';
import { DocumentsTabs } from './documents/DocumentsTabs';

export const Documents = () => {
  const [activeTab, setActiveTab] = useState('files');

  // Mock data for documents overview
  const totalDocuments = 247;
  const sharedDocuments = 89;
  const storageUsed = 2.4; // GB
  const recentUploads = 12;
  const pendingApprovals = 5;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        <DocumentsHeader />
        
        <DocumentsStatsCards
          totalDocuments={totalDocuments}
          sharedDocuments={sharedDocuments}
          storageUsed={storageUsed}
          recentUploads={recentUploads}
          pendingApprovals={pendingApprovals}
        />

        <DocumentsTabs activeTab={activeTab} onTabChange={setActiveTab} />
      </div>
    </div>
  );
};
