
import React from 'react';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Mail, Phone, MessageSquare, Calendar, Edit, Trash2 } from 'lucide-react';
import { Person } from '@/types/person';
import { useToast } from '@/hooks/use-toast';

interface PersonActionsProps {
  person: Person;
  onEdit?: (person: Person) => void;
  onDelete?: (personId: string) => void;
}

export const PersonActions: React.FC<PersonActionsProps> = ({
  person,
  onEdit,
  onDelete
}) => {
  const { toast } = useToast();

  const handleEmailClick = () => {
    window.open(`mailto:${person.email}`, '_blank');
    toast({
      title: "Email Client Opened",
      description: `Opening email to ${person.firstName} ${person.lastName}`,
    });
  };

  const handlePhoneClick = () => {
    if (person.phone) {
      navigator.clipboard.writeText(person.phone);
      toast({
        title: "Phone Number Copied",
        description: `${person.phone} copied to clipboard`,
      });
    }
  };

  const handleScheduleMeeting = () => {
    toast({
      title: "Meeting Scheduler",
      description: "Meeting scheduler would open here",
    });
  };

  const handleAIChat = () => {
    toast({
      title: "AI Chat",
      description: `Starting AI conversation about ${person.firstName} ${person.lastName}`,
    });
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm">
          <MoreHorizontal className="w-4 h-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuItem onClick={handleEmailClick}>
          <Mail className="w-4 h-4 mr-2" />
          Send Email
        </DropdownMenuItem>
        {person.phone && (
          <DropdownMenuItem onClick={handlePhoneClick}>
            <Phone className="w-4 h-4 mr-2" />
            Copy Phone
          </DropdownMenuItem>
        )}
        <DropdownMenuItem onClick={handleScheduleMeeting}>
          <Calendar className="w-4 h-4 mr-2" />
          Schedule Meeting
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleAIChat}>
          <MessageSquare className="w-4 h-4 mr-2" />
          AI Research
        </DropdownMenuItem>
        {onEdit && (
          <DropdownMenuItem onClick={() => onEdit(person)}>
            <Edit className="w-4 h-4 mr-2" />
            Edit Contact
          </DropdownMenuItem>
        )}
        {onDelete && (
          <DropdownMenuItem 
            onClick={() => onDelete(person.id)}
            className="text-red-600"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Delete
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
