
import React from 'react';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

interface PersonFormHeaderProps {
  isEdit: boolean;
  onCancel: () => void;
}

export const PersonFormHeader: React.FC<PersonFormHeaderProps> = ({ isEdit, onCancel }) => {
  return (
    <div className="flex items-center gap-4 mb-6">
      <Button variant="ghost" onClick={onCancel}>
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back
      </Button>
      <div>
        <h1 className="text-3xl font-bold">
          {isEdit ? 'Edit Contact' : 'Add Contact'}
        </h1>
        <p className="text-muted-foreground">
          {isEdit ? 'Update contact information' : 'Add a new contact to your network'}
        </p>
      </div>
    </div>
  );
};
