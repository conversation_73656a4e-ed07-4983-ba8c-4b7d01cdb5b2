
import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { UseFormRegister } from 'react-hook-form';
import { PersonFormData } from '@/types/person';

interface PersonAddressFieldsProps {
  register: UseFormRegister<PersonFormData>;
}

export const PersonAddressFields: React.FC<PersonAddressFieldsProps> = ({ register }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="md:col-span-2">
        <Label htmlFor="street">Street Address</Label>
        <Input
          id="street"
          {...register('street')}
        />
      </div>

      <div>
        <Label htmlFor="city">City</Label>
        <Input
          id="city"
          {...register('city')}
        />
      </div>

      <div>
        <Label htmlFor="state">State</Label>
        <Input
          id="state"
          {...register('state')}
        />
      </div>

      <div>
        <Label htmlFor="zipCode">ZIP Code</Label>
        <Input
          id="zipCode"
          {...register('zipCode')}
        />
      </div>

      <div>
        <Label htmlFor="country">Country</Label>
        <Input
          id="country"
          {...register('country')}
        />
      </div>
    </div>
  );
};
