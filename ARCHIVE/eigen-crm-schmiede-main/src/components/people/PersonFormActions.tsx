
import React from 'react';
import { Button } from '@/components/ui/button';

interface PersonFormActionsProps {
  isEdit: boolean;
  isSubmitting: boolean;
  onCancel: () => void;
}

export const PersonFormActions: React.FC<PersonFormActionsProps> = ({
  isEdit,
  isSubmitting,
  onCancel
}) => {
  return (
    <div className="flex gap-4 pt-6">
      <Button
        type="submit"
        disabled={isSubmitting}
        className="bg-gradient-to-r from-blue-600 to-indigo-600"
      >
        {isSubmitting ? 'Saving...' : (isEdit ? 'Update Contact' : 'Add Contact')}
      </Button>
      <Button
        type="button"
        variant="outline"
        onClick={onCancel}
        disabled={isSubmitting}
      >
        Cancel
      </Button>
    </div>
  );
};
