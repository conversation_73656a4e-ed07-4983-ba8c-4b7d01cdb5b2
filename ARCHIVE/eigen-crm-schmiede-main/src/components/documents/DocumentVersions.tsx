
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  Download,
  Eye,
  GitBranch,
  Clock,
  User
} from 'lucide-react';

interface DocumentVersion {
  id: string;
  fileName: string;
  version: string;
  modifier: string;
  modifiedDate: string;
  size: string;
  changes: string;
  isCurrent: boolean;
}

const mockVersions: DocumentVersion[] = [
  {
    id: '1',
    fileName: 'Project_Proposal.docx',
    version: '3.2',
    modifier: '<PERSON>',
    modifiedDate: '2024-01-20T10:30:00',
    size: '1.2 MB',
    changes: 'Updated budget section and timeline',
    isCurrent: true
  },
  {
    id: '2',
    fileName: 'Project_Proposal.docx',
    version: '3.1',
    modifier: '<PERSON>',
    modifiedDate: '2024-01-19T14:15:00',
    size: '1.1 MB',
    changes: 'Added risk assessment section',
    isCurrent: false
  },
  {
    id: '3',
    fileName: 'Project_Proposal.docx',
    version: '3.0',
    modifier: '<PERSON>',
    modifiedDate: '2024-01-18T09:45:00',
    size: '1.0 MB',
    changes: 'Initial draft with project scope',
    isCurrent: false
  }
];

export const DocumentVersions = () => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold">Document Version Control</h2>
        <p className="text-muted-foreground">Track changes and manage document versions</p>
      </div>

      {/* Version History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GitBranch className="h-5 w-5" />
            Version History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockVersions.map((version, index) => (
              <div key={version.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <FileText className="h-8 w-8 text-blue-600" />
                    {index < mockVersions.length - 1 && (
                      <div className="absolute top-8 left-4 w-px h-8 bg-gray-300"></div>
                    )}
                  </div>
                  
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-semibold">{version.fileName}</h4>
                      <Badge variant={version.isCurrent ? "default" : "secondary"}>
                        v{version.version}
                      </Badge>
                      {version.isCurrent && (
                        <Badge className="bg-green-100 text-green-800">Current</Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {version.modifier}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {new Date(version.modifiedDate).toLocaleString()}
                      </div>
                      <span>{version.size}</span>
                    </div>
                    
                    <p className="text-sm text-gray-600 mt-1">{version.changes}</p>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-1" />
                    Preview
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-1" />
                    Download
                  </Button>
                  {!version.isCurrent && (
                    <Button variant="outline" size="sm">
                      Restore
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Version Comparison */}
      <Card>
        <CardHeader>
          <CardTitle>Compare Versions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Compare from:</label>
              <select className="w-full mt-1 p-2 border rounded-md">
                <option>v3.1 - Sarah Johnson</option>
                <option>v3.0 - Mike Davis</option>
              </select>
            </div>
            <div>
              <label className="text-sm font-medium">Compare to:</label>
              <select className="w-full mt-1 p-2 border rounded-md">
                <option>v3.2 - John Smith (Current)</option>
                <option>v3.1 - Sarah Johnson</option>
              </select>
            </div>
          </div>
          <Button className="mt-4">
            <GitBranch className="h-4 w-4 mr-2" />
            Compare Versions
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};
