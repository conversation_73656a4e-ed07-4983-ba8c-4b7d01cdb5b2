
import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  File, 
  Folder,
  Download,
  Share2,
  Edit,
  Trash2,
  Eye,
  MoreHorizontal
} from 'lucide-react';

interface Document {
  id: string;
  name: string;
  type: 'file' | 'folder';
  size?: string;
  modified: string;
  owner: string;
  shared: boolean;
  fileType?: string;
}

const mockDocuments: Document[] = [
  {
    id: '1',
    name: 'Project Specifications',
    type: 'folder',
    modified: '2024-01-20',
    owner: '<PERSON>',
    shared: true
  },
  {
    id: '2',
    name: 'Safety_Manual_v2.pdf',
    type: 'file',
    size: '2.4 MB',
    modified: '2024-01-19',
    owner: '<PERSON>',
    shared: false,
    fileType: 'pdf'
  },
  {
    id: '3',
    name: 'Budget_Report_Q1.xlsx',
    type: 'file',
    size: '890 KB',
    modified: '2024-01-18',
    owner: '<PERSON>',
    shared: true,
    fileType: 'excel'
  },
  {
    id: '4',
    name: 'Meeting Notes',
    type: 'folder',
    modified: '2024-01-17',
    owner: 'Emma Wilson',
    shared: false
  }
];

export const FileManager = () => {
  const [searchTerm, setSearchTerm] = useState('');

  const getFileIcon = (document: Document) => {
    if (document.type === 'folder') {
      return <Folder className="h-8 w-8 text-blue-600" />;
    }
    return <File className="h-8 w-8 text-gray-600" />;
  };

  const getFileTypeColor = (fileType?: string) => {
    switch (fileType) {
      case 'pdf': return 'bg-red-100 text-red-800';
      case 'excel': return 'bg-green-100 text-green-800';
      case 'word': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredDocuments = mockDocuments.filter(doc =>
    doc.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">File Manager</h2>
          <p className="text-muted-foreground">Browse and manage your documents</p>
        </div>
        <Button className="bg-gradient-to-r from-blue-600 to-indigo-600">
          <File className="h-4 w-4 mr-2" />
          Upload Files
        </Button>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-6">
          <Input
            placeholder="Search documents..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </CardContent>
      </Card>

      {/* File List */}
      <div className="grid gap-4">
        {filteredDocuments.map((document) => (
          <Card key={document.id} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  {getFileIcon(document)}
                  <div>
                    <h4 className="font-semibold">{document.name}</h4>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <span>Modified: {new Date(document.modified).toLocaleDateString()}</span>
                      <span>•</span>
                      <span>Owner: {document.owner}</span>
                      {document.size && (
                        <>
                          <span>•</span>
                          <span>{document.size}</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {document.fileType && (
                    <Badge className={getFileTypeColor(document.fileType)}>
                      {document.fileType.toUpperCase()}
                    </Badge>
                  )}
                  {document.shared && (
                    <Badge className="bg-green-100 text-green-800">
                      <Share2 className="h-3 w-3 mr-1" />
                      Shared
                    </Badge>
                  )}
                  
                  <div className="flex gap-1">
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Share2 className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
