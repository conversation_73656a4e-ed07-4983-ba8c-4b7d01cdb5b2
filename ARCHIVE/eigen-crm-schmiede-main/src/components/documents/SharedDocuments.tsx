
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  Share2, 
  Users,
  Eye,
  Download,
  Settings,
  Link,
  Mail
} from 'lucide-react';

interface SharedDocument {
  id: string;
  name: string;
  sharedWith: string[];
  permissions: 'view' | 'edit' | 'admin';
  shareType: 'internal' | 'external' | 'public';
  sharedDate: string;
  expiryDate?: string;
  accessCount: number;
}

const mockSharedDocs: SharedDocument[] = [
  {
    id: '1',
    name: 'Q1_Financial_Report.pdf',
    sharedWith: ['<PERSON>', '<PERSON>', '<PERSON>'],
    permissions: 'view',
    shareType: 'internal',
    sharedDate: '2024-01-20',
    accessCount: 15
  },
  {
    id: '2',
    name: 'Project_Timeline.xlsx',
    sharedWith: ['Client Team', 'External Consultant'],
    permissions: 'edit',
    shareType: 'external',
    sharedDate: '2024-01-19',
    expiryDate: '2024-02-19',
    accessCount: 8
  },
  {
    id: '3',
    name: 'Company_Presentation.pptx',
    sharedWith: ['Public Link'],
    permissions: 'view',
    shareType: 'public',
    sharedDate: '2024-01-18',
    accessCount: 42
  }
];

export const SharedDocuments = () => {
  const getShareTypeColor = (type: string) => {
    switch (type) {
      case 'internal': return 'bg-blue-100 text-blue-800';
      case 'external': return 'bg-orange-100 text-orange-800';
      case 'public': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPermissionColor = (permission: string) => {
    switch (permission) {
      case 'view': return 'bg-gray-100 text-gray-800';
      case 'edit': return 'bg-yellow-100 text-yellow-800';
      case 'admin': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Shared Documents</h2>
          <p className="text-muted-foreground">Manage document sharing and permissions</p>
        </div>
        <Button className="bg-gradient-to-r from-blue-600 to-indigo-600">
          <Share2 className="h-4 w-4 mr-2" />
          Share Document
        </Button>
      </div>

      {/* Shared Documents List */}
      <div className="space-y-4">
        {mockSharedDocs.map((doc) => (
          <Card key={doc.id} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <Share2 className="h-5 w-5 text-blue-600" />
                    <h3 className="text-lg font-semibold">{doc.name}</h3>
                    <Badge className={getShareTypeColor(doc.shareType)}>
                      {doc.shareType}
                    </Badge>
                    <Badge className={getPermissionColor(doc.permissions)}>
                      {doc.permissions}
                    </Badge>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Shared Date</p>
                      <p className="text-sm">{new Date(doc.sharedDate).toLocaleDateString()}</p>
                    </div>
                    {doc.expiryDate && (
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Expires</p>
                        <p className="text-sm">{new Date(doc.expiryDate).toLocaleDateString()}</p>
                      </div>
                    )}
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Access Count</p>
                      <p className="text-sm">{doc.accessCount} views</p>
                    </div>
                  </div>

                  <div className="mb-4">
                    <p className="text-sm font-medium mb-2">Shared with:</p>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-gray-500" />
                      <div className="flex items-center gap-1">
                        {doc.sharedWith.slice(0, 3).map((person, index) => (
                          <Avatar key={index} className="h-6 w-6">
                            <AvatarFallback className="text-xs">
                              {person.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                        ))}
                        {doc.sharedWith.length > 3 && (
                          <span className="text-sm text-muted-foreground ml-2">
                            +{doc.sharedWith.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Eye className="h-4 w-4 mr-1" />
                  View
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-1" />
                  Download
                </Button>
                <Button variant="outline" size="sm">
                  <Link className="h-4 w-4 mr-1" />
                  Copy Link
                </Button>
                <Button variant="outline" size="sm">
                  <Mail className="h-4 w-4 mr-1" />
                  Send Email
                </Button>
                <Button variant="outline" size="sm">
                  <Settings className="h-4 w-4 mr-1" />
                  Permissions
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Share Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="p-4 hover:bg-slate-50 cursor-pointer">
              <div className="flex items-center gap-3">
                <Users className="h-8 w-8 text-blue-600" />
                <div>
                  <h4 className="font-medium">Share with Team</h4>
                  <p className="text-sm text-muted-foreground">Internal sharing</p>
                </div>
              </div>
            </Card>

            <Card className="p-4 hover:bg-slate-50 cursor-pointer">
              <div className="flex items-center gap-3">
                <Link className="h-8 w-8 text-green-600" />
                <div>
                  <h4 className="font-medium">Generate Link</h4>
                  <p className="text-sm text-muted-foreground">Public sharing</p>
                </div>
              </div>
            </Card>

            <Card className="p-4 hover:bg-slate-50 cursor-pointer">
              <div className="flex items-center gap-3">
                <Mail className="h-8 w-8 text-purple-600" />
                <div>
                  <h4 className="font-medium">Email Share</h4>
                  <p className="text-sm text-muted-foreground">Direct email</p>
                </div>
              </div>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
