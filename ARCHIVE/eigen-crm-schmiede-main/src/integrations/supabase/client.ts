// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://mpqcvjzxfoshkmrveout.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1wcWN2anp4Zm9zaGttcnZlb3V0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0NDQ3NzUsImV4cCI6MjA2NDAyMDc3NX0.yE1jPpCoUwz1OhgFPB5Hn8I3_Ve-TaUvt-UT2w0poPw";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);