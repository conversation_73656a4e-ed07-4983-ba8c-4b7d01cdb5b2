
export interface Opportunity {
  id: number;
  title: string;
  company: string;
  contactPerson: string;
  value: number;
  stage: 'discovery' | 'qualification' | 'proposal' | 'negotiation' | 'closed-won' | 'closed-lost';
  probability: number;
  expectedCloseDate: string;
  createdDate: string;
  lastActivity: string;
  description: string;
  tags: string[];
  notes: string[];
  nextSteps: string[];
}

export const mockOpportunities: Opportunity[] = [
  {
    id: 1,
    title: "Enterprise License Expansion",
    company: "TechCorp Solutions",
    contactPerson: "<PERSON>",
    value: 125000,
    stage: "negotiation",
    probability: 85,
    expectedCloseDate: "2024-02-15",
    createdDate: "2023-12-01",
    lastActivity: "2024-01-20",
    description: "Expansion of existing enterprise license to include 500 additional users across 3 new departments.",
    tags: ["expansion", "enterprise", "license"],
    notes: [
      "Budget approved by CFO",
      "Security review completed",
      "Waiting for final contract terms"
    ],
    nextSteps: [
      "Schedule final negotiation meeting",
      "Prepare contract amendments",
      "Coordinate implementation timeline"
    ]
  },
  {
    id: 2,
    title: "AI Automation Platform",
    company: "InnovatePlus",
    contactPerson: "<PERSON>",
    value: 75000,
    stage: "proposal",
    probability: 70,
    expectedCloseDate: "2024-03-01",
    createdDate: "2024-01-05",
    lastActivity: "2024-01-18",
    description: "Implementation of AI-powered automation platform for customer service operations.",
    tags: ["ai", "automation", "new-customer"],
    notes: [
      "Demo completed successfully",
      "Technical requirements gathered",
      "Proposal submitted"
    ],
    nextSteps: [
      "Follow up on proposal",
      "Schedule technical deep-dive",
      "Provide ROI calculations"
    ]
  },
  {
    id: 3,
    title: "Investment Portfolio Analytics",
    company: "Global Ventures",
    contactPerson: "Emily Rodriguez",
    value: 200000,
    stage: "discovery",
    probability: 40,
    expectedCloseDate: "2024-04-30",
    createdDate: "2024-01-10",
    lastActivity: "2024-01-22",
    description: "Custom analytics platform for investment portfolio management and risk assessment.",
    tags: ["custom", "analytics", "fintech"],
    notes: [
      "Initial discovery call completed",
      "Requirements gathering in progress",
      "Stakeholder mapping needed"
    ],
    nextSteps: [
      "Schedule stakeholder interviews",
      "Prepare technical architecture proposal",
      "Identify integration requirements"
    ]
  },
  {
    id: 4,
    title: "Startup Package Deal",
    company: "StartupX",
    contactPerson: "Lisa Thompson",
    value: 25000,
    stage: "qualification",
    probability: 60,
    expectedCloseDate: "2024-02-28",
    createdDate: "2024-01-12",
    lastActivity: "2024-01-17",
    description: "Startup package including basic platform access and onboarding support.",
    tags: ["startup", "package", "small-deal"],
    notes: [
      "Budget constraints discussed",
      "Flexible payment terms requested",
      "Referral from existing customer"
    ],
    nextSteps: [
      "Prepare startup pricing proposal",
      "Schedule product demo",
      "Discuss payment options"
    ]
  }
];
