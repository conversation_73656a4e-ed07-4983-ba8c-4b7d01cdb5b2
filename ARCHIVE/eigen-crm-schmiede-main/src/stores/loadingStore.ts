
import { create } from 'zustand';

interface LoadingState {
  globalLoading: boolean;
  componentLoading: Record<string, boolean>;
  loadingMessages: Record<string, string>;
  
  setGlobalLoading: (loading: boolean, message?: string) => void;
  setComponentLoading: (component: string, loading: boolean, message?: string) => void;
  clearComponentLoading: (component: string) => void;
  clearAllLoading: () => void;
  isLoading: (component?: string) => boolean;
  getLoadingMessage: (component?: string) => string;
}

export const useLoadingStore = create<LoadingState>((set, get) => ({
  globalLoading: false,
  componentLoading: {},
  loadingMessages: {},

  setGlobalLoading: (loading: boolean, message?: string) => 
    set(state => ({
      globalLoading: loading,
      loadingMessages: {
        ...state.loadingMessages,
        global: message || ''
      }
    })),

  setComponentLoading: (component: string, loading: boolean, message?: string) =>
    set(state => ({
      componentLoading: {
        ...state.componentLoading,
        [component]: loading
      },
      loadingMessages: {
        ...state.loadingMessages,
        [component]: message || ''
      }
    })),

  clearComponentLoading: (component: string) =>
    set(state => {
      const newComponentLoading = { ...state.componentLoading };
      const newLoadingMessages = { ...state.loadingMessages };
      delete newComponentLoading[component];
      delete newLoadingMessages[component];
      
      return {
        componentLoading: newComponentLoading,
        loadingMessages: newLoadingMessages
      };
    }),

  clearAllLoading: () =>
    set({
      globalLoading: false,
      componentLoading: {},
      loadingMessages: {}
    }),

  isLoading: (component?: string) => {
    const state = get();
    if (component) {
      return state.componentLoading[component] || false;
    }
    return state.globalLoading || Object.values(state.componentLoading).some(loading => loading);
  },

  getLoadingMessage: (component?: string) => {
    const state = get();
    if (component) {
      return state.loadingMessages[component] || '';
    }
    return state.loadingMessages.global || '';
  }
}));
