
import { SettingsState } from '@/types/settings';
import { APIKeys, DatabaseConfig, NotificationConfig, RAGConfig, LLMConfig, AutonomyConfig } from '@/types/settings';

export interface SettingsActions {
  updateApiKeys: (keys: Partial<APIKeys>) => void;
  updateDatabase: (config: Partial<DatabaseConfig>) => void;
  updateNotifications: (config: Partial<NotificationConfig>) => void;
  updateRAG: (config: Partial<RAGConfig>) => void;
  updateLLM: (config: Partial<LLMConfig>) => void;
  updateAutonomy: (config: Partial<AutonomyConfig>) => void;
  syncSettings: () => Promise<void>;
  resetSettings: () => void;
  exportSettings: () => string;
  importSettings: (settings: string) => void;
}

export type SettingsStore = SettingsState & SettingsActions;
