
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { SettingsStore } from './settings/types';
import { initialState } from './settings/initialState';
import { createSettingsActions } from './settings/actions';

export const useSettingsStore = create<SettingsStore>()(
  persist(
    (set, get) => ({
      ...initialState,
      ...createSettingsActions(set, get)
    }),
    {
      name: 'crm-settings',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        apiKeys: state.apiKeys,
        database: state.database,
        notifications: state.notifications,
        rag: state.rag,
        llm: state.llm,
        autonomy: state.autonomy,
        lastSync: state.lastSync
      })
    }
  )
);
