
import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'

export interface Conversation {
  id: string
  userMessage: string
  aiResponse: string | null
  context: Record<string, any>
  timestamp: string
}

interface ConversationState {
  conversations: Conversation[]
  currentConversation: string | null
  
  // Actions
  addConversation: (conversation: Conversation) => void
  setCurrentConversation: (id: string | null) => void
}

export const useConversationStore = create<ConversationState>()(
  subscribeWithSelector((set) => ({
    conversations: [],
    currentConversation: null,
    
    addConversation: (conversation) => set((state) => ({
      conversations: [...state.conversations, conversation]
    })),
    
    setCurrentConversation: (id) => set({ currentConversation: id })
  }))
)
