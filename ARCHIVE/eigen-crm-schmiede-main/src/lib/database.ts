import { supabase } from './supabase';

// Database service for real data operations
export class DatabaseService {
  // Projects
  static async getProjects() {
    const { data, error } = await supabase
      .from('projects')
      .select(`
        *,
        company:companies(name),
        project_manager:profiles!project_manager_id(full_name)
      `)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  }

  static async getProject(id: string) {
    const { data, error } = await supabase
      .from('projects')
      .select(`
        *,
        company:companies(*),
        project_manager:profiles!project_manager_id(*),
        tasks(*),
        safety_reports(*)
      `)
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  }

  // AI Agents
  static async getAIAgents() {
    const { data, error } = await supabase
      .from('ai_agents')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  }

  static async updateAgentStatus(id: string, status: string) {
    const { data, error } = await supabase
      .from('ai_agents')
      .update({ 
        status, 
        last_activity: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  // Conversations
  static async getConversations(limit?: number) {
    let query = supabase
      .from('conversations')
      .select('*')
      .order('last_message_at', { ascending: false });
    
    if (limit) {
      query = query.limit(limit);
    }
    
    const { data, error } = await query;
    
    if (error) throw error;
    return data;
  }

  static async saveConversation(data: any) {
    const { data: result, error } = await supabase
      .from('conversations')
      .insert(data)
      .select()
      .single();
    
    if (error) throw error;
    return result;
  }

  // Approval Requests
  static async getApprovalRequests() {
    const { data, error } = await supabase
      .from('approval_requests')
      .select(`
        *,
        project:projects(name),
        requested_by:profiles!requested_by(full_name)
      `)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  }

  static async updateApprovalRequest(id: string, status: string, approvedBy?: string) {
    const updates: any = { 
      status, 
      updated_at: new Date().toISOString()
    };
    
    if (status === 'approved' && approvedBy) {
      updates.approved_by = approvedBy;
      updates.approval_date = new Date().toISOString();
    }

    const { data, error } = await supabase
      .from('approval_requests')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  // Opportunities
  static async getOpportunities() {
    const { data, error } = await supabase
      .from('opportunities')
      .select(`
        *,
        company:companies(name),
        contact:contacts(first_name, last_name)
      `)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  }

  static async createOpportunity(data: any) {
    const { data: result, error } = await supabase
      .from('opportunities')
      .insert(data)
      .select()
      .single();
    
    if (error) throw error;
    return result;
  }

  static async updateOpportunity(id: string, updates: any) {
    const { data, error } = await supabase
      .from('opportunities')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  static async deleteOpportunity(id: string) {
    const { data, error } = await supabase
      .from('opportunities')
      .delete()
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  // Activity Feed
  static async getActivityFeed(limit = 10) {
    const { data, error } = await supabase
      .from('activity_feed')
      .select(`
        *,
        project:projects(name),
        user:profiles!user_id(full_name)
      `)
      .order('created_at', { ascending: false })
      .limit(limit);
    
    if (error) throw error;
    return data;
  }

  static async createActivityEntry(entry: {
    type: string;
    title: string;
    description: string;
    status?: string;
    value?: string;
    project_id?: string;
    user_id?: string;
  }) {
    const { data, error } = await supabase
      .from('activity_feed')
      .insert(entry)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  // Companies
  static async getCompanies() {
    const { data, error } = await supabase
      .from('companies')
      .select('*')
      .order('name');
    
    if (error) throw error;
    return data;
  }

  static async createCompany(data: any) {
    const { data: result, error } = await supabase
      .from('companies')
      .insert(data)
      .select()
      .single();
    
    if (error) throw error;
    return result;
  }

  static async updateCompany(id: string, updates: any) {
    const { data, error } = await supabase
      .from('companies')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  static async deleteCompany(id: string) {
    const { data, error } = await supabase
      .from('companies')
      .delete()
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  // Contacts
  static async getContacts() {
    const { data, error } = await supabase
      .from('contacts')
      .select(`
        *,
        company:companies(name)
      `)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  }

  static async createContact(data: any) {
    const { data: result, error } = await supabase
      .from('contacts')
      .insert(data)
      .select()
      .single();
    
    if (error) throw error;
    return result;
  }

  static async updateContact(id: string, updates: any) {
    const { data, error } = await supabase
      .from('contacts')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  static async deleteContact(id: string) {
    const { data, error } = await supabase
      .from('contacts')
      .delete()
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  // Tasks
  static async getTasks(projectId?: string) {
    let query = supabase
      .from('tasks')
      .select(`
        *,
        project:projects(name),
        assigned_to:profiles!assigned_to(full_name),
        created_by:profiles!created_by(full_name)
      `);
    
    if (projectId) {
      query = query.eq('project_id', projectId);
    }
    
    const { data, error } = await query
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  }

  // Safety Reports
  static async getSafetyReports(projectId?: string) {
    let query = supabase
      .from('safety_reports')
      .select(`
        *,
        project:projects(name),
        reported_by:profiles!reported_by(full_name),
        assigned_to:profiles!assigned_to(full_name)
      `);
    
    if (projectId) {
      query = query.eq('project_id', projectId);
    }
    
    const { data, error } = await query
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  }

  // User Profile
  static async getCurrentUserProfile() {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return null;

    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();
    
    if (error && error.code !== 'PGRST116') throw error;
    return data;
  }

  static async getCurrentUser() {
    return this.getCurrentUserProfile();
  }

  // Communication methods
  static async getChannels() {
    const { data, error } = await supabase
      .from('channels')
      .select('*')
      .order('name');
    
    if (error) throw error;
    return data || [];
  }

  static async getMessages(channelId: string) {
    const { data, error } = await supabase
      .from('messages')
      .select('*')
      .eq('channel_id', channelId)
      .order('created_at', { ascending: true });
    
    if (error) throw error;
    return data || [];
  }

  static async getAnnouncements() {
    const { data, error } = await supabase
      .from('announcements')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }

  static async sendMessage(channelId: string, message: string) {
    const { data, error } = await supabase
      .from('messages')
      .insert({
        channel_id: channelId,
        content: message,
        sender_id: (await supabase.auth.getUser()).data.user?.id
      })
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  static async createAnnouncement(announcement: { title: string; content: string; priority: string }) {
    const { data, error } = await supabase
      .from('announcements')
      .insert({
        ...announcement,
        author_id: (await supabase.auth.getUser()).data.user?.id
      })
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  // Additional methods for other components
  static async createEmployee(data: any) {
    const { data: result, error } = await supabase
      .from('employees')
      .insert(data)
      .select()
      .single();
    
    if (error) throw error;
    return result;
  }

  static async getShipments() {
    const { data, error } = await supabase
      .from('shipments')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }

  static async getSuppliers() {
    const { data, error } = await supabase
      .from('suppliers')
      .select('*')
      .order('name');
    
    if (error) throw error;
    return data || [];
  }

  static async createShipment(data: any) {
    const { data: result, error } = await supabase
      .from('shipments')
      .insert(data)
      .select()
      .single();
    
    if (error) throw error;
    return result;
  }

  static async updateShipment(id: string, updates: any) {
    const { data, error } = await supabase
      .from('shipments')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  static async getQualityMetrics() {
    const { data, error } = await supabase
      .from('quality_metrics')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }

  static async createQualityInspection(data: any) {
    const { data: result, error } = await supabase
      .from('quality_inspections')
      .insert(data)
      .select()
      .single();
    
    if (error) throw error;
    return result;
  }

  static async getQualityInspections() {
    const { data, error } = await supabase
      .from('quality_inspections')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }
}
