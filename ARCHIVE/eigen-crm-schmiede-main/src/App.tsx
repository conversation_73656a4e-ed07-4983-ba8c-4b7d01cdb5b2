
import {
  BrowserRouter,
  Routes,
  Route,
} from "react-router-dom"
import {
  QueryClient,
  QueryClientProvider,
} from "@tanstack/react-query"
import { ThemeProvider } from "@/components/theme-provider"
import { LanguageProvider } from "@/hooks/useLanguage"
import { AuthProvider } from "@/components/auth/AuthProvider"
import { ProtectedRoute } from "@/components/auth/ProtectedRoute"
import { Layout } from "@/components/Layout"
import { Index } from "@/components/Index"
import { UnifiedDashboard } from "@/components/unified/UnifiedDashboard"
import { Projects } from "@/components/Projects"
import { People } from "@/components/People"
import { Companies } from "@/components/Companies"
import { Opportunities } from "@/components/Opportunities"
import { Documents } from "@/components/Documents"
import { Settings } from "@/components/Settings"
import { NotFound } from "@/components/NotFound"
import { Calendar } from "@/components/Calendar"
import { Tasks } from '@/components/Tasks'
import { Fleet } from '@/components/Fleet'
import { Automation } from '@/components/Automation'
import { Toaster } from "@/components/ui/toaster"
import { EnhancedErrorBoundary } from "@/components/common/EnhancedErrorBoundary"
import { AuthPage } from './components/auth/AuthPage'
import { ReportsPage } from './components/reports/ReportsPage'
import { SecurityEnhancements } from './components/security/SecurityEnhancements'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error) => {
        // Don't retry on 4xx errors
        if (error && typeof error === 'object' && 'status' in error) {
          const status = error.status as number
          if (status >= 400 && status < 500) return false
        }
        return failureCount < 3
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    },
    mutations: {
      retry: 1,
    },
  },
})

function App() {
  return (
    <EnhancedErrorBoundary component="App" showDetails={true}>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider defaultTheme="light" storageKey="vite-ui-theme">
          <LanguageProvider>
            <AuthProvider>
              <BrowserRouter>
                <ProtectedRoute>
                  <Routes>
                    <Route path="/auth" element={<AuthPage />} />
                    <Route path="/" element={
                      <Layout />
                    }>
                      <Route index element={<Index />} />
                      <Route path="dashboard" element={<UnifiedDashboard />} />
                      <Route path="projects" element={<Projects />} />
                      <Route path="people" element={<People />} />
                      <Route path="companies" element={<Companies />} />
                      <Route path="opportunities" element={<Opportunities />} />
                      <Route path="documents" element={<Documents />} />
                      <Route path="tasks" element={<Tasks />} />
                      <Route path="calendar" element={<Calendar />} />
                      <Route path="fleet" element={<Fleet />} />
                      <Route path="automation" element={<Automation />} />
                      <Route path="settings" element={<Settings />} />
                      <Route path="reports" element={<ReportsPage />} />
                      <Route path="security" element={<SecurityEnhancements />} />
                      <Route path="*" element={<NotFound />} />
                    </Route>
                  </Routes>
                </ProtectedRoute>
              </BrowserRouter>
            </AuthProvider>
            <Toaster />
          </LanguageProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </EnhancedErrorBoundary>
  )
}

export default App
