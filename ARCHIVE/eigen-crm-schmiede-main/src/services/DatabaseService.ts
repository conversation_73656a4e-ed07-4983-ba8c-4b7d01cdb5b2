import { supabase, isSupabaseConfigured } from '@/lib/supabase';
import { logger } from './Logger';
import { ErrorService } from './ErrorService';
import type { Database } from '@/lib/supabase';

type Tables = Database['public']['Tables'];

export class DatabaseService {
  private static isConfigured = isSupabaseConfigured();

  // Vehicles
  static async getVehicles() {
    if (!this.isConfigured) {
      logger.info('Mock: Getting vehicles');
      return [];
    }

    try {
      const { data, error } = await supabase
        .from('vehicles')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'getVehicles', true, { count: data?.length || 0 });
      return data || [];
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'getVehicles');
      logger.error('Failed to get vehicles', { error: serviceError });
      return [];
    }
  }

  // Maintenance Records
  static async getMaintenanceRecords() {
    if (!this.isConfigured) {
      logger.info('Mock: Getting maintenance records');
      return [];
    }

    try {
      const { data, error } = await supabase
        .from('maintenance_records')
        .select('*')
        .order('performed_at', { ascending: false });

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'getMaintenanceRecords', true, { count: data?.length || 0 });
      return data || [];
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'getMaintenanceRecords');
      logger.error('Failed to get maintenance records', { error: serviceError });
      return [];
    }
  }

  // Safety Reports
  static async getSafetyReports() {
    if (!this.isConfigured) {
      logger.info('Mock: Getting safety reports');
      return [
        { id: '1', title: 'PPE Inspection', severity: 'low', status: 'resolved', created_at: new Date().toISOString() },
        { id: '2', title: 'Equipment Check', severity: 'high', status: 'pending', created_at: new Date().toISOString() }
      ];
    }

    try {
      const { data, error } = await supabase
        .from('safety_reports')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'getSafetyReports', true, { count: data?.length || 0 });
      return data || [];
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'getSafetyReports');
      logger.error('Failed to get safety reports', { error: serviceError });
      return [];
    }
  }

  // Quality Inspections
  static async getQualityInspections() {
    if (!this.isConfigured) {
      logger.info('Mock: Getting quality inspections');
      return [
        { id: '1', area: 'Building A', passed: true, defects_found: 0, inspection_date: new Date().toISOString() },
        { id: '2', area: 'Foundation', passed: false, defects_found: 3, inspection_date: new Date().toISOString() }
      ];
    }

    try {
      const { data, error } = await supabase
        .from('quality_inspections')
        .select('*')
        .order('inspection_date', { ascending: false });

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'getQualityInspections', true, { count: data?.length || 0 });
      return data || [];
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'getQualityInspections');
      logger.error('Failed to get quality inspections', { error: serviceError });
      return [];
    }
  }

  // Quality Metrics
  static async getQualityMetrics() {
    if (!this.isConfigured) {
      logger.info('Mock: Getting quality metrics');
      return [];
    }

    try {
      const { data, error } = await supabase
        .from('quality_metrics')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'getQualityMetrics', true, { count: data?.length || 0 });
      return data || [];
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'getQualityMetrics');
      logger.error('Failed to get quality metrics', { error: serviceError });
      return [];
    }
  }

  // Create Quality Inspection
  static async createQualityInspection(inspection: any) {
    if (!this.isConfigured) {
      logger.info('Mock: Creating quality inspection');
      return { id: Date.now().toString(), ...inspection };
    }

    try {
      const { data, error } = await supabase
        .from('quality_inspections')
        .insert(inspection)
        .select()
        .single();

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'createQualityInspection', true);
      return data;
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'createQualityInspection');
      logger.error('Failed to create quality inspection', { error: serviceError });
      throw serviceError;
    }
  }

  // Employees
  static async getEmployees() {
    if (!this.isConfigured) {
      logger.info('Mock: Getting employees');
      return [
        { id: '1', first_name: 'John', last_name: 'Doe', status: 'active', created_at: new Date().toISOString() },
        { id: '2', first_name: 'Jane', last_name: 'Smith', status: 'active', created_at: new Date().toISOString() }
      ];
    }

    try {
      const { data, error } = await supabase
        .from('employees')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'getEmployees', true, { count: data?.length || 0 });
      return data || [];
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'getEmployees');
      logger.error('Failed to get employees', { error: serviceError });
      return [];
    }
  }

  // Create Employee
  static async createEmployee(employee: any) {
    if (!this.isConfigured) {
      logger.info('Mock: Creating employee');
      return { id: Date.now().toString(), ...employee };
    }

    try {
      const { data, error } = await supabase
        .from('employees')
        .insert(employee)
        .select()
        .single();

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'createEmployee', true);
      return data;
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'createEmployee');
      logger.error('Failed to create employee', { error: serviceError });
      throw serviceError;
    }
  }

  // Trainings
  static async getTrainings() {
    if (!this.isConfigured) {
      logger.info('Mock: Getting trainings');
      return [
        { id: '1', title: 'Safety Training', status: 'completed', created_at: new Date().toISOString() },
        { id: '2', title: 'Equipment Training', status: 'ongoing', created_at: new Date().toISOString() }
      ];
    }

    try {
      const { data, error } = await supabase
        .from('trainings')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'getTrainings', true, { count: data?.length || 0 });
      return data || [];
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'getTrainings');
      logger.error('Failed to get trainings', { error: serviceError });
      return [];
    }
  }

  // Communication - Channels
  static async getChannels() {
    if (!this.isConfigured) {
      logger.info('Mock: Getting channels');
      return [
        { id: 'general', name: 'General', type: 'channel', unread: 0 },
        { id: 'projects', name: 'Projects', type: 'channel', unread: 2 }
      ];
    }

    try {
      const { data, error } = await supabase
        .from('channels')
        .select('*')
        .order('name');

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'getChannels', true, { count: data?.length || 0 });
      return data || [];
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'getChannels');
      logger.error('Failed to get channels', { error: serviceError });
      return [];
    }
  }

  // Messages
  static async getMessages(channelId: string) {
    if (!this.isConfigured) {
      logger.info('Mock: Getting messages');
      return [
        { id: '1', content: 'Hello team!', sender: 'John Doe', created_at: new Date().toISOString() }
      ];
    }

    try {
      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .eq('channel_id', channelId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'getMessages', true, { count: data?.length || 0 });
      return data || [];
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'getMessages');
      logger.error('Failed to get messages', { error: serviceError });
      return [];
    }
  }

  // Send Message
  static async sendMessage(channelId: string, message: string) {
    if (!this.isConfigured) {
      logger.info('Mock: Sending message');
      return { id: Date.now().toString(), content: message, channel_id: channelId };
    }

    try {
      const { data, error } = await supabase
        .from('messages')
        .insert({ channel_id: channelId, content: message })
        .select()
        .single();

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'sendMessage', true);
      return data;
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'sendMessage');
      logger.error('Failed to send message', { error: serviceError });
      throw serviceError;
    }
  }

  // Announcements
  static async getAnnouncements() {
    if (!this.isConfigured) {
      logger.info('Mock: Getting announcements');
      return [
        { id: '1', title: 'Team Meeting', content: 'Weekly team meeting tomorrow', created_at: new Date().toISOString() }
      ];
    }

    try {
      const { data, error } = await supabase
        .from('announcements')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'getAnnouncements', true, { count: data?.length || 0 });
      return data || [];
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'getAnnouncements');
      logger.error('Failed to get announcements', { error: serviceError });
      return [];
    }
  }

  // Create Announcement
  static async createAnnouncement(announcement: any) {
    if (!this.isConfigured) {
      logger.info('Mock: Creating announcement');
      return { id: Date.now().toString(), ...announcement };
    }

    try {
      const { data, error } = await supabase
        .from('announcements')
        .insert(announcement)
        .select()
        .single();

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'createAnnouncement', true);
      return data;
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'createAnnouncement');
      logger.error('Failed to create announcement', { error: serviceError });
      throw serviceError;
    }
  }

  // Logistics - Shipments
  static async getShipments() {
    if (!this.isConfigured) {
      logger.info('Mock: Getting shipments');
      return [
        { id: '1', tracking_number: 'TRK-001', status: 'in_transit', created_at: new Date().toISOString() }
      ];
    }

    try {
      const { data, error } = await supabase
        .from('shipments')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'getShipments', true, { count: data?.length || 0 });
      return data || [];
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'getShipments');
      logger.error('Failed to get shipments', { error: serviceError });
      return [];
    }
  }

  // Suppliers
  static async getSuppliers() {
    if (!this.isConfigured) {
      logger.info('Mock: Getting suppliers');
      return [
        { id: '1', name: 'ABC Suppliers', status: 'active', created_at: new Date().toISOString() }
      ];
    }

    try {
      const { data, error } = await supabase
        .from('suppliers')
        .select('*')
        .order('name');

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'getSuppliers', true, { count: data?.length || 0 });
      return data || [];
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'getSuppliers');
      logger.error('Failed to get suppliers', { error: serviceError });
      return [];
    }
  }

  // Create Shipment
  static async createShipment(shipment: any) {
    if (!this.isConfigured) {
      logger.info('Mock: Creating shipment');
      return { id: Date.now().toString(), ...shipment };
    }

    try {
      const { data, error } = await supabase
        .from('shipments')
        .insert(shipment)
        .select()
        .single();

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'createShipment', true);
      return data;
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'createShipment');
      logger.error('Failed to create shipment', { error: serviceError });
      throw serviceError;
    }
  }

  // Update Shipment
  static async updateShipment(id: string, updates: any) {
    if (!this.isConfigured) {
      logger.info('Mock: Updating shipment');
      return { id, ...updates };
    }

    try {
      const { data, error } = await supabase
        .from('shipments')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'updateShipment', true);
      return data;
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'updateShipment');
      logger.error('Failed to update shipment', { error: serviceError });
      throw serviceError;
    }
  }

  // Companies
  static async getCompanies() {
    if (!this.isConfigured) {
      logger.info('Mock: Getting companies');
      return [
        { id: '1', name: 'ABC Corp', industry: 'Construction', created_at: new Date().toISOString() }
      ];
    }

    try {
      const { data, error } = await supabase
        .from('companies')
        .select('*')
        .order('name');

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'getCompanies', true, { count: data?.length || 0 });
      return data || [];
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'getCompanies');
      logger.error('Failed to get companies', { error: serviceError });
      return [];
    }
  }

  // Create Company
  static async createCompany(company: any) {
    if (!this.isConfigured) {
      logger.info('Mock: Creating company');
      return { id: Date.now().toString(), ...company };
    }

    try {
      const { data, error } = await supabase
        .from('companies')
        .insert(company)
        .select()
        .single();

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'createCompany', true);
      return data;
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'createCompany');
      logger.error('Failed to create company', { error: serviceError });
      throw serviceError;
    }
  }

  // Update Company
  static async updateCompany(id: string, updates: any) {
    if (!this.isConfigured) {
      logger.info('Mock: Updating company');
      return { id, ...updates };
    }

    try {
      const { data, error } = await supabase
        .from('companies')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'updateCompany', true);
      return data;
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'updateCompany');
      logger.error('Failed to update company', { error: serviceError });
      throw serviceError;
    }
  }

  // Delete Company
  static async deleteCompany(id: string) {
    if (!this.isConfigured) {
      logger.info('Mock: Deleting company');
      return { id };
    }

    try {
      const { error } = await supabase
        .from('companies')
        .delete()
        .eq('id', id);

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'deleteCompany', true);
      return { id };
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'deleteCompany');
      logger.error('Failed to delete company', { error: serviceError });
      throw serviceError;
    }
  }

  // Contacts
  static async getContacts() {
    if (!this.isConfigured) {
      logger.info('Mock: Getting contacts');
      return [
        { id: '1', first_name: 'John', last_name: 'Doe', email: '<EMAIL>', created_at: new Date().toISOString() }
      ];
    }

    try {
      const { data, error } = await supabase
        .from('contacts')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'getContacts', true, { count: data?.length || 0 });
      return data || [];
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'getContacts');
      logger.error('Failed to get contacts', { error: serviceError });
      return [];
    }
  }

  // Create Contact
  static async createContact(contact: any) {
    if (!this.isConfigured) {
      logger.info('Mock: Creating contact');
      return { id: Date.now().toString(), ...contact };
    }

    try {
      const { data, error } = await supabase
        .from('contacts')
        .insert(contact)
        .select()
        .single();

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'createContact', true);
      return data;
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'createContact');
      logger.error('Failed to create contact', { error: serviceError });
      throw serviceError;
    }
  }

  // Update Contact
  static async updateContact(id: string, updates: any) {
    if (!this.isConfigured) {
      logger.info('Mock: Updating contact');
      return { id, ...updates };
    }

    try {
      const { data, error } = await supabase
        .from('contacts')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'updateContact', true);
      return data;
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'updateContact');
      logger.error('Failed to update contact', { error: serviceError });
      throw serviceError;
    }
  }

  // Delete Contact
  static async deleteContact(id: string) {
    if (!this.isConfigured) {
      logger.info('Mock: Deleting contact');
      return { id };
    }

    try {
      const { error } = await supabase
        .from('contacts')
        .delete()
        .eq('id', id);

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'deleteContact', true);
      return { id };
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'deleteContact');
      logger.error('Failed to delete contact', { error: serviceError });
      throw serviceError;
    }
  }

  // Opportunities
  static async getOpportunities() {
    if (!this.isConfigured) {
      logger.info('Mock: Getting opportunities');
      return [
        { id: '1', title: 'New Project', stage: 'discovery', value: 100000, created_at: new Date().toISOString() }
      ];
    }

    try {
      const { data, error } = await supabase
        .from('opportunities')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'getOpportunities', true, { count: data?.length || 0 });
      return data || [];
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'getOpportunities');
      logger.error('Failed to get opportunities', { error: serviceError });
      return [];
    }
  }

  // Create Opportunity
  static async createOpportunity(opportunity: any) {
    if (!this.isConfigured) {
      logger.info('Mock: Creating opportunity');
      return { id: Date.now().toString(), ...opportunity };
    }

    try {
      const { data, error } = await supabase
        .from('opportunities')
        .insert(opportunity)
        .select()
        .single();

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'createOpportunity', true);
      return data;
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'createOpportunity');
      logger.error('Failed to create opportunity', { error: serviceError });
      throw serviceError;
    }
  }

  // Update Opportunity
  static async updateOpportunity(id: string, updates: any) {
    if (!this.isConfigured) {
      logger.info('Mock: Updating opportunity');
      return { id, ...updates };
    }

    try {
      const { data, error } = await supabase
        .from('opportunities')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'updateOpportunity', true);
      return data;
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'updateOpportunity');
      logger.error('Failed to update opportunity', { error: serviceError });
      throw serviceError;
    }
  }

  // Delete Opportunity
  static async deleteOpportunity(id: string) {
    if (!this.isConfigured) {
      logger.info('Mock: Deleting opportunity');
      return { id };
    }

    try {
      const { error } = await supabase
        .from('opportunities')
        .delete()
        .eq('id', id);

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'deleteOpportunity', true);
      return { id };
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'deleteOpportunity');
      logger.error('Failed to delete opportunity', { error: serviceError });
      throw serviceError;
    }
  }

  // Conversations
  static async getConversations(limit?: number) {
    if (!this.isConfigured) {
      logger.info('Mock: Getting conversations');
      return [
        { id: '1', title: 'Project Discussion', created_at: new Date().toISOString() }
      ];
    }

    try {
      let query = supabase
        .from('conversations')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (limit) {
        query = query.limit(limit);
      }
      
      const { data, error } = await query;

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'getConversations', true, { count: data?.length || 0 });
      return data || [];
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'getConversations');
      logger.error('Failed to get conversations', { error: serviceError });
      return [];
    }
  }

  // Save Conversation
  static async saveConversation(conversation: any) {
    if (!this.isConfigured) {
      logger.info('Mock: Saving conversation');
      return { id: Date.now().toString(), ...conversation };
    }

    try {
      const { data, error } = await supabase
        .from('conversations')
        .insert(conversation)
        .select()
        .single();

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'saveConversation', true);
      return data;
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'saveConversation');
      logger.error('Failed to save conversation', { error: serviceError });
      throw serviceError;
    }
  }

  // AI Agents
  static async getAIAgents() {
    if (!this.isConfigured) {
      logger.info('Mock: Getting AI agents');
      return [
        { id: '1', name: 'Project Assistant', status: 'active', created_at: new Date().toISOString() }
      ];
    }

    try {
      const { data, error } = await supabase
        .from('ai_agents')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'getAIAgents', true, { count: data?.length || 0 });
      return data || [];
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'getAIAgents');
      logger.error('Failed to get AI agents', { error: serviceError });
      return [];
    }
  }

  // Update Agent Status
  static async updateAgentStatus(id: string, status: string) {
    if (!this.isConfigured) {
      logger.info('Mock: Updating agent status');
      return { id, status };
    }

    try {
      const { data, error } = await supabase
        .from('ai_agents')
        .update({ status })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      logger.logServiceCall('DatabaseService', 'updateAgentStatus', true);
      return data;
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'updateAgentStatus');
      logger.error('Failed to update agent status', { error: serviceError });
      throw serviceError;
    }
  }

  // Current User
  static async getCurrentUser() {
    if (!this.isConfigured) {
      logger.info('Mock: Getting current user');
      return { id: 'mock-user', email: '<EMAIL>' };
    }

    try {
      const { data: { user } } = await supabase.auth.getUser();
      logger.logServiceCall('DatabaseService', 'getCurrentUser', true);
      return user;
    } catch (error) {
      const serviceError = ErrorService.handleServiceError(error, 'DatabaseService', 'getCurrentUser');
      logger.error('Failed to get current user', { error: serviceError });
      return null;
    }
  }
}
