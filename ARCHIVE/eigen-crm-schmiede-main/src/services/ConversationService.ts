
import { supabase, isSupabaseConfigured } from '@/lib/supabase';
import type { Database } from '@/lib/supabase';

type Tables = Database['public']['Tables'];
type Conversation = Tables['conversations']['Row'];

export class ConversationService {
  private static isConfigured = isSupabaseConfigured();

  static async getCurrentUser() {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw error;
    return user;
  }

  static async saveConversation(conversation: Tables['conversations']['Insert']): Promise<Conversation | null> {
    if (!this.isConfigured) {
      console.log('Mock: Saving conversation:', conversation);
      return { 
        ...conversation, 
        id: `mock-${Date.now()}`, 
        created_at: new Date().toISOString()
      } as Conversation;
    }

    try {
      const user = await this.getCurrentUser();
      const conversationWithUser = {
        ...conversation,
        user_message: conversation.user_message,
        context: {
          ...conversation.context,
          user_id: user?.id
        }
      };

      const { data, error } = await supabase
        .from('conversations')
        .insert(conversationWithUser)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error saving conversation:', error);
      return null;
    }
  }

  static async getConversations(limit: number = 50): Promise<Conversation[]> {
    if (!this.isConfigured) {
      return [];
    }

    try {
      const { data, error } = await supabase
        .from('conversations')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching conversations:', error);
      return [];
    }
  }

  async processMessage(message: string): Promise<void> {
    console.log('Processing message:', message);
    // Mock implementation for now
    await ConversationService.saveConversation({
      user_message: message,
      ai_response: `Mock response to: ${message}`,
      context: {}
    });
  }
}

// Export an instance for use in other services
export const conversationService = new ConversationService();
