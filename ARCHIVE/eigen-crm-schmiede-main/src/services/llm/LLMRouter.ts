
import type { TaskType } from '@/types/unified'

export type LLMProvider = 'cloud' | 'ollama'

export interface LLMRoute {
  provider: LLMProvider
  model: string
  priority: number
}

export interface RoutingConfig {
  sensitive_data: LLMRoute[]
  code_generation: LLMRoute[]
  creative_tasks: LLMRoute[]
  real_time_chat: LLMRoute[]
  language_translation: LLMRoute[]
  business_intelligence: LLMRoute[]
  project_planning: LLMRoute[]
  market_analysis: LLMRoute[]
  default: LLMRoute[]
}

export class LLMRouter {
  private static config: RoutingConfig = {
    sensitive_data: [
      { provider: 'ollama', model: 'llama2', priority: 1 },
      { provider: 'cloud', model: 'gpt-4o-mini', priority: 2 }
    ],
    code_generation: [
      { provider: 'ollama', model: 'codellama', priority: 1 },
      { provider: 'cloud', model: 'gpt-4o', priority: 2 }
    ],
    creative_tasks: [
      { provider: 'cloud', model: 'gpt-4o', priority: 1 },
      { provider: 'ollama', model: 'mistral', priority: 2 }
    ],
    real_time_chat: [
      { provider: 'ollama', model: 'llama2', priority: 1 },
      { provider: 'cloud', model: 'gpt-4o-mini', priority: 2 }
    ],
    language_translation: [
      { provider: 'cloud', model: 'gpt-4o', priority: 1 },
      { provider: 'ollama', model: 'llama2', priority: 2 }
    ],
    business_intelligence: [
      { provider: 'ollama', model: 'llama2', priority: 1 },
      { provider: 'cloud', model: 'gpt-4o', priority: 2 }
    ],
    project_planning: [
      { provider: 'cloud', model: 'gpt-4o', priority: 1 },
      { provider: 'ollama', model: 'llama2', priority: 2 }
    ],
    market_analysis: [
      { provider: 'cloud', model: 'gpt-4o', priority: 1 },
      { provider: 'ollama', model: 'llama2', priority: 2 }
    ],
    default: [
      { provider: 'ollama', model: 'llama2', priority: 1 },
      { provider: 'cloud', model: 'gpt-4o-mini', priority: 2 }
    ]
  }

  static getRoute(taskType: TaskType, dataContext?: any): LLMRoute {
    console.log(`Routing task: ${taskType}`)
    
    // Determine routing based on task type and data sensitivity
    if (this.containsSensitiveData(dataContext)) {
      return this.config.sensitive_data[0]
    }

    switch (taskType) {
      case 'research':
      case 'analysis':
        return this.config.sensitive_data[0] // Keep research local
      case 'code_generation':
      case 'generation':
        return this.config.code_generation[0]
      case 'creative':
        return this.config.creative_tasks[0]
      case 'conversation':
        return this.config.real_time_chat[0]
      case 'translation':
        return this.config.language_translation[0]
      case 'business_intelligence':
        return this.config.business_intelligence[0]
      case 'project_planning':
        return this.config.project_planning[0]
      case 'market_analysis':
        return this.config.market_analysis[0]
      default:
        return this.config.default[0]
    }
  }

  static getAlternativeRoute(taskType: TaskType): LLMRoute {
    const routes = this.getRoutesForTask(taskType)
    return routes.length > 1 ? routes[1] : routes[0]
  }

  private static getRoutesForTask(taskType: TaskType): LLMRoute[] {
    switch (taskType) {
      case 'code_generation':
      case 'generation':
        return this.config.code_generation
      case 'creative':
        return this.config.creative_tasks
      case 'conversation':
        return this.config.real_time_chat
      case 'translation':
        return this.config.language_translation
      case 'business_intelligence':
        return this.config.business_intelligence
      case 'project_planning':
        return this.config.project_planning
      case 'market_analysis':
        return this.config.market_analysis
      default:
        return this.config.default
    }
  }

  private static containsSensitiveData(data: any): boolean {
    if (!data) return false
    
    const sensitiveFields = ['email', 'phone', 'customer_id', 'contact_id', 'company_name']
    const dataStr = JSON.stringify(data).toLowerCase()
    
    return sensitiveFields.some(field => dataStr.includes(field))
  }

  static updateConfig(newConfig: Partial<RoutingConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }
}
