
export interface OllamaResponse {
  model: string;
  created_at: string;
  response: string;
  done: boolean;
}

export interface OllamaModel {
  name: string;
  size: number;
  digest: string;
  modified_at: string;
}

export class OllamaService {
  private static baseUrl = 'http://localhost:11434';
  private static isAvailable: boolean | null = null;

  static async checkAvailability(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`);
      this.isAvailable = response.ok;
      return this.isAvailable;
    } catch (error) {
      console.log('Ollama not available:', error);
      this.isAvailable = false;
      return false;
    }
  }

  static async getAvailableModels(): Promise<OllamaModel[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`);
      if (!response.ok) throw new Error('Failed to fetch models');
      
      const data = await response.json();
      return data.models || [];
    } catch (error) {
      console.error('Error fetching Ollama models:', error);
      return [];
    }
  }

  static async generateResponse(
    model: string, 
    prompt: string, 
    context?: any
  ): Promise<string> {
    if (!await this.checkAvailability()) {
      throw new Error('Ollama is not available');
    }

    try {
      const response = await fetch(`${this.baseUrl}/api/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model,
          prompt,
          stream: false,
          context: context || {}
        }),
      });

      if (!response.ok) {
        throw new Error(`Ollama API error: ${response.statusText}`);
      }

      const data: OllamaResponse = await response.json();
      return data.response;
    } catch (error) {
      console.error('Ollama generation error:', error);
      throw error;
    }
  }

  static async pullModel(modelName: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/pull`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: modelName
        }),
      });

      return response.ok;
    } catch (error) {
      console.error('Error pulling model:', error);
      return false;
    }
  }

  static isModelAvailable(modelName: string, availableModels: OllamaModel[]): boolean {
    return availableModels.some(model => model.name.includes(modelName));
  }
}
