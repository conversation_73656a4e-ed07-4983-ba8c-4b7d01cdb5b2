
import { supabase, isSupabaseConfigured } from '@/lib/supabase';
import type { Database } from '@/lib/supabase';

type Tables = Database['public']['Tables'];
type AIAgent = Tables['ai_agents']['Row'];

export class AIAgentService {
  private static isConfigured = isSupabaseConfigured();

  static async getAIAgents(): Promise<AIAgent[]> {
    if (!this.isConfigured) {
      return this.getMockAgents();
    }

    try {
      const { data, error } = await supabase
        .from('ai_agents')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching AI agents:', error);
      return this.getMockAgents();
    }
  }

  static async updateAgentStatus(id: string, status: string, lastActivity?: string): Promise<boolean> {
    if (!this.isConfigured) {
      console.log('Mock: Updating agent status:', { id, status, lastActivity });
      return true;
    }

    try {
      const { error } = await supabase
        .from('ai_agents')
        .update({ 
          status, 
          last_activity: lastActivity || new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error updating agent status:', error);
      return false;
    }
  }

  private static getMockAgents(): AIAgent[] {
    return [
      {
        id: 'research-agent',
        name: 'Research Agent',
        type: 'research',
        status: 'idle',
        configuration: {
          model: 'gpt-4',
          capabilities: ['company_research', 'market_analysis']
        },
        last_activity: new Date().toISOString(),
        created_by: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'project-manager',
        name: 'Project Manager Agent',
        type: 'automation',
        status: 'idle',
        configuration: {
          model: 'gpt-4',
          capabilities: ['project_planning', 'resource_allocation', 'timeline_management']
        },
        last_activity: new Date().toISOString(),
        created_by: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }
}
