
import { RAGService } from './RAGService';

export interface IndexableDocument {
  id: string;
  title: string;
  content: string;
  type: 'pdf' | 'docx' | 'txt' | 'md';
  projectId?: string;
  uploadedBy: string;
  uploadedAt: string;
  tags?: string[];
}

export class DocumentIndexingService {
  private static indexedDocuments = new Set<string>();

  static async indexDocument(document: IndexableDocument): Promise<void> {
    if (this.indexedDocuments.has(document.id)) {
      console.log(`Document ${document.id} already indexed, skipping...`);
      return;
    }

    try {
      await RAGService.indexDocument(document.content, {
        source: document.title,
        type: 'document',
        timestamp: document.uploadedAt,
        projectId: document.projectId,
        tags: document.tags || [],
        documentId: document.id,
        uploadedBy: document.uploadedBy
      });

      this.indexedDocuments.add(document.id);
      console.log(`Successfully indexed document: ${document.title}`);
    } catch (error) {
      console.error(`Failed to index document ${document.title}:`, error);
      throw error;
    }
  }

  static async indexMultipleDocuments(documents: IndexableDocument[]): Promise<void> {
    console.log(`Starting batch indexing of ${documents.length} documents...`);
    
    for (const document of documents) {
      await this.indexDocument(document);
    }

    console.log('Batch indexing completed');
  }

  static async reindexDocument(document: IndexableDocument): Promise<void> {
    // Remove from indexed set to force reindexing
    this.indexedDocuments.delete(document.id);
    await this.indexDocument(document);
  }

  static isDocumentIndexed(documentId: string): boolean {
    return this.indexedDocuments.has(documentId);
  }

  static getIndexedDocumentCount(): number {
    return this.indexedDocuments.size;
  }

  static async extractTextFromFile(file: File): Promise<string> {
    // This is a simplified text extraction - in production you'd use proper parsers
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (event) => {
        const text = event.target?.result as string;
        resolve(text);
      };
      
      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };

      if (file.type === 'text/plain' || file.name.endsWith('.md')) {
        reader.readAsText(file);
      } else {
        // For other file types, you'd need specialized parsers
        // For now, just return filename as content
        resolve(`Document: ${file.name}\nType: ${file.type}\nSize: ${file.size} bytes`);
      }
    });
  }

  static async indexUploadedFile(file: File, projectId?: string, uploadedBy: string = 'current-user'): Promise<void> {
    try {
      const content = await this.extractTextFromFile(file);
      const document: IndexableDocument = {
        id: `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        title: file.name,
        content,
        type: this.getFileType(file),
        projectId,
        uploadedBy,
        uploadedAt: new Date().toISOString(),
        tags: this.generateTags(file.name, content)
      };

      await this.indexDocument(document);
    } catch (error) {
      console.error('Failed to index uploaded file:', error);
      throw error;
    }
  }

  private static getFileType(file: File): 'pdf' | 'docx' | 'txt' | 'md' {
    if (file.type === 'application/pdf') return 'pdf';
    if (file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') return 'docx';
    if (file.name.endsWith('.md')) return 'md';
    return 'txt';
  }

  private static generateTags(filename: string, content: string): string[] {
    const tags: string[] = [];
    
    // Generate tags based on filename
    const lowerFilename = filename.toLowerCase();
    if (lowerFilename.includes('safety')) tags.push('safety');
    if (lowerFilename.includes('manual')) tags.push('manual');
    if (lowerFilename.includes('spec')) tags.push('specifications');
    if (lowerFilename.includes('report')) tags.push('report');
    if (lowerFilename.includes('budget')) tags.push('budget');
    
    // Generate tags based on content (basic keyword detection)
    const lowerContent = content.toLowerCase();
    if (lowerContent.includes('ppe') || lowerContent.includes('safety equipment')) tags.push('ppe');
    if (lowerContent.includes('timeline') || lowerContent.includes('schedule')) tags.push('scheduling');
    if (lowerContent.includes('equipment') || lowerContent.includes('machinery')) tags.push('equipment');
    if (lowerContent.includes('concrete') || lowerContent.includes('foundation')) tags.push('concrete-work');
    
    return [...new Set(tags)]; // Remove duplicates
  }
}
