
// Stub service to fix build error
export class AutonomyService {
  static getInstance() {
    return new AutonomyService();
  }
  
  async getApprovals() {
    return [];
  }

  static async approveRequest(requestId: string, approvedBy: string): Promise<boolean> {
    console.log(`Approving request ${requestId} by ${approvedBy}`);
    return true;
  }

  static async rejectRequest(requestId: string, rejectedBy: string): Promise<boolean> {
    console.log(`Rejecting request ${requestId} by ${rejectedBy}`);
    return true;
  }
}
