
import { vectorDB, DocumentChunk, SearchResult } from './VectorDatabase';
import { LLMService } from './llm/LLMService';

export interface RAGResponse {
  answer: string;
  sources: SearchResult[];
  confidence: number;
  processingTime: number;
}

export class RAGService {
  private static chunkSize = 500;
  private static chunkOverlap = 50;

  static async indexDocument(content: string, metadata: any): Promise<void> {
    const chunks = this.splitIntoChunks(content, this.chunkSize, this.chunkOverlap);
    
    const documentChunks: DocumentChunk[] = chunks.map((chunk, index) => ({
      id: `${metadata.source}_chunk_${index}`,
      content: chunk,
      metadata: {
        ...metadata,
        chunkIndex: index,
        totalChunks: chunks.length
      }
    }));

    await vectorDB.addDocuments(documentChunks);
    console.log(`Indexed ${chunks.length} chunks from ${metadata.source}`);
  }

  static async queryWithContext(
    question: string, 
    projectId?: string,
    maxSources: number = 5
  ): Promise<RAGResponse> {
    const startTime = Date.now();

    try {
      // Search for relevant context
      const searchResults = await vectorDB.semanticSearch(question, undefined, projectId);
      
      if (searchResults.length === 0) {
        return {
          answer: "I don't have specific information about that in the knowledge base. Let me provide a general response.",
          sources: [],
          confidence: 0.1,
          processingTime: Date.now() - startTime
        };
      }

      // Prepare context from top results
      const context = searchResults
        .slice(0, maxSources)
        .map(result => `Source: ${result.chunk.metadata.source}\nContent: ${result.chunk.content}`)
        .join('\n\n---\n\n');

      // Create enhanced prompt
      const prompt = `Based on the following context from project documents and knowledge base, please answer the question. If the context doesn't contain enough information, say so and provide what you can.

Context:
${context}

Question: ${question}

Instructions:
- Answer based primarily on the provided context
- If context is insufficient, indicate this clearly
- Cite specific sources when possible
- Focus on construction and project management aspects
- Be concise but comprehensive

Answer:`;

      // Get LLM response
      const llmResponse = await LLMService.processRequest({
        task: 'conversation',
        prompt,
        context: { hasContext: true, sourceCount: searchResults.length }
      });

      return {
        answer: llmResponse.response,
        sources: searchResults,
        confidence: this.calculateConfidence(searchResults),
        processingTime: Date.now() - startTime
      };

    } catch (error) {
      console.error('RAG query failed:', error);
      return {
        answer: "I encountered an error while searching the knowledge base. Please try rephrasing your question.",
        sources: [],
        confidence: 0,
        processingTime: Date.now() - startTime
      };
    }
  }

  static async indexSampleDocuments(): Promise<void> {
    console.log('Indexing sample construction documents...');

    const sampleDocuments = [
      {
        content: `Safety Protocol for Construction Sites

1. Personal Protective Equipment (PPE)
- Hard hats must be worn at all times on site
- Safety glasses required in all work areas
- Steel-toed boots mandatory for all personnel
- High-visibility vests required in vehicle operation zones

2. Equipment Safety
- Daily equipment inspections required before use
- Lockout/tagout procedures for maintenance
- Crane operations require certified operators only
- Fall protection required for work above 6 feet

3. Emergency Procedures
- Emergency contact numbers posted in all areas
- First aid stations located every 100 feet
- Emergency evacuation routes clearly marked
- Regular safety drills conducted monthly`,
        metadata: {
          source: 'Safety Manual v2.1',
          type: 'document' as const,
          timestamp: new Date().toISOString(),
          tags: ['safety', 'ppe', 'emergency', 'equipment']
        }
      },
      {
        content: `Project Timeline and Milestones

Phase 1: Site Preparation (Weeks 1-4)
- Site survey and soil testing
- Permit acquisition and approvals
- Temporary facilities setup
- Access road construction

Phase 2: Foundation Work (Weeks 5-12)
- Excavation and grading
- Foundation pouring
- Utility rough-ins
- Basement waterproofing

Phase 3: Structural Work (Weeks 13-24)
- Steel frame erection
- Concrete work
- Roofing installation
- Exterior wall systems

Phase 4: Interior Build-out (Weeks 25-36)
- Electrical and plumbing rough-in
- HVAC installation
- Drywall and flooring
- Final finishes and fixtures`,
        metadata: {
          source: 'Project Timeline Document',
          type: 'project' as const,
          timestamp: new Date().toISOString(),
          projectId: 'downtown-office-complex',
          tags: ['timeline', 'milestones', 'phases', 'scheduling']
        }
      },
      {
        content: `Equipment Maintenance Guidelines

Daily Inspections:
- Check fluid levels (oil, hydraulic, coolant)
- Inspect tires for wear and proper inflation
- Test all safety devices and alarms
- Verify proper operation of all controls

Weekly Maintenance:
- Grease all lubrication points
- Check and clean air filters
- Inspect belts and hoses for wear
- Test backup alarms and lights

Monthly Service:
- Change engine oil and filters
- Inspect hydraulic systems for leaks
- Check track tension on tracked equipment
- Service transmission and differentials

Equipment-Specific Notes:
- Cranes require daily load testing
- Excavators need track inspection after every 250 hours
- Concrete mixers require thorough cleaning after each use`,
        metadata: {
          source: 'Equipment Manual',
          type: 'document' as const,
          timestamp: new Date().toISOString(),
          tags: ['equipment', 'maintenance', 'inspection', 'service']
        }
      }
    ];

    for (const doc of sampleDocuments) {
      await this.indexDocument(doc.content, doc.metadata);
    }

    console.log('Sample documents indexed successfully');
  }

  private static splitIntoChunks(text: string, chunkSize: number, overlap: number): string[] {
    const words = text.split(/\s+/);
    const chunks: string[] = [];
    
    for (let i = 0; i < words.length; i += chunkSize - overlap) {
      const chunk = words.slice(i, i + chunkSize).join(' ');
      chunks.push(chunk);
      
      if (i + chunkSize >= words.length) break;
    }
    
    return chunks;
  }

  private static calculateConfidence(results: SearchResult[]): number {
    if (results.length === 0) return 0;
    
    const avgSimilarity = results.reduce((sum, result) => sum + result.similarity, 0) / results.length;
    const sourceVariety = new Set(results.map(r => r.chunk.metadata.source)).size;
    
    // Confidence based on similarity and source diversity
    return Math.min(0.95, avgSimilarity * 0.7 + (sourceVariety / 5) * 0.3);
  }
}
