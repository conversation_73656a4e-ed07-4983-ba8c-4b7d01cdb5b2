
import { performanceMonitor } from './PerformanceMonitor';
import { logger } from './Logger';

interface PerformanceMetric {
  name: string;
  duration: number;
  timestamp: Date;
  metadata?: Record<string, any>;
}

class PerformanceTracker {
  private metrics: PerformanceMetric[] = [];
  private activeOperations = new Map<string, number>();

  public startOperation(name: string, metadata?: Record<string, any>): string {
    const operationId = `${name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.activeOperations.set(operationId, performance.now());
    
    logger.debug(`Started operation: ${name}`, { operationId, metadata });
    return operationId;
  }

  public endOperation(operationId: string, metadata?: Record<string, any>): PerformanceMetric | null {
    const startTime = this.activeOperations.get(operationId);
    if (!startTime) {
      logger.warn(`Operation ${operationId} not found`);
      return null;
    }

    const duration = performance.now() - startTime;
    const metric: PerformanceMetric = {
      name: operationId.split('_')[0],
      duration,
      timestamp: new Date(),
      metadata
    };

    this.metrics.push(metric);
    this.activeOperations.delete(operationId);

    // Log slow operations
    if (duration > 2000) {
      logger.warn(`Slow operation detected: ${metric.name}`, { duration, metadata });
    }

    logger.debug(`Completed operation: ${metric.name}`, { duration, metadata });
    return metric;
  }

  public async measureAsync<T>(
    name: string, 
    operation: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    const operationId = this.startOperation(name, metadata);
    
    try {
      const result = await operation();
      this.endOperation(operationId, { ...metadata, success: true });
      return result;
    } catch (error) {
      this.endOperation(operationId, { ...metadata, success: false, error: error instanceof Error ? error.message : 'Unknown error' });
      throw error;
    }
  }

  public measure<T>(
    name: string,
    operation: () => T,
    metadata?: Record<string, any>
  ): T {
    const operationId = this.startOperation(name, metadata);
    
    try {
      const result = operation();
      this.endOperation(operationId, { ...metadata, success: true });
      return result;
    } catch (error) {
      this.endOperation(operationId, { ...metadata, success: false, error: error instanceof Error ? error.message : 'Unknown error' });
      throw error;
    }
  }

  public getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  public getMetricsByName(name: string): PerformanceMetric[] {
    return this.metrics.filter(metric => metric.name === name);
  }

  public getAverageTime(name: string): number {
    const metrics = this.getMetricsByName(name);
    if (metrics.length === 0) return 0;
    
    const total = metrics.reduce((sum, metric) => sum + metric.duration, 0);
    return total / metrics.length;
  }

  public clearMetrics(): void {
    this.metrics = [];
  }

  public getSummary(): Record<string, { count: number; averageTime: number; maxTime: number; minTime: number }> {
    const summary: Record<string, { count: number; averageTime: number; maxTime: number; minTime: number }> = {};
    
    this.metrics.forEach(metric => {
      if (!summary[metric.name]) {
        summary[metric.name] = {
          count: 0,
          averageTime: 0,
          maxTime: 0,
          minTime: Infinity
        };
      }
      
      const stat = summary[metric.name];
      stat.count++;
      stat.maxTime = Math.max(stat.maxTime, metric.duration);
      stat.minTime = Math.min(stat.minTime, metric.duration);
    });
    
    // Calculate averages
    Object.keys(summary).forEach(name => {
      const metrics = this.getMetricsByName(name);
      summary[name].averageTime = this.getAverageTime(name);
    });
    
    return summary;
  }
}

export const performanceTracker = new PerformanceTracker();
