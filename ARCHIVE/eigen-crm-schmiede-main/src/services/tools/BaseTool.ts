
import type { ToolDefinition, ToolExecutionRequest, ToolExecutionResult } from '@/types/tools'

export abstract class BaseTool {
  protected definition: ToolDefinition

  constructor(definition: ToolDefinition) {
    this.definition = definition
  }

  getDefinition(): ToolDefinition {
    return this.definition
  }

  abstract execute(request: ToolExecutionRequest): Promise<ToolExecutionResult>
  
  abstract validate(parameters: Record<string, any>): Promise<boolean>
  
  abstract configure(config: Record<string, any>): Promise<boolean>

  protected validateParameters(parameters: Record<string, any>): string[] {
    const errors: string[] = []
    
    for (const param of this.definition.parameters) {
      if (param.required && !(param.name in parameters)) {
        errors.push(`Required parameter '${param.name}' is missing`)
      }
      
      if (param.name in parameters) {
        const value = parameters[param.name]
        const expectedType = param.type
        
        if (expectedType === 'string' && typeof value !== 'string') {
          errors.push(`Parameter '${param.name}' must be a string`)
        } else if (expectedType === 'number' && typeof value !== 'number') {
          errors.push(`Parameter '${param.name}' must be a number`)
        } else if (expectedType === 'boolean' && typeof value !== 'boolean') {
          errors.push(`Parameter '${param.name}' must be a boolean`)
        }
      }
    }
    
    return errors
  }
}
