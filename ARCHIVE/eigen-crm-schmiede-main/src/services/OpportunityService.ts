
import { supabase, isSupabaseConfigured } from '@/lib/supabase';
import type { Database } from '@/lib/supabase';

type Tables = Database['public']['Tables'];
type Opportunity = Tables['opportunities']['Row'];

export class OpportunityService {
  private static isConfigured = isSupabaseConfigured();

  static async getOpportunities(): Promise<Opportunity[]> {
    if (!this.isConfigured) {
      return this.getMockOpportunities();
    }

    try {
      const { data, error } = await supabase
        .from('opportunities')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching opportunities:', error);
      return this.getMockOpportunities();
    }
  }

  static async createOpportunity(opportunity: Tables['opportunities']['Insert']): Promise<Opportunity | null> {
    if (!this.isConfigured) {
      console.log('Mock: Creating opportunity:', opportunity);
      return { 
        ...opportunity, 
        id: `mock-${Date.now()}`, 
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      } as Opportunity;
    }

    try {
      const { data, error } = await supabase
        .from('opportunities')
        .insert(opportunity)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating opportunity:', error);
      return null;
    }
  }

  static async updateOpportunity(id: string, updates: Tables['opportunities']['Update']): Promise<Opportunity | null> {
    if (!this.isConfigured) {
      console.log('Mock: Updating opportunity:', { id, updates });
      return { 
        ...updates, 
        id, 
        updated_at: new Date().toISOString()
      } as Opportunity;
    }

    try {
      const { data, error } = await supabase
        .from('opportunities')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating opportunity:', error);
      return null;
    }
  }

  static async deleteOpportunity(id: string): Promise<boolean> {
    if (!this.isConfigured) {
      console.log('Mock: Deleting opportunity:', id);
      return true;
    }

    try {
      const { error } = await supabase
        .from('opportunities')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error deleting opportunity:', error);
      return false;
    }
  }

  private static getMockOpportunities(): Opportunity[] {
    return [
      {
        id: 'mock-1',
        title: 'Downtown Office Complex',
        value: 250000,
        stage: 'proposal',
        probability: 70,
        expected_close_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        company_id: 'mock-1',
        contact_id: 'mock-1',
        description: 'Construction of new office complex in downtown area',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }
}
