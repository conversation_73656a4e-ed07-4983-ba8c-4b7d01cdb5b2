
import { supabase, isSupabaseConfigured } from '@/lib/supabase';

interface Employee {
  id: string;
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  position: string;
  department: string;
  hire_date: string;
  status: 'active' | 'inactive' | 'terminated';
  skills: string[];
  certifications: any[];
  emergency_contact?: any;
  created_at: string;
  updated_at: string;
}

interface Training {
  id: string;
  title: string;
  description: string;
  type: 'safety' | 'technical' | 'compliance' | 'soft_skills';
  duration_hours: number;
  instructor: string;
  max_participants: number;
  scheduled_date: string;
  location: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  participants: string[];
  created_at: string;
}

export class HRService {
  private static isConfigured = isSupabaseConfigured();

  static async getEmployees(): Promise<Employee[]> {
    if (!this.isConfigured) {
      return this.getMockEmployees();
    }

    try {
      const { data, error } = await supabase
        .from('employees')
        .select('*')
        .order('last_name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching employees:', error);
      return this.getMockEmployees();
    }
  }

  static async createEmployee(employee: Omit<Employee, 'id' | 'created_at' | 'updated_at'>): Promise<Employee | null> {
    if (!this.isConfigured) {
      console.log('Mock: Creating employee:', employee);
      return { 
        ...employee, 
        id: `mock-${Date.now()}`, 
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      } as Employee;
    }

    try {
      const { data, error } = await supabase
        .from('employees')
        .insert(employee)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating employee:', error);
      return null;
    }
  }

  static async updateEmployee(id: string, updates: Partial<Employee>): Promise<Employee | null> {
    if (!this.isConfigured) {
      console.log('Mock: Updating employee:', { id, updates });
      return { 
        ...updates, 
        id, 
        updated_at: new Date().toISOString()
      } as Employee;
    }

    try {
      const { data, error } = await supabase
        .from('employees')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating employee:', error);
      return null;
    }
  }

  static async getTrainings(): Promise<Training[]> {
    if (!this.isConfigured) {
      return this.getMockTrainings();
    }

    try {
      const { data, error } = await supabase
        .from('trainings')
        .select('*')
        .order('scheduled_date', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching trainings:', error);
      return this.getMockTrainings();
    }
  }

  private static getMockEmployees(): Employee[] {
    return [
      {
        id: 'emp-1',
        employee_id: 'EMP-001',
        first_name: 'John',
        last_name: 'Smith',
        email: '<EMAIL>',
        phone: '******-0123',
        position: 'Project Manager',
        department: 'Construction',
        hire_date: '2023-01-15',
        status: 'active',
        skills: ['Project Management', 'Safety Compliance', 'Team Leadership'],
        certifications: [
          { name: 'PMP Certification', issued_date: '2023-05-01', expires_date: '2026-05-01' }
        ],
        emergency_contact: {
          name: 'Jane Smith',
          relationship: 'Spouse',
          phone: '******-0124'
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }

  private static getMockTrainings(): Training[] {
    return [
      {
        id: 'training-1',
        title: 'Safety Awareness Training',
        description: 'Comprehensive safety training for construction workers',
        type: 'safety',
        duration_hours: 8,
        instructor: 'Safety Officer Johnson',
        max_participants: 20,
        scheduled_date: '2024-03-01T09:00:00Z',
        location: 'Training Room A',
        status: 'scheduled',
        participants: ['emp-1'],
        created_at: new Date().toISOString()
      }
    ];
  }
}
