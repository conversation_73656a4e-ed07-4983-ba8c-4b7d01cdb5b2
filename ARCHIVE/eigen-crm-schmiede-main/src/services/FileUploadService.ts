
import { supabase, isSupabaseConfigured } from '@/lib/supabase';
import { DocumentIndexingService } from './DocumentIndexingService';
import { logger } from './Logger';

export interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
  projectId?: string;
  uploadedBy: string;
  uploadedAt: string;
  status: 'uploading' | 'completed' | 'failed';
}

export class FileUploadService {
  static async uploadFile(
    file: File, 
    bucket: string = 'documents',
    projectId?: string
  ): Promise<UploadedFile> {
    if (!isSupabaseConfigured()) {
      logger.info('Using mock upload for demo', { fileName: file.name, projectId });
      return this.mockUpload(file, projectId);
    }

    try {
      const fileName = `${Date.now()}_${file.name}`;
      const filePath = projectId ? `${projectId}/${fileName}` : fileName;

      logger.info('Starting file upload', { fileName, filePath, size: file.size });

      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) throw error;

      const { data: urlData } = supabase.storage
        .from(bucket)
        .getPublicUrl(data.path);

      const uploadedFile: UploadedFile = {
        id: data.path,
        name: file.name,
        size: file.size,
        type: file.type,
        url: urlData.publicUrl,
        projectId,
        uploadedBy: 'current-user',
        uploadedAt: new Date().toISOString(),
        status: 'completed'
      };

      logger.logServiceCall('FileUploadService', 'uploadFile', true, { 
        fileName: file.name, 
        fileId: uploadedFile.id 
      });

      // Index document for RAG if it's a text file
      if (this.isIndexableFile(file)) {
        try {
          await DocumentIndexingService.indexUploadedFile(file, projectId);
          logger.info('Document indexed successfully', { fileName: file.name });
        } catch (indexError) {
          logger.warn('Failed to index file', { fileName: file.name, error: indexError });
        }
      }

      return uploadedFile;
    } catch (error) {
      logger.logServiceCall('FileUploadService', 'uploadFile', false, { 
        fileName: file.name, 
        error 
      });
      throw error;
    }
  }

  static async deleteFile(filePath: string, bucket: string = 'documents'): Promise<void> {
    if (!isSupabaseConfigured()) {
      logger.info('Mock delete file', { filePath });
      return;
    }

    const { error } = await supabase.storage
      .from(bucket)
      .remove([filePath]);

    if (error) {
      logger.logServiceCall('FileUploadService', 'deleteFile', false, { filePath, error });
      throw error;
    }

    logger.logServiceCall('FileUploadService', 'deleteFile', true, { filePath });
  }

  static async getFiles(projectId?: string, bucket: string = 'documents'): Promise<UploadedFile[]> {
    if (!isSupabaseConfigured()) {
      return this.getMockFiles();
    }

    try {
      const path = projectId || '';
      const { data, error } = await supabase.storage
        .from(bucket)
        .list(path);

      if (error) throw error;

      const files = data?.map(file => ({
        id: file.name,
        name: file.name.split('_').slice(1).join('_'),
        size: file.metadata?.size || 0,
        type: file.metadata?.mimetype || 'unknown',
        url: supabase.storage.from(bucket).getPublicUrl(`${path}/${file.name}`).data.publicUrl,
        projectId,
        uploadedBy: 'unknown',
        uploadedAt: file.created_at || new Date().toISOString(),
        status: 'completed' as const
      })) || [];

      logger.logServiceCall('FileUploadService', 'getFiles', true, { 
        projectId, 
        fileCount: files.length 
      });

      return files;
    } catch (error) {
      logger.logServiceCall('FileUploadService', 'getFiles', false, { projectId, error });
      return [];
    }
  }

  private static mockUpload(file: File, projectId?: string): UploadedFile {
    return {
      id: `mock_${Date.now()}`,
      name: file.name,
      size: file.size,
      type: file.type,
      url: URL.createObjectURL(file),
      projectId,
      uploadedBy: 'demo-user',
      uploadedAt: new Date().toISOString(),
      status: 'completed'
    };
  }

  private static getMockFiles(): UploadedFile[] {
    return [
      {
        id: 'mock_1',
        name: 'project_specifications.pdf',
        size: 2458000,
        type: 'application/pdf',
        url: '/placeholder.svg',
        uploadedBy: 'john.doe',
        uploadedAt: '2024-01-15T10:30:00Z',
        status: 'completed'
      },
      {
        id: 'mock_2',
        name: 'safety_manual.docx',
        size: 1234000,
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        url: '/placeholder.svg',
        uploadedBy: 'jane.smith',
        uploadedAt: '2024-01-10T14:20:00Z',
        status: 'completed'
      }
    ];
  }

  private static isIndexableFile(file: File): boolean {
    const indexableTypes = [
      'text/plain',
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    return indexableTypes.includes(file.type) || file.name.endsWith('.md');
  }
}
