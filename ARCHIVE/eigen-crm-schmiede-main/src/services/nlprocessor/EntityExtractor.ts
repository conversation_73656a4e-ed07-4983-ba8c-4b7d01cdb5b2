
import { CommandPattern } from './CommandPatterns';

export class EntityExtractor {
  extractEntities(match: RegExpMatchArray, pattern: CommandPattern): Record<string, any> {
    const entities: Record<string, any> = {};
    
    if (pattern.entities.includes('company') && match[1]) {
      entities.company = match[1].trim();
    }
    if (pattern.entities.includes('filter') && match[2]) {
      entities.filter = match[2].trim();
    }
    if (pattern.entities.includes('target') && match[1]) {
      entities.target = match[1].trim();
    }
    if (pattern.entities.includes('contact') && match[2]) {
      entities.contact = match[2].trim();
    }
    if (pattern.entities.includes('recipient') && match[2]) {
      entities.recipient = match[2].trim();
    }
    if (pattern.entities.includes('amount') && match[3]) {
      let amount = parseInt(match[3]);
      const unit = match[4]?.toLowerCase();
      if (unit === 'k') amount *= 1000;
      if (unit === 'm') amount *= 1000000;
      entities.amount = amount;
    }

    return entities;
  }
}
