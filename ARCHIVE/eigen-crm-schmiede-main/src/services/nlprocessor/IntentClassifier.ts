
import { FuzzyMatcher } from './FuzzyMatcher';
import { ContextManager } from './ContextManager';

export interface IntentDefinition {
  name: string;
  patterns: string[];
  keywords: string[];
  examples: string[];
  confidence_threshold: number;
  context_dependent: boolean;
}

export interface ClassificationResult {
  intent: string;
  confidence: number;
  entities: Record<string, any>;
  reasoning: string[];
}

export class IntentClassifier {
  private intents: IntentDefinition[] = [
    {
      name: 'research_company',
      patterns: ['research *', 'find information about *', 'tell me about *', 'what do you know about *'],
      keywords: ['research', 'company', 'information', 'details', 'background'],
      examples: ['Research Apple Inc', 'Tell me about Microsoft', 'Find information about Tesla'],
      confidence_threshold: 0.7,
      context_dependent: false
    },
    {
      name: 'list_leads',
      patterns: ['show leads', 'list contacts', 'find people', 'get leads *'],
      keywords: ['leads', 'contacts', 'people', 'prospects', 'list', 'show'],
      examples: ['Show me all leads', 'List contacts from last week', 'Find people in tech'],
      confidence_threshold: 0.8,
      context_dependent: true
    },
    {
      name: 'qualify_lead',
      patterns: ['qualify *', 'score *', 'evaluate *', 'assess *'],
      keywords: ['qualify', 'score', 'evaluate', 'assess', 'lead', 'prospect'],
      examples: ['Qualify this lead', 'Score John Smith', 'Evaluate this prospect'],
      confidence_threshold: 0.8,
      context_dependent: true
    },
    {
      name: 'make_call',
      patterns: ['call *', 'phone *', 'contact *', 'reach out to *'],
      keywords: ['call', 'phone', 'contact', 'reach', 'dial'],
      examples: ['Call Sarah Johnson', 'Phone the client', 'Contact the prospect'],
      confidence_threshold: 0.8,
      context_dependent: false
    },
    {
      name: 'schedule_meeting',
      patterns: ['schedule *', 'book *', 'arrange meeting *', 'set up call *'],
      keywords: ['schedule', 'book', 'meeting', 'appointment', 'call', 'arrange'],
      examples: ['Schedule a meeting with John', 'Book a call for tomorrow', 'Arrange meeting'],
      confidence_threshold: 0.8,
      context_dependent: true
    },
    {
      name: 'dashboard_control',
      patterns: ['show dashboard', 'open *', 'navigate to *', 'switch to *'],
      keywords: ['dashboard', 'show', 'open', 'navigate', 'switch', 'view'],
      examples: ['Show dashboard', 'Open opportunities', 'Navigate to people'],
      confidence_threshold: 0.7,
      context_dependent: false
    }
  ];

  classify(message: string, contextManager?: ContextManager): ClassificationResult {
    const normalizedMessage = message.toLowerCase().trim();
    const keywords = FuzzyMatcher.extractKeywords(normalizedMessage);
    
    let bestMatch: ClassificationResult = {
      intent: 'unknown',
      confidence: 0,
      entities: {},
      reasoning: ['No matching intent found']
    };

    for (const intent of this.intents) {
      const result = this.scoreIntent(normalizedMessage, keywords, intent, contextManager);
      
      if (result.confidence > bestMatch.confidence && result.confidence >= intent.confidence_threshold) {
        bestMatch = result;
      }
    }

    return bestMatch;
  }

  private scoreIntent(
    message: string, 
    keywords: string[], 
    intent: IntentDefinition, 
    contextManager?: ContextManager
  ): ClassificationResult {
    let score = 0;
    const reasoning: string[] = [];
    const entities: Record<string, any> = {};

    // Pattern matching
    for (const pattern of intent.patterns) {
      const regex = new RegExp(pattern.replace('*', '(.*)'), 'i');
      const match = message.match(regex);
      if (match) {
        score += 0.4;
        reasoning.push(`Matched pattern: "${pattern}"`);
        
        // Extract entities from capture groups
        if (match[1]) {
          entities.target = match[1].trim();
        }
        break;
      }
    }

    // Keyword matching
    const matchedKeywords = intent.keywords.filter(keyword => 
      keywords.some(k => FuzzyMatcher.similarity(k, keyword) > 0.8)
    );
    
    if (matchedKeywords.length > 0) {
      score += (matchedKeywords.length / intent.keywords.length) * 0.3;
      reasoning.push(`Matched keywords: ${matchedKeywords.join(', ')}`);
    }

    // Context boost
    if (intent.context_dependent && contextManager) {
      const currentTopic = contextManager.getCurrentTopic();
      const recentEntities = contextManager.getRecentEntities();
      
      if (currentTopic && this.isRelatedTopic(intent.name, currentTopic)) {
        score += 0.2;
        reasoning.push(`Context boost: related to current topic "${currentTopic}"`);
      }
      
      // Add recent entities if relevant
      if (Object.keys(recentEntities).length > 0) {
        entities.context = recentEntities;
      }
    }

    // Fuzzy matching against examples
    const bestExampleMatch = intent.examples
      .map(example => FuzzyMatcher.similarity(message, example))
      .reduce((max, current) => Math.max(max, current), 0);
    
    if (bestExampleMatch > 0.6) {
      score += bestExampleMatch * 0.1;
      reasoning.push(`Similar to training example (${Math.round(bestExampleMatch * 100)}% match)`);
    }

    return {
      intent: intent.name,
      confidence: Math.min(score, 1.0),
      entities,
      reasoning
    };
  }

  private isRelatedTopic(intent: string, currentTopic: string): boolean {
    const relationships: Record<string, string[]> = {
      'lead_management': ['qualify_lead', 'list_leads'],
      'company_research': ['research_company'],
      'communications': ['make_call', 'schedule_meeting'],
      'deal_management': ['list_opportunities']
    };

    return relationships[currentTopic]?.includes(intent) || false;
  }
}
