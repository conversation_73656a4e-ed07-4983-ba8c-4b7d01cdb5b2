
export interface CommandPattern {
  regex: RegExp;
  intent: string;
  entities: string[];
  agent?: string;
  action?: string;
  priority?: number;
  category?: string;
}

export const commandPatterns: CommandPattern[] = [
  // Project management
  {
    regex: /show|list|find (projects|jobs) (.*)/i,
    intent: 'list_projects',
    entities: ['filter'],
    agent: 'project-manager',
    category: 'project_management',
    priority: 1
  },
  {
    regex: /(create|start|new) project (.*?)(?:\s|$)/i,
    intent: 'create_project',
    entities: ['project_name'],
    agent: 'project-manager',
    action: 'create_project',
    category: 'project_management',
    priority: 1
  },
  {
    regex: /(update|modify) project (.*?) (budget|timeline|status|phase) (.*?)(?:\s|$)/i,
    intent: 'update_project',
    entities: ['project', 'field', 'value'],
    agent: 'project-manager',
    action: 'update_project',
    category: 'project_management',
    priority: 1
  },
  {
    regex: /project.*?(status|progress|update) (.*)/i,
    intent: 'project_status',
    entities: ['project'],
    agent: 'project-manager',
    action: 'get_project_status',
    category: 'project_management',
    priority: 1
  },
  
  // HR & Workforce management
  {
    regex: /show|list|find (employees|workers|staff|team) (.*)/i,
    intent: 'list_employees',
    entities: ['filter'],
    agent: 'hr-manager',
    category: 'hr_management',
    priority: 1
  },
  {
    regex: /(hire|add|recruit) (employee|worker|staff) (.*?)(?:\s|$)/i,
    intent: 'hire_employee',
    entities: ['employee_name'],
    agent: 'hr-manager',
    action: 'hire_employee',
    category: 'hr_management',
    priority: 1
  },
  {
    regex: /schedule.*?(training|certification|course).*(for|to) (.*?)(?:\s|$)/i,
    intent: 'schedule_training',
    entities: ['employee'],
    agent: 'hr-manager',
    action: 'schedule_training',
    category: 'hr_management',
    priority: 1
  },
  {
    regex: /(timesheet|hours|attendance) (.*)/i,
    intent: 'manage_timesheet',
    entities: ['employee'],
    agent: 'hr-manager',
    action: 'manage_timesheet',
    category: 'hr_management',
    priority: 1
  },
  {
    regex: /performance.*?(review|evaluation) (.*)/i,
    intent: 'performance_review',
    entities: ['employee'],
    agent: 'hr-manager',
    action: 'performance_review',
    category: 'hr_management',
    priority: 1
  },
  
  // Finance & Billing
  {
    regex: /show|list|find (invoices|bills|payments) (.*)/i,
    intent: 'list_invoices',
    entities: ['filter'],
    agent: 'finance-manager',
    category: 'finance',
    priority: 1
  },
  {
    regex: /(create|generate|send) invoice.*(for|to) (.*?)(?:\s|$)/i,
    intent: 'create_invoice',
    entities: ['client'],
    agent: 'finance-manager',
    action: 'create_invoice',
    category: 'finance',
    priority: 1
  },
  {
    regex: /show.*?(expenses|costs|budget) (.*)/i,
    intent: 'show_expenses',
    entities: ['filter'],
    agent: 'finance-manager',
    category: 'finance',
    priority: 1
  },
  {
    regex: /cash.*?flow|profit.*?loss|financial.*?report/i,
    intent: 'financial_report',
    entities: [],
    agent: 'finance-manager',
    action: 'generate_report',
    category: 'finance',
    priority: 1
  },
  {
    regex: /(approve|reject) (payment|expense|invoice) (.*?)(?:\s|$)/i,
    intent: 'approve_payment',
    entities: ['item'],
    agent: 'finance-manager',
    action: 'approve_payment',
    category: 'finance',
    priority: 1
  },
  
  // Logistics & Supply Chain
  {
    regex: /show|list|find (deliveries|shipments|materials) (.*)/i,
    intent: 'list_deliveries',
    entities: ['filter'],
    agent: 'logistics-manager',
    category: 'logistics',
    priority: 1
  },
  {
    regex: /schedule.*?delivery.*(for|to) (.*?)(?:\s|$)/i,
    intent: 'schedule_delivery',
    entities: ['destination'],
    agent: 'logistics-manager',
    action: 'schedule_delivery',
    category: 'logistics',
    priority: 1
  },
  {
    regex: /check.*?(inventory|stock|materials) (.*)/i,
    intent: 'check_inventory',
    entities: ['material'],
    agent: 'logistics-manager',
    action: 'check_inventory',
    category: 'logistics',
    priority: 1
  },
  {
    regex: /(restock|order|purchase) (.*?)(?:\s|$)/i,
    intent: 'restock_material',
    entities: ['material'],
    agent: 'logistics-manager',
    action: 'restock_material',
    category: 'logistics',
    priority: 1
  },
  {
    regex: /supplier.*?(contact|order|quote) (.*)/i,
    intent: 'supplier_management',
    entities: ['supplier'],
    agent: 'logistics-manager',
    action: 'manage_supplier',
    category: 'logistics',
    priority: 1
  },
  
  // Safety & Compliance
  {
    regex: /report.*?(incident|accident|safety|injury) (.*)/i,
    intent: 'report_incident',
    entities: ['description'],
    agent: 'safety-manager',
    action: 'report_incident',
    category: 'safety',
    priority: 2
  },
  {
    regex: /schedule.*?(inspection|safety check|audit) (.*)/i,
    intent: 'schedule_inspection',
    entities: ['location'],
    agent: 'safety-manager',
    action: 'schedule_inspection',
    category: 'safety',
    priority: 1
  },
  {
    regex: /check.*?(certification|permit|license|compliance) (.*)/i,
    intent: 'check_certification',
    entities: ['item'],
    agent: 'safety-manager',
    action: 'check_certification',
    category: 'safety',
    priority: 1
  },
  {
    regex: /safety.*?(training|briefing|meeting) (.*)/i,
    intent: 'safety_training',
    entities: ['topic'],
    agent: 'safety-manager',
    action: 'safety_training',
    category: 'safety',
    priority: 1
  },
  {
    regex: /(ppe|equipment|gear).*?(check|inspection|inventory) (.*)/i,
    intent: 'ppe_management',
    entities: ['location'],
    agent: 'safety-manager',
    action: 'manage_ppe',
    category: 'safety',
    priority: 1
  },
  
  // Document Management
  {
    regex: /upload.*?(document|file|blueprint|contract) (.*)/i,
    intent: 'upload_document',
    entities: ['document_type'],
    agent: 'document-manager',
    action: 'upload_document',
    category: 'documents',
    priority: 1
  },
  {
    regex: /find.*?(contract|permit|blueprint|document|file) (.*)/i,
    intent: 'find_document',
    entities: ['document_type', 'search_term'],
    agent: 'document-manager',
    action: 'find_document',
    category: 'documents',
    priority: 1
  },
  {
    regex: /renew.*?(permit|license|certification|contract) (.*)/i,
    intent: 'renew_permit',
    entities: ['permit_type'],
    agent: 'document-manager',
    action: 'renew_permit',
    category: 'documents',
    priority: 1
  },
  {
    regex: /(sign|approve|review) (contract|document) (.*)/i,
    intent: 'document_approval',
    entities: ['document'],
    agent: 'document-manager',
    action: 'approve_document',
    category: 'documents',
    priority: 1
  },
  {
    regex: /version.*?(control|history) (.*)/i,
    intent: 'document_version',
    entities: ['document'],
    agent: 'document-manager',
    action: 'manage_versions',
    category: 'documents',
    priority: 1
  },
  
  // Communication
  {
    regex: /send.*?(message|announcement|notification) (.*)/i,
    intent: 'send_message',
    entities: ['content'],
    agent: 'communication-manager',
    action: 'send_message',
    category: 'communication',
    priority: 1
  },
  {
    regex: /create.*?(channel|group|team) (.*)/i,
    intent: 'create_channel',
    entities: ['channel_name'],
    agent: 'communication-manager',
    action: 'create_channel',
    category: 'communication',
    priority: 1
  },
  {
    regex: /schedule.*?(meeting|call|conference) (.*)/i,
    intent: 'schedule_meeting',
    entities: ['meeting_details'],
    agent: 'communication-manager',
    action: 'schedule_meeting',
    category: 'communication',
    priority: 1
  },
  {
    regex: /(broadcast|announce) (.*)/i,
    intent: 'broadcast_message',
    entities: ['message'],
    agent: 'communication-manager',
    action: 'broadcast_message',
    category: 'communication',
    priority: 1
  },
  {
    regex: /team.*?(update|standup|sync) (.*)/i,
    intent: 'team_communication',
    entities: ['team'],
    agent: 'communication-manager',
    action: 'team_update',
    category: 'communication',
    priority: 1
  },
  
  // Quality Control
  {
    regex: /quality.*?(inspection|check|audit) (.*)/i,
    intent: 'quality_inspection',
    entities: ['location'],
    agent: 'quality-manager',
    action: 'quality_inspection',
    category: 'quality',
    priority: 1
  },
  {
    regex: /report.*?(defect|issue|problem|failure) (.*)/i,
    intent: 'report_defect',
    entities: ['description'],
    agent: 'quality-manager',
    action: 'report_defect',
    category: 'quality',
    priority: 2
  },
  {
    regex: /approve.*?(work|phase|milestone|deliverable) (.*)/i,
    intent: 'approve_work',
    entities: ['work_item'],
    agent: 'quality-manager',
    action: 'approve_work',
    category: 'quality',
    priority: 1
  },
  {
    regex: /test.*?(material|concrete|steel|quality) (.*)/i,
    intent: 'test_material',
    entities: ['material_type'],
    agent: 'quality-manager',
    action: 'test_material',
    category: 'quality',
    priority: 1
  },
  {
    regex: /(rework|correction|fix) (.*)/i,
    intent: 'quality_rework',
    entities: ['item'],
    agent: 'quality-manager',
    action: 'manage_rework',
    category: 'quality',
    priority: 1
  },
  
  // Company research
  {
    regex: /research|find|tell me about|information on|details about (.*?)(?:\s|$)/i,
    intent: 'research_company',
    entities: ['company'],
    agent: 'research-agent',
    action: 'company_research',
    category: 'research',
    priority: 1
  },
  
  // Lead management
  {
    regex: /show|list|find (leads|contacts|people|prospects) (.*)/i,
    intent: 'list_leads',
    entities: ['filter'],
    agent: 'lead-qualifier',
    category: 'lead_management',
    priority: 1
  },
  {
    regex: /qualify|score (.*?)(?:\s|$)/i,
    intent: 'qualify_lead',
    entities: ['target'],
    agent: 'lead-qualifier',
    action: 'qualify_lead',
    category: 'lead_management',
    priority: 1
  },
  
  // Opportunities
  {
    regex: /show|list|find (deals|opportunities|bids) (.*)/i,
    intent: 'list_opportunities',
    entities: ['filter'],
    category: 'opportunities',
    priority: 1
  },
  {
    regex: /(deals|opportunities) (above|over|greater than) (\d+)([kmKM]?)/i,
    intent: 'filter_opportunities_by_value',
    entities: ['amount', 'currency'],
    category: 'opportunities',
    priority: 1
  },
  
  // Fleet management - Enhanced
  {
    regex: /show|list|find (vehicles|fleet|trucks|equipment) (.*)/i,
    intent: 'list_vehicles',
    entities: ['filter'],
    agent: 'fleet-manager',
    category: 'fleet',
    priority: 1
  },
  {
    regex: /track|locate|where is (vehicle|truck|van|equipment) (.*?)(?:\s|$)/i,
    intent: 'track_vehicle',
    entities: ['vehicle'],
    agent: 'fleet-manager',
    action: 'track_vehicle',
    category: 'fleet',
    priority: 1
  },
  {
    regex: /schedule.*?maintenance.*(for|on) (.*?)(?:\s|$)/i,
    intent: 'schedule_maintenance',
    entities: ['vehicle'],
    agent: 'fleet-manager',
    action: 'schedule_maintenance',
    category: 'fleet',
    priority: 1
  },
  {
    regex: /optimize.*?route.*(for|to) (.*?)(?:\s|$)/i,
    intent: 'optimize_route',
    entities: ['destination'],
    agent: 'fleet-manager',
    action: 'optimize_route',
    category: 'fleet',
    priority: 1
  },
  {
    regex: /fuel.*?(efficiency|consumption|usage|cost) (.*)/i,
    intent: 'fuel_analysis',
    entities: ['filter'],
    agent: 'fleet-manager',
    action: 'analyze_fuel',
    category: 'fleet',
    priority: 1
  },
  {
    regex: /(assign|dispatch) (vehicle|truck|equipment) (.*)/i,
    intent: 'assign_vehicle',
    entities: ['vehicle', 'assignment'],
    agent: 'fleet-manager',
    action: 'assign_vehicle',
    category: 'fleet',
    priority: 1
  },
  
  // Phone calls
  {
    regex: /call|phone (.*?)(?:\s|$)/i,
    intent: 'make_call',
    entities: ['target'],
    agent: 'phone-agent',
    action: 'make_call',
    category: 'communication',
    priority: 1
  },
  {
    regex: /schedule.*?call.*(with|for) (.*?)(?:\s|$)/i,
    intent: 'schedule_call',
    entities: ['contact'],
    agent: 'phone-agent',
    action: 'schedule_call',
    category: 'communication',
    priority: 1
  },
  
  // Automation
  {
    regex: /send.*?email.*(to|for) (.*?)(?:\s|$)/i,
    intent: 'send_email',
    entities: ['recipient'],
    agent: 'automation-agent',
    action: 'send_email',
    category: 'automation',
    priority: 1
  },
  {
    regex: /automate.*?(task|process|workflow) (.*)/i,
    intent: 'create_automation',
    entities: ['process'],
    agent: 'automation-agent',
    action: 'create_automation',
    category: 'automation',
    priority: 1
  },
  
  // General queries
  {
    regex: /what.*?(happening|new|recent|updates)/i,
    intent: 'get_updates',
    entities: [],
    category: 'general',
    priority: 1
  },
  {
    regex: /help|assistance|what can you do|commands/i,
    intent: 'help',
    entities: [],
    category: 'general',
    priority: 1
  },
  {
    regex: /system.*?(status|health|performance)/i,
    intent: 'system_status',
    entities: [],
    category: 'system',
    priority: 1
  },
  
  // Advanced AI queries
  {
    regex: /(analyze|predict|forecast) (.*)/i,
    intent: 'ai_analysis',
    entities: ['subject'],
    agent: 'analytics-agent',
    action: 'perform_analysis',
    category: 'analytics',
    priority: 1
  },
  {
    regex: /generate.*?(report|summary|insight) (.*)/i,
    intent: 'generate_report',
    entities: ['report_type'],
    agent: 'analytics-agent',
    action: 'generate_report',
    category: 'analytics',
    priority: 1
  }
];

// Helper function to get patterns by category
export const getPatternsByCategory = (category: string): CommandPattern[] => {
  return commandPatterns.filter(pattern => pattern.category === category);
};

// Helper function to get patterns by priority
export const getPatternsByPriority = (priority: number): CommandPattern[] => {
  return commandPatterns.filter(pattern => pattern.priority === priority);
};

// Helper function to get all categories
export const getCategories = (): string[] => {
  return [...new Set(commandPatterns.map(pattern => pattern.category).filter(Boolean))];
};
