
import { agent<PERSON>anager } from '../AgentManager';
import { LLMService, LLMRequest } from '../llm/LLMService';
import { toolExecutor } from '../tools/ToolExecutor';

export class CommandExecutor {
  async executeCommand(command: any): Promise<any> {
    console.log('Executing command:', command);

    try {
      switch (command.intent) {
        case 'research_company':
          return await this.executeResearchCommand(command);
        case 'qualify_lead':
          return await this.executeQualifyCommand(command);
        case 'make_call':
          return await this.executeCallCommand(command);
        case 'list_opportunities':
          return await this.executeOpportunityListCommand(command);
        case 'filter_opportunities_by_value':
          return await this.executeOpportunityFilterCommand(command);
        case 'list_vehicles':
          return await this.executeVehicleListCommand(command);
        case 'track_vehicle':
          return await this.executeTrackVehicleCommand(command);
        case 'schedule_maintenance':
          return await this.executeScheduleMaintenanceCommand(command);
        case 'optimize_route':
          return await this.executeOptimizeRouteCommand(command);
        case 'fuel_analysis':
          return await this.executeFuelAnalysisCommand(command);
        case 'help':
          return this.executeHelpCommand();
        case 'system_status':
          return await this.executeSystemStatusCommand();
        default:
          return this.executeUnknownCommand();
      }
    } catch (error) {
      console.error('Command execution error:', error);
      return {
        type: 'error',
        data: null,
        message: 'Sorry, I encountered an error executing that command.'
      };
    }
  }

  private async executeResearchCommand(command: any) {
    if (command.agent && command.action) {
      // Check if we should use a tool for research
      const availableTools = toolExecutor.getAvailableTools('workflow')
      const researchTool = availableTools.find(tool => 
        tool.name.toLowerCase().includes('research') || 
        tool.name.toLowerCase().includes('enrichment')
      )

      let result
      if (researchTool) {
        // Use tool-based research
        result = await agentManager.executeWithTool(
          command.agent,
          researchTool.id,
          { company: command.entities.company }
        )
      } else {
        // Fallback to traditional research
        result = await agentManager.executeTask(
          command.agent,
          command.action,
          { company_name: command.entities.company }
        )
      }

      return {
        type: 'research_result',
        data: result,
        message: `Here's what I found about ${command.entities.company}:`
      }
    }
    throw new Error('Research command missing agent or action');
  }

  private async executeQualifyCommand(command: any) {
    if (command.agent && command.action) {
      const result = await agentManager.executeTask(
        command.agent,
        command.action,
        { contact_id: command.entities.target }
      );
      return {
        type: 'qualification_result',
        data: result,
        message: `Lead qualification complete for ${command.entities.target}:`
      };
    }
    throw new Error('Qualify command missing agent or action');
  }

  private async executeCallCommand(command: any) {
    if (command.agent && command.action) {
      const result = await agentManager.executeTask(
        command.agent,
        command.action,
        { 
          phone_number: '+1234567890',
          purpose: `Contact ${command.entities.target}`
        }
      );
      return {
        type: 'call_result',
        data: result,
        message: `Call initiated to ${command.entities.target}:`
      };
    }
    throw new Error('Call command missing agent or action');
  }

  private async executeOpportunityListCommand(command: any) {
    return {
      type: 'opportunity_list',
      data: await this.mockOpportunityData(command.entities.filter),
      message: 'Here are the opportunities matching your criteria:'
    };
  }

  private async executeOpportunityFilterCommand(command: any) {
    return {
      type: 'opportunity_list',
      data: await this.mockOpportunityData(`value > ${command.entities.amount}`),
      message: `Opportunities above $${command.entities.amount.toLocaleString()}:`
    };
  }

  private async executeVehicleListCommand(command: any) {
    return {
      type: 'vehicle_list',
      data: await this.mockFleetData(command.entities.filter),
      message: 'Here are your fleet vehicles:'
    };
  }

  private async executeTrackVehicleCommand(command: any) {
    if (command.agent && command.action) {
      const result = await agentManager.executeTask(
        command.agent,
        command.action,
        { vehicle_id: command.entities.vehicle }
      );
      return {
        type: 'track_result',
        data: result,
        message: `Vehicle tracking for ${command.entities.vehicle}:`
      };
    }
    throw new Error('Track vehicle command missing agent or action');
  }

  private async executeScheduleMaintenanceCommand(command: any) {
    if (command.agent && command.action) {
      const result = await agentManager.executeTask(
        command.agent,
        command.action,
        { vehicle_id: command.entities.vehicle }
      );
      return {
        type: 'maintenance_result',
        data: result,
        message: `Maintenance scheduled for ${command.entities.vehicle}:`
      };
    }
    throw new Error('Schedule maintenance command missing agent or action');
  }

  private async executeOptimizeRouteCommand(command: any) {
    if (command.agent && command.action) {
      const result = await agentManager.executeTask(
        command.agent,
        command.action,
        { destination: command.entities.destination }
      );
      return {
        type: 'route_result',
        data: result,
        message: `Optimized route to ${command.entities.destination}:`
      };
    }
    throw new Error('Optimize route command missing agent or action');
  }

  private async executeFuelAnalysisCommand(command: any) {
    if (command.agent && command.action) {
      const result = await agentManager.executeTask(
        command.agent,
        command.action,
        { filter: command.entities.filter }
      );
      return {
        type: 'fuel_analysis_result',
        data: result,
        message: 'Fuel efficiency analysis:'
      };
    }
    throw new Error('Fuel analysis command missing agent or action');
  }

  private async executeSystemStatusCommand() {
    const systemStatus = await LLMService.getSystemStatus();
    
    return {
      type: 'system_status',
      data: systemStatus,
      message: 'LLM System Status:'
    };
  }

  private executeHelpCommand() {
    return {
      type: 'help',
      data: this.getHelpInfo(),
      message: 'Here\'s what I can help you with:'
    };
  }

  private executeUnknownCommand() {
    return {
      type: 'unknown',
      data: null,
      message: 'I didn\'t understand that command. Try asking me to research a company, show deals, track vehicles, or make a call.'
    };
  }

  private async mockOpportunityData(filter?: string) {
    const opportunities = [
      { id: '1', title: 'Acme Corp Deal', value: 75000, stage: 'Proposal', probability: 70 },
      { id: '2', title: 'Tech Solutions Contract', value: 120000, stage: 'Negotiation', probability: 85 },
      { id: '3', title: 'Startup Partnership', value: 45000, stage: 'Discovery', probability: 40 },
      { id: '4', title: 'Enterprise License', value: 200000, stage: 'Proposal', probability: 60 }
    ];

    if (filter?.includes('value >')) {
      const amount = parseInt(filter.match(/\d+/)?.[0] || '0');
      return opportunities.filter(opp => opp.value > amount);
    }

    return opportunities;
  }

  private async mockFleetData(filter?: string) {
    const vehicles = [
      { id: '1', name: 'Truck 001', type: 'Pickup Truck', status: 'active', driver: 'John Smith', location: 'Downtown Site A', fuel: 85 },
      { id: '2', name: 'Van 002', type: 'Cargo Van', status: 'maintenance', driver: 'Unassigned', location: 'Shop', fuel: 60 },
      { id: '3', name: 'Crane 003', type: 'Mobile Crane', status: 'active', driver: 'Mike Johnson', location: 'Industrial Park B', fuel: 40 },
      { id: '4', name: 'Excavator 004', type: 'Excavator', status: 'active', driver: 'Sarah Davis', location: 'Highway Project', fuel: 70 }
    ];

    if (filter) {
      return vehicles.filter(vehicle => 
        vehicle.name.toLowerCase().includes(filter.toLowerCase()) ||
        vehicle.type.toLowerCase().includes(filter.toLowerCase()) ||
        vehicle.status.toLowerCase().includes(filter.toLowerCase())
      );
    }

    return vehicles;
  }

  private getHelpInfo() {
    return {
      commands: [
        'Research [company name] - Get company information',
        'Show deals above [amount] - Filter opportunities by value',
        'Track vehicle [name] - Get vehicle location and status',
        'Schedule maintenance for [vehicle] - Schedule vehicle maintenance',
        'Show fleet vehicles - List all vehicles',
        'Optimize route to [destination] - Get optimized route',
        'Fuel efficiency analysis - Get fuel consumption insights',
        'Call [contact name] - Initiate a phone call',
        'Qualify [lead name] - Run lead qualification',
        'Send email to [contact] - Send automated email',
        'System status - Check LLM availability',
        'What\'s happening - Get recent updates'
      ],
      agents: agentManager.getAllAgents().map(agent => ({
        name: agent.name,
        type: agent.type,
        status: agent.status,
        capabilities: agentManager.getCapabilities(agent.id)
      }))
    };
  }
}
