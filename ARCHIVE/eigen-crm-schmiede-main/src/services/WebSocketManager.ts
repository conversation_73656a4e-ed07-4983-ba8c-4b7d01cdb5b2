
import { realTimeService, RealtimeService } from './RealtimeService';

// Legacy WebSocketManager for backward compatibility
export class WebSocketManager {
  private realTimeService: RealtimeService;

  constructor(url?: string) {
    console.warn('WebSocketManager is deprecated. Use RealtimeService directly.');
    this.realTimeService = url ? new RealtimeService() : realTimeService;
  }

  public send(message: any) {
    return this.realTimeService.send(message);
  }

  public disconnect() {
    return this.realTimeService.disconnect();
  }

  public isConnected(): boolean {
    return this.realTimeService.isConnected();
  }
}

// Export the singleton instance for backward compatibility
export const wsManager = new WebSocketManager();

// Re-export the modern service
export { realTimeService as modernWsManager, RealtimeService };
