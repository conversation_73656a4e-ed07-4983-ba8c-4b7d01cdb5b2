
import { pipeline } from '@huggingface/transformers';

export interface DocumentChunk {
  id: string;
  content: string;
  metadata: {
    source: string;
    type: 'document' | 'conversation' | 'project' | 'safety_report';
    timestamp: string;
    projectId?: string;
    tags?: string[];
  };
  embedding?: number[];
}

export interface SearchResult {
  chunk: DocumentChunk;
  similarity: number;
}

export class VectorDatabase {
  private static instance: VectorDatabase;
  private embedder: any = null;
  private documents: DocumentChunk[] = [];
  private isInitialized = false;

  private constructor() {}

  static getInstance(): VectorDatabase {
    if (!VectorDatabase.instance) {
      VectorDatabase.instance = new VectorDatabase();
    }
    return VectorDatabase.instance;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('Initializing vector database with embeddings model...');
      this.embedder = await pipeline(
        'feature-extraction',
        'Xenova/all-MiniLM-L6-v2',
        { device: 'webgpu' }
      );
      this.isInitialized = true;
      console.log('Vector database initialized successfully');
    } catch (error) {
      console.warn('WebGPU failed, falling back to CPU:', error);
      try {
        this.embedder = await pipeline(
          'feature-extraction',
          'Xenova/all-MiniLM-L6-v2'
        );
        this.isInitialized = true;
        console.log('Vector database initialized with CPU');
      } catch (fallbackError) {
        console.error('Failed to initialize embeddings:', fallbackError);
        throw fallbackError;
      }
    }
  }

  async addDocument(chunk: DocumentChunk): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const embedding = await this.generateEmbedding(chunk.content);
      const documentWithEmbedding = { ...chunk, embedding };
      
      // Remove existing document with same ID
      this.documents = this.documents.filter(doc => doc.id !== chunk.id);
      this.documents.push(documentWithEmbedding);
      
      console.log(`Added document chunk: ${chunk.id}`);
    } catch (error) {
      console.error('Error adding document:', error);
    }
  }

  async addDocuments(chunks: DocumentChunk[]): Promise<void> {
    for (const chunk of chunks) {
      await this.addDocument(chunk);
    }
  }

  async search(query: string, limit: number = 5, threshold: number = 0.3): Promise<SearchResult[]> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const queryEmbedding = await this.generateEmbedding(query);
      const results: SearchResult[] = [];

      for (const doc of this.documents) {
        if (!doc.embedding) continue;
        
        const similarity = this.cosineSimilarity(queryEmbedding, doc.embedding);
        if (similarity >= threshold) {
          results.push({ chunk: doc, similarity });
        }
      }

      return results
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, limit);
    } catch (error) {
      console.error('Error searching documents:', error);
      return [];
    }
  }

  async semanticSearch(query: string, type?: string, projectId?: string): Promise<SearchResult[]> {
    let results = await this.search(query);

    // Filter by type if specified
    if (type) {
      results = results.filter(result => result.chunk.metadata.type === type);
    }

    // Filter by project if specified
    if (projectId) {
      results = results.filter(result => result.chunk.metadata.projectId === projectId);
    }

    return results;
  }

  private async generateEmbedding(text: string): Promise<number[]> {
    if (!this.embedder) {
      throw new Error('Embedder not initialized');
    }

    const result = await this.embedder(text, { pooling: 'mean', normalize: true });
    return Array.from(result.data);
  }

  private cosineSimilarity(a: number[], b: number[]): number {
    const dotProduct = a.reduce((sum, ai, i) => sum + ai * b[i], 0);
    const magnitudeA = Math.sqrt(a.reduce((sum, ai) => sum + ai * ai, 0));
    const magnitudeB = Math.sqrt(b.reduce((sum, bi) => sum + bi * bi, 0));
    return dotProduct / (magnitudeA * magnitudeB);
  }

  getDocumentCount(): number {
    return this.documents.length;
  }

  removeDocument(id: string): void {
    this.documents = this.documents.filter(doc => doc.id !== id);
  }

  clearAll(): void {
    this.documents = [];
  }
}

export const vectorDB = VectorDatabase.getInstance();
