
import { supabase, isSupabaseConfigured } from '@/lib/supabase';

interface QualityInspection {
  id: string;
  inspector_name: string;
  inspection_date: string;
  area: string;
  type: 'material' | 'workmanship' | 'compliance' | 'final';
  checklist: any[];
  passed: boolean;
  defects_found: number;
  corrective_actions: string[];
  photos?: string[];
  project_id?: string;
  created_at: string;
  updated_at: string;
}

interface QualityMetrics {
  id: string;
  metric_name: string;
  target_value: number;
  actual_value: number;
  unit: string;
  measurement_date: string;
  project_id?: string;
  created_at: string;
}

export class QualityService {
  private static isConfigured = isSupabaseConfigured();

  static async getQualityInspections(projectId?: string): Promise<QualityInspection[]> {
    if (!this.isConfigured) {
      return this.getMockInspections();
    }

    try {
      let query = supabase.from('quality_inspections').select('*');
      
      if (projectId) {
        query = query.eq('project_id', projectId);
      }

      const { data, error } = await query.order('inspection_date', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching quality inspections:', error);
      return this.getMockInspections();
    }
  }

  static async createQualityInspection(inspection: Omit<QualityInspection, 'id' | 'created_at' | 'updated_at'>): Promise<QualityInspection | null> {
    if (!this.isConfigured) {
      console.log('Mock: Creating quality inspection:', inspection);
      return { 
        ...inspection, 
        id: `mock-${Date.now()}`, 
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      } as QualityInspection;
    }

    try {
      const { data, error } = await supabase
        .from('quality_inspections')
        .insert(inspection)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating quality inspection:', error);
      return null;
    }
  }

  static async getQualityMetrics(projectId?: string): Promise<QualityMetrics[]> {
    if (!this.isConfigured) {
      return this.getMockMetrics();
    }

    try {
      let query = supabase.from('quality_metrics').select('*');
      
      if (projectId) {
        query = query.eq('project_id', projectId);
      }

      const { data, error } = await query.order('measurement_date', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching quality metrics:', error);
      return this.getMockMetrics();
    }
  }

  private static getMockInspections(): QualityInspection[] {
    return [
      {
        id: 'quality-1',
        inspector_name: 'Quality Control Manager',
        inspection_date: '2024-02-20',
        area: 'Foundation - Block A',
        type: 'material',
        checklist: [
          { item: 'Concrete strength', status: 'pass', value: '35 MPa' },
          { item: 'Surface finish', status: 'pass' },
          { item: 'Dimensions', status: 'fail', note: '2mm deviation' }
        ],
        passed: false,
        defects_found: 1,
        corrective_actions: ['Rework surface to specification'],
        photos: [],
        project_id: 'project-1',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }

  private static getMockMetrics(): QualityMetrics[] {
    return [
      {
        id: 'metric-1',
        metric_name: 'Concrete Strength',
        target_value: 35,
        actual_value: 37.2,
        unit: 'MPa',
        measurement_date: '2024-02-20',
        project_id: 'project-1',
        created_at: new Date().toISOString()
      }
    ];
  }
}
