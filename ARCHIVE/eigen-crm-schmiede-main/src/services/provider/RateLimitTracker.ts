
import { ProviderUsage } from '@/types/provider';

export class RateLimitTracker {
  private usage: Map<string, ProviderUsage> = new Map();
  private storageKey = 'ai_provider_usage';

  constructor() {
    this.loadUsage();
    this.startCleanupTimer();
  }

  private loadUsage(): void {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (stored) {
        const data = JSON.parse(stored);
        Object.entries(data).forEach(([key, value]) => {
          this.usage.set(key, value as ProviderUsage);
        });
      }
    } catch (error) {
      console.warn('Failed to load usage data:', error);
    }
  }

  private saveUsage(): void {
    try {
      const data = Object.fromEntries(this.usage);
      localStorage.setItem(this.storageKey, JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to save usage data:', error);
    }
  }

  private startCleanupTimer(): void {
    // Reset daily counters at midnight
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    
    const msUntilMidnight = tomorrow.getTime() - now.getTime();
    
    setTimeout(() => {
      this.resetDailyCounters();
      // Set up daily reset
      setInterval(() => this.resetDailyCounters(), 24 * 60 * 60 * 1000);
    }, msUntilMidnight);

    // Reset minute counters every minute
    setInterval(() => this.resetMinuteCounters(), 60 * 1000);
  }

  private resetDailyCounters(): void {
    const today = new Date().toISOString().split('T')[0];
    this.usage.forEach((usage, providerId) => {
      if (usage.lastResetDate !== today) {
        usage.requestsToday = 0;
        usage.tokensToday = 0;
        usage.lastResetDate = today;
      }
    });
    this.saveUsage();
  }

  private resetMinuteCounters(): void {
    const now = Date.now();
    this.usage.forEach((usage) => {
      if (now - usage.lastRequestTime > 60000) {
        usage.requestsThisMinute = 0;
        usage.tokensThisMinute = 0;
      }
    });
  }

  canMakeRequest(providerId: string, rateLimits: any): boolean {
    const usage = this.getUsage(providerId);
    const now = Date.now();

    // Check minute limits
    if (usage.requestsThisMinute >= rateLimits.requestsPerMinute) {
      return false;
    }

    if (rateLimits.tokensPerMinute && usage.tokensThisMinute >= rateLimits.tokensPerMinute) {
      return false;
    }

    // Check daily limits
    if (usage.requestsToday >= rateLimits.requestsPerDay) {
      return false;
    }

    if (rateLimits.tokensPerDay && usage.tokensToday >= rateLimits.tokensPerDay) {
      return false;
    }

    return true;
  }

  recordRequest(providerId: string, tokensUsed: number, cost: number): void {
    const usage = this.getUsage(providerId);
    const now = Date.now();

    usage.requestsToday += 1;
    usage.requestsThisMinute += 1;
    usage.tokensToday += tokensUsed;
    usage.tokensThisMinute += tokensUsed;
    usage.totalCost += cost;
    usage.lastRequestTime = now;

    this.usage.set(providerId, usage);
    this.saveUsage();
  }

  private getUsage(providerId: string): ProviderUsage {
    if (!this.usage.has(providerId)) {
      const today = new Date().toISOString().split('T')[0];
      this.usage.set(providerId, {
        providerId,
        requestsToday: 0,
        requestsThisMinute: 0,
        tokensToday: 0,
        tokensThisMinute: 0,
        lastRequestTime: 0,
        totalCost: 0,
        lastResetDate: today
      });
    }
    return this.usage.get(providerId)!;
  }

  getUsageStats(): Map<string, ProviderUsage> {
    return new Map(this.usage);
  }

  getRemainingQuota(providerId: string, rateLimits: any): {
    requestsPerMinute: number;
    requestsPerDay: number;
    tokensPerMinute?: number;
    tokensPerDay?: number;
  } {
    const usage = this.getUsage(providerId);
    
    return {
      requestsPerMinute: Math.max(0, rateLimits.requestsPerMinute - usage.requestsThisMinute),
      requestsPerDay: Math.max(0, rateLimits.requestsPerDay - usage.requestsToday),
      tokensPerMinute: rateLimits.tokensPerMinute ? 
        Math.max(0, rateLimits.tokensPerMinute - usage.tokensThisMinute) : undefined,
      tokensPerDay: rateLimits.tokensPerDay ? 
        Math.max(0, rateLimits.tokensPerDay - usage.tokensToday) : undefined
    };
  }
}
