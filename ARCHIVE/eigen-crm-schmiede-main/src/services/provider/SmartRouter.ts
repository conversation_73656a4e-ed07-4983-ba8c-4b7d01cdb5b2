
import { ProviderConfig, RoutingRequest, RoutingProfile } from '@/types/provider';
import { RateLimitTracker } from './RateLimitTracker';
import { ProviderConfigManager } from './ProviderConfigs';

interface ProviderScore {
  providerId: string;
  score: number;
  reasons: string[];
}

export class SmartRouter {
  private rateLimitTracker: RateLimitTracker;
  private configManager: ProviderConfigManager;

  constructor() {
    this.rateLimitTracker = new RateLimitTracker();
    this.configManager = new ProviderConfigManager();
  }

  selectProvider(request: RoutingRequest): ProviderConfig | null {
    const availableProviders = this.getAvailableProviders(request);
    
    if (availableProviders.length === 0) {
      console.warn('No available providers for request:', request);
      return null;
    }

    const scoredProviders = this.scoreProviders(availableProviders, request);
    scoredProviders.sort((a, b) => b.score - a.score);

    console.log('Provider scoring results:', scoredProviders);

    return this.configManager.getConfig(scoredProviders[0].providerId) || null;
  }

  private getAvailableProviders(request: RoutingRequest): ProviderConfig[] {
    return this.configManager.getAvailableConfigs()
      .filter(config => {
        // Check if provider supports the task type
        if (!config.capabilities.includes(request.taskType)) {
          return false;
        }

        // Check rate limits
        if (!this.rateLimitTracker.canMakeRequest(config.id, config.rateLimits)) {
          return false;
        }

        // Check cost constraints
        if (request.maxCost && config.costPerRequest > request.maxCost) {
          return false;
        }

        // Check data sensitivity for local vs cloud
        if (request.dataSensitivity === 'high' && !config.isLocal && config.privacyScore < 8) {
          return false;
        }

        return true;
      });
  }

  private scoreProviders(providers: ProviderConfig[], request: RoutingRequest): ProviderScore[] {
    return providers.map(provider => {
      const score = this.calculateProviderScore(provider, request);
      return {
        providerId: provider.id,
        score: score.total,
        reasons: score.reasons
      };
    });
  }

  private calculateProviderScore(provider: ProviderConfig, request: RoutingRequest): {
    total: number;
    reasons: string[];
  } {
    let score = 0;
    const reasons: string[] = [];
    const weights = this.getProfileWeights(request.preferredProfile);

    // Privacy score (0-100)
    const privacyScore = (provider.privacyScore / 10) * weights.privacy;
    score += privacyScore;
    if (privacyScore > 0) reasons.push(`Privacy: ${provider.privacyScore}/10`);

    // Efficiency score (0-100) - based on response time and reliability
    const efficiencyScore = (
      (1 - (provider.averageResponseTime / 5000)) * 50 + // Response time component
      provider.reliability * 50 // Reliability component
    ) * weights.efficiency;
    score += efficiencyScore;
    if (efficiencyScore > 0) reasons.push(`Efficiency: ${Math.round(efficiencyScore)}/100`);

    // Cost score (0-100) - inverse of cost
    const maxCost = 5; // cents
    const costScore = ((maxCost - Math.min(provider.costPerRequest, maxCost)) / maxCost) * 100 * weights.cost;
    score += costScore;
    if (costScore > 0) reasons.push(`Cost: ${provider.costPerRequest}¢ per request`);

    // Availability bonus
    const quota = this.rateLimitTracker.getRemainingQuota(provider.id, provider.rateLimits);
    const availabilityBonus = Math.min(quota.requestsPerMinute / 10, 20);
    score += availabilityBonus;
    if (availabilityBonus > 0) reasons.push(`Available quota: ${quota.requestsPerMinute}/min`);

    // Task specialization bonus
    if (this.isSpecializedForTask(provider, request.taskType)) {
      score += 25;
      reasons.push(`Specialized for ${request.taskType}`);
    }

    // Data sensitivity alignment
    if (request.dataSensitivity === 'high' && provider.isLocal) {
      score += 30;
      reasons.push('Local processing for sensitive data');
    }

    return { total: score, reasons };
  }

  private getProfileWeights(profile: RoutingProfile): {
    privacy: number;
    efficiency: number;
    cost: number;
  } {
    switch (profile) {
      case 'privacy':
        return { privacy: 1.0, efficiency: 0.3, cost: 0.2 };
      case 'efficiency':
        return { privacy: 0.2, efficiency: 1.0, cost: 0.3 };
      case 'cost':
        return { privacy: 0.2, efficiency: 0.3, cost: 1.0 };
      case 'balanced':
      default:
        return { privacy: 0.4, efficiency: 0.4, cost: 0.4 };
    }
  }

  private isSpecializedForTask(provider: ProviderConfig, taskType: string): boolean {
    const specializations: Record<string, string[]> = {
      'code_generation': ['codellama', 'gpt-4o'],
      'research': ['perplexity', 'sonar'],
      'creative': ['gpt-4o', 'claude'],
      'conversation': ['llama2', 'gpt-4o-mini']
    };

    const specialized = specializations[taskType] || [];
    return specialized.some(spec => 
      provider.models.some(model => model.toLowerCase().includes(spec.toLowerCase()))
    );
  }

  recordUsage(providerId: string, tokensUsed: number, cost: number): void {
    this.rateLimitTracker.recordRequest(providerId, tokensUsed, cost);
  }

  getUsageStats() {
    return this.rateLimitTracker.getUsageStats();
  }

  getRemainingQuota(providerId: string) {
    const config = this.configManager.getConfig(providerId);
    if (!config) return null;
    
    return this.rateLimitTracker.getRemainingQuota(providerId, config.rateLimits);
  }
}
