
import { ProviderConfig } from '@/types/provider';

export const defaultProviderConfigs: ProviderConfig[] = [
  {
    id: 'ollama-llama2',
    name: 'Ollama Llama2',
    type: 'ollama',
    baseUrl: 'http://localhost:11434',
    models: ['llama2', 'llama2:7b', 'llama2:13b'],
    isLocal: true,
    privacyScore: 10,
    costPerRequest: 0,
    rateLimits: {
      requestsPerMinute: 60,
      requestsPerDay: 10000
    },
    capabilities: ['conversation', 'analysis', 'research'],
    averageResponseTime: 2000,
    reliability: 0.9
  },
  {
    id: 'ollama-codellama',
    name: 'Ollama CodeLlama',
    type: 'ollama',
    baseUrl: 'http://localhost:11434',
    models: ['codellama', 'codellama:7b', 'codellama:13b'],
    isLocal: true,
    privacyScore: 10,
    costPerRequest: 0,
    rateLimits: {
      requestsPerMinute: 60,
      requestsPerDay: 10000
    },
    capabilities: ['code_generation'],
    averageResponseTime: 2500,
    reliability: 0.9
  },
  {
    id: 'openai-gpt4o-mini',
    name: 'OpenAI GPT-4o Mini',
    type: 'openai',
    models: ['gpt-4o-mini'],
    isLocal: false,
    privacyScore: 6,
    costPerRequest: 0.5,
    rateLimits: {
      requestsPerMinute: 500,
      requestsPerDay: 10000,
      tokensPerMinute: 200000,
      tokensPerDay: 1000000
    },
    capabilities: ['conversation', 'analysis', 'creative', 'code_generation'],
    averageResponseTime: 1200,
    reliability: 0.99
  },
  {
    id: 'openai-gpt4o',
    name: 'OpenAI GPT-4o',
    type: 'openai',
    models: ['gpt-4o'],
    isLocal: false,
    privacyScore: 6,
    costPerRequest: 2.5,
    rateLimits: {
      requestsPerMinute: 500,
      requestsPerDay: 10000,
      tokensPerMinute: 30000,
      tokensPerDay: 300000
    },
    capabilities: ['conversation', 'analysis', 'creative', 'code_generation', 'research'],
    averageResponseTime: 1500,
    reliability: 0.99
  },
  {
    id: 'perplexity-sonar',
    name: 'Perplexity Sonar',
    type: 'perplexity',
    models: ['llama-3.1-sonar-small-128k-online'],
    isLocal: false,
    privacyScore: 7,
    costPerRequest: 1.0,
    rateLimits: {
      requestsPerMinute: 20,
      requestsPerDay: 1000
    },
    capabilities: ['research', 'analysis'],
    averageResponseTime: 2000,
    reliability: 0.95
  },
  {
    id: 'huggingface-free',
    name: 'HuggingFace Free Models',
    type: 'huggingface',
    models: ['microsoft/DialoGPT-medium', 'facebook/blenderbot-400M-distill'],
    isLocal: false,
    privacyScore: 8,
    costPerRequest: 0,
    rateLimits: {
      requestsPerMinute: 30,
      requestsPerDay: 1000
    },
    capabilities: ['conversation'],
    averageResponseTime: 3000,
    reliability: 0.85
  }
];

export class ProviderConfigManager {
  private configs: Map<string, ProviderConfig> = new Map();
  private storageKey = 'ai_provider_configs';

  constructor() {
    this.loadConfigs();
  }

  private loadConfigs(): void {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (stored) {
        const data = JSON.parse(stored);
        Object.entries(data).forEach(([key, value]) => {
          this.configs.set(key, value as ProviderConfig);
        });
      } else {
        // Initialize with defaults
        defaultProviderConfigs.forEach(config => {
          this.configs.set(config.id, config);
        });
        this.saveConfigs();
      }
    } catch (error) {
      console.warn('Failed to load provider configs:', error);
      // Fallback to defaults
      defaultProviderConfigs.forEach(config => {
        this.configs.set(config.id, config);
      });
    }
  }

  private saveConfigs(): void {
    try {
      const data = Object.fromEntries(this.configs);
      localStorage.setItem(this.storageKey, JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to save provider configs:', error);
    }
  }

  getAllConfigs(): ProviderConfig[] {
    return Array.from(this.configs.values());
  }

  getConfig(providerId: string): ProviderConfig | undefined {
    return this.configs.get(providerId);
  }

  updateConfig(config: ProviderConfig): void {
    this.configs.set(config.id, config);
    this.saveConfigs();
  }

  removeConfig(providerId: string): void {
    this.configs.delete(providerId);
    this.saveConfigs();
  }

  getConfigsByCapability(capability: string): ProviderConfig[] {
    return Array.from(this.configs.values())
      .filter(config => config.capabilities.includes(capability as any));
  }

  getAvailableConfigs(): ProviderConfig[] {
    // In a real implementation, this would check actual availability
    return this.getAllConfigs();
  }
}
