import { unifiedLLM } from './UnifiedLLMService'
import { unifiedDatabase } from './UnifiedDatabaseService'
import type { 
  UnifiedLLMRequest, 
  UnifiedServiceResponse, 
  UnifiedProject, 
  TaskType 
} from '@/types/unified'

export class UnifiedAIOrchestrator {
  private static instance: UnifiedAIOrchestrator

  static getInstance(): UnifiedAIOrchestrator {
    if (!UnifiedAIOrchestrator.instance) {
      UnifiedAIOrchestrator.instance = new UnifiedAIOrchestrator()
    }
    return UnifiedAIOrchestrator.instance
  }

  async analyzeBusinessData(data: any, analysisType: string): Promise<UnifiedServiceResponse<any>> {
    try {
      const request: UnifiedLLMRequest = {
        task: 'business_intelligence' as TaskType,
        prompt: `Analyze the following business data for ${analysisType}:\n\n${JSON.stringify(data, null, 2)}\n\nProvide insights, trends, and actionable recommendations.`,
        context: { analysisType, data },
        dataSensitivity: 'high',
        preferredProfile: 'privacy'
      }

      const response = await unifiedLLM.processRequest(request)
      
      if (response.success && response.data) {
        // Store analysis result
        await unifiedDatabase.createActivityEntry({
          type: 'ai_analysis',
          title: `Business Analysis: ${analysisType}`,
          description: `AI generated insights for ${analysisType}`,
          value: analysisType
        })

        return {
          success: true,
          data: {
            analysis: response.data.response,
            confidence: response.data.confidence,
            processingTime: response.data.processingTime,
            insights: this.extractInsights(response.data.response),
            recommendations: this.extractRecommendations(response.data.response)
          }
        }
      }

      return {
        success: false,
        error: 'Failed to process business analysis'
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Business analysis failed'
      }
    }
  }

  async generateProjectPlan(description: string): Promise<UnifiedServiceResponse<Partial<UnifiedProject>>> {
    try {
      const request: UnifiedLLMRequest = {
        task: 'project_planning',
        prompt: `Create a comprehensive project plan for: ${description}
        
        Provide a JSON response with:
        - name: Project name
        - description: Detailed description
        - estimatedHours: Total estimated hours
        - budget: Estimated budget
        - tasks: Array of tasks with titles, descriptions, estimated hours, and priorities
        - milestones: Key milestones with titles and descriptions
        - risks: Potential risks with impact and probability assessments
        
        Format as valid JSON only.`,
        context: { projectDescription: description },
        dataSensitivity: 'medium',
        preferredProfile: 'efficiency'
      }

      const response = await unifiedLLM.processRequest(request)
      
      if (response.success && response.data) {
        const projectData = this.parseProjectResponse(response.data.response)
        
        return {
          success: true,
          data: {
            ...projectData,
            status: 'planning' as const,
            priority: 'medium' as const,
            progress: 0,
            startDate: new Date().toISOString(),
            endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        }
      }

      return {
        success: false,
        error: 'Failed to generate project plan'
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Project plan generation failed'
      }
    }
  }

  async generateExecutiveSummary(data: any): Promise<UnifiedServiceResponse<string>> {
    try {
      const request: UnifiedLLMRequest = {
        task: 'executive_summary',
        prompt: `Generate an executive summary based on the following business data:
        
        ${JSON.stringify(data, null, 2)}
        
        Include:
        - Key performance indicators and metrics
        - Major achievements and milestones
        - Critical challenges and risks
        - Strategic recommendations
        - Financial highlights
        
        Keep it concise, professional, and actionable.`,
        context: { summaryData: data },
        dataSensitivity: 'high',
        preferredProfile: 'privacy'
      }

      const response = await unifiedLLM.processRequest(request)
      
      if (response.success && response.data) {
        return {
          success: true,
          data: response.data.response
        }
      }

      return {
        success: false,
        error: 'Failed to generate executive summary'
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Executive summary generation failed'
      }
    }
  }

  async processMarketAnalysis(marketData: any): Promise<UnifiedServiceResponse<any>> {
    try {
      const request: UnifiedLLMRequest = {
        task: 'market_analysis',
        prompt: `Analyze the following market data and provide comprehensive insights:
        
        ${JSON.stringify(marketData, null, 2)}
        
        Analyze:
        - Market trends and patterns
        - Competitive landscape
        - Growth opportunities
        - Risk factors
        - Strategic recommendations
        
        Provide actionable insights for business strategy.`,
        context: { marketData },
        dataSensitivity: 'medium',
        preferredProfile: 'efficiency'
      }

      const response = await unifiedLLM.processRequest(request)
      
      if (response.success && response.data) {
        return {
          success: true,
          data: {
            analysis: response.data.response,
            trends: this.extractTrends(response.data.response),
            opportunities: this.extractOpportunities(response.data.response),
            risks: this.extractRisks(response.data.response),
            recommendations: this.extractRecommendations(response.data.response)
          }
        }
      }

      return {
        success: false,
        error: 'Failed to process market analysis'
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Market analysis failed'
      }
    }
  }

  private extractInsights(response: string): string[] {
    const insights = []
    const lines = response.split('\n')
    
    for (const line of lines) {
      if (line.includes('insight') || line.includes('trend') || line.includes('pattern')) {
        insights.push(line.trim())
      }
    }
    
    return insights.slice(0, 5)
  }

  private extractRecommendations(response: string): string[] {
    const recommendations = []
    const lines = response.split('\n')
    
    for (const line of lines) {
      if (line.includes('recommend') || line.includes('suggest') || line.includes('should')) {
        recommendations.push(line.trim())
      }
    }
    
    return recommendations.slice(0, 5)
  }

  private extractTrends(response: string): string[] {
    const trends = []
    const lines = response.split('\n')
    
    for (const line of lines) {
      if (line.includes('trend') || line.includes('growing') || line.includes('increasing')) {
        trends.push(line.trim())
      }
    }
    
    return trends.slice(0, 3)
  }

  private extractOpportunities(response: string): string[] {
    const opportunities = []
    const lines = response.split('\n')
    
    for (const line of lines) {
      if (line.includes('opportunity') || line.includes('potential') || line.includes('growth')) {
        opportunities.push(line.trim())
      }
    }
    
    return opportunities.slice(0, 3)
  }

  private extractRisks(response: string): string[] {
    const risks = []
    const lines = response.split('\n')
    
    for (const line of lines) {
      if (line.includes('risk') || line.includes('challenge') || line.includes('threat')) {
        risks.push(line.trim())
      }
    }
    
    return risks.slice(0, 3)
  }

  private parseProjectResponse(response: string): any {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0])
      }
    } catch (error) {
      console.error('Failed to parse AI project response:', error)
    }

    return {
      name: 'AI Generated Project',
      description: 'Project created from AI analysis',
      estimatedHours: 100,
      budget: 10000,
      tasks: [],
      milestones: [],
      risks: []
    }
  }

  async getAIMetrics(): Promise<UnifiedServiceResponse<any>> {
    try {
      const llmMetrics = unifiedLLM.getMetrics()
      const usageStats = unifiedLLM.getUsageStats()
      
      return {
        success: true,
        data: {
          llm: llmMetrics,
          usage: usageStats,
          performance: {
            averageProcessingTime: llmMetrics.averageResponseTime,
            successRate: llmMetrics.successRate,
            systemHealth: llmMetrics.systemHealth
          }
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get AI metrics'
      }
    }
  }
}

export const unifiedAI = UnifiedAIOrchestrator.getInstance()
