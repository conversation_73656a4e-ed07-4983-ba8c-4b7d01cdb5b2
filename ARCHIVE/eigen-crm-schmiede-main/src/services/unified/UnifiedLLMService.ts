
import { SmartRouter } from '@/services/provider/SmartRouter'
import { LLMRouter } from '@/services/llm/LLMRouter'
import { LLMService as BaseLLMService } from '@/services/llm/LLMService'
import { OllamaService } from '@/services/llm/OllamaService'
import type { 
  UnifiedLLMRequest, 
  UnifiedLLMResponse, 
  UnifiedServiceResponse,
  UnifiedProviderConfig,
  UnifiedMetrics,
  TaskType as UnifiedTaskType,
  LLMProvider
} from '@/types/unified'
import type { RoutingRequest, ProviderConfig, ProviderType } from '@/types/provider'

export class UnifiedLLMService {
  private static instance: UnifiedLLMService
  private smartRouter: SmartRouter
  private metrics: UnifiedMetrics

  constructor() {
    this.smartRouter = new SmartRouter()
    this.metrics = {
      totalRequests: 0,
      successRate: 0,
      averageResponseTime: 0,
      costToday: 0,
      activeProviders: 0,
      systemHealth: 100
    }
  }

  static getInstance(): UnifiedLLMService {
    if (!UnifiedLLMService.instance) {
      UnifiedLLMService.instance = new UnifiedLLMService()
    }
    return UnifiedLLMService.instance
  }

  private mapProviderTypeToLLMProvider(providerType: ProviderType): LLMProvider {
    switch (providerType) {
      case 'ollama':
        return 'ollama'
      case 'openai':
      case 'anthropic':
      case 'google':
      case 'perplexity':
      case 'huggingface':
      default:
        return 'cloud'
    }
  }

  private convertToUnifiedProvider(provider: ProviderConfig): UnifiedProviderConfig {
    return {
      id: provider.id,
      name: provider.name,
      type: this.mapProviderTypeToLLMProvider(provider.type),
      models: provider.models,
      isActive: true,
      isLocal: provider.isLocal,
      capabilities: provider.capabilities as UnifiedTaskType[],
      rateLimits: {
        requestsPerMinute: provider.rateLimits.requestsPerMinute,
        requestsPerDay: provider.rateLimits.requestsPerDay,
        tokensPerMinute: provider.rateLimits.tokensPerMinute || 10000
      },
      costPerRequest: provider.costPerRequest,
      averageResponseTime: provider.averageResponseTime,
      reliability: provider.reliability,
      privacyScore: provider.privacyScore,
      healthStatus: 'healthy' as const
    }
  }

  async processRequest(request: UnifiedLLMRequest): Promise<UnifiedServiceResponse<UnifiedLLMResponse>> {
    const startTime = Date.now()
    this.metrics.totalRequests++

    try {
      console.log('Processing unified LLM request:', request.task)

      // Convert to provider routing request
      const routingRequest: RoutingRequest = {
        taskType: this.mapTaskType(request.task),
        prompt: request.prompt,
        context: request.context,
        dataSensitivity: request.dataSensitivity || 'medium',
        preferredProfile: request.preferredProfile || 'balanced',
        maxCost: request.maxCost
      }

      const selectedProvider = this.smartRouter.selectProvider(routingRequest)

      if (!selectedProvider) {
        console.warn('No provider available for request, falling back to basic routing')
        return this.fallbackToBasicRouting(request)
      }

      console.log('Selected provider:', selectedProvider.id)

      let response: UnifiedLLMResponse

      // Convert provider config to unified config
      const unifiedProvider = this.convertToUnifiedProvider(selectedProvider)

      if (selectedProvider.type === 'ollama') {
        response = await this.processWithOllama(request, unifiedProvider)
      } else {
        response = await this.processWithCloud(request, unifiedProvider)
      }

      const processingTime = Date.now() - startTime
      const tokensUsed = response.usage?.totalTokens || 0
      const cost = selectedProvider.costPerRequest
      
      this.smartRouter.recordUsage(selectedProvider.id, tokensUsed, cost)
      this.updateMetrics(processingTime, true, cost)

      return {
        success: true,
        data: {
          ...response,
          provider: selectedProvider.name,
          processingTime
        }
      }

    } catch (error) {
      const processingTime = Date.now() - startTime
      this.updateMetrics(processingTime, false, 0)

      console.error('Unified LLM processing failed:', error)
      
      try {
        const fallbackResponse = await this.tryFallbackProvider(request)
        if (fallbackResponse.success) {
          return fallbackResponse
        }
      } catch (fallbackError) {
        console.error('Fallback provider also failed:', fallbackError)
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'LLM processing failed'
      }
    }
  }

  private mapTaskType(unifiedTask: UnifiedTaskType): any {
    // Map unified task types to provider task types
    const taskMapping: Record<string, string> = {
      'generation': 'code_generation',
      'business_intelligence': 'analysis',
      'project_planning': 'analysis',
      'executive_summary': 'analysis',
      'market_analysis': 'analysis',
      'revenue_forecast': 'analysis',
      'competitor_analysis': 'analysis',
      'trend_analysis': 'analysis',
      'opportunity_discovery': 'research',
      'process_automation': 'code_generation',
      'document_generation': 'creative'
    }

    return taskMapping[unifiedTask] || unifiedTask
  }

  private async processWithOllama(request: UnifiedLLMRequest, provider: UnifiedProviderConfig): Promise<UnifiedLLMResponse> {
    // Check if Ollama is available
    const isAvailable = await OllamaService.checkAvailability()
    if (!isAvailable) {
      throw new Error('Ollama service is not available')
    }

    const model = provider.models[0] || 'llama2'
    
    const response = await OllamaService.generateResponse(request.prompt, model)

    return {
      response: response || 'No response generated',
      confidence: 0.85,
      provider: 'ollama',
      model,
      usage: {
        promptTokens: Math.floor(request.prompt.length / 4),
        completionTokens: Math.floor((response?.length || 0) / 4),
        totalTokens: Math.floor((request.prompt.length + (response?.length || 0)) / 4)
      }
    }
  }

  private async processWithCloud(request: UnifiedLLMRequest, provider: UnifiedProviderConfig): Promise<UnifiedLLMResponse> {
    const response = await BaseLLMService.processRequest({
      task: request.task,
      prompt: request.prompt,
      context: request.context,
      model: request.model,
      temperature: request.temperature,
      maxTokens: request.maxTokens,
      preferredProvider: 'cloud'
    })

    return {
      response: response.response,
      confidence: response.confidence,
      provider: response.provider,
      model: response.model,
      processingTime: response.processingTime,
      usage: response.usage,
      metadata: response.metadata
    }
  }

  private async fallbackToBasicRouting(request: UnifiedLLMRequest): Promise<UnifiedServiceResponse<UnifiedLLMResponse>> {
    try {
      const route = LLMRouter.getRoute(request.task, request.context)
      console.log('Using basic routing fallback:', route)

      const response = await BaseLLMService.processRequest({
        task: request.task,
        prompt: request.prompt,
        context: request.context,
        preferredProvider: route.provider
      })

      return {
        success: true,
        data: response
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Fallback routing failed'
      }
    }
  }

  private async tryFallbackProvider(request: UnifiedLLMRequest): Promise<UnifiedServiceResponse<UnifiedLLMResponse>> {
    console.log('Trying fallback provider...')
    
    const alternativeRoute = LLMRouter.getAlternativeRoute(request.task)
    
    try {
      const response = await BaseLLMService.processRequest({
        task: request.task,
        prompt: request.prompt,
        context: request.context,
        preferredProvider: alternativeRoute.provider
      })

      return {
        success: true,
        data: response
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'All providers failed'
      }
    }
  }

  private updateMetrics(processingTime: number, success: boolean, cost: number): void {
    const previousSuccessful = this.metrics.successRate * (this.metrics.totalRequests - 1)
    this.metrics.successRate = (previousSuccessful + (success ? 1 : 0)) / this.metrics.totalRequests

    const previousTime = this.metrics.averageResponseTime * (this.metrics.totalRequests - 1)
    this.metrics.averageResponseTime = (previousTime + processingTime) / this.metrics.totalRequests

    this.metrics.costToday += cost

    const healthScore = (this.metrics.successRate * 70) + 
                       (Math.max(0, 100 - (this.metrics.averageResponseTime / 50)) * 30)
    this.metrics.systemHealth = Math.min(100, Math.max(0, healthScore))
  }

  async getSystemStatus(): Promise<UnifiedServiceResponse<any>> {
    try {
      const ollamaAvailable = await OllamaService.checkAvailability()
      const cloudStatus = await BaseLLMService.getSystemStatus()
      const usageStats = this.smartRouter.getUsageStats()

      return {
        success: true,
        data: {
          status: 'operational',
          metrics: this.metrics,
          providers: {
            ollama: {
              available: ollamaAvailable,
              models: ['llama2', 'codellama', 'mistral'],
              status: ollamaAvailable ? 'active' : 'inactive'
            },
            cloud: {
              available: true,
              models: ['gpt-4o', 'gpt-4o-mini', 'claude-3-sonnet'],
              status: 'active'
            }
          },
          routing: {
            smartRouterActive: true,
            usageStats,
            totalRequests: this.metrics.totalRequests,
            successRate: this.metrics.successRate
          }
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get system status'
      }
    }
  }

  getMetrics(): UnifiedMetrics {
    return { ...this.metrics }
  }

  getUsageStats() {
    return this.smartRouter.getUsageStats()
  }

  getRemainingQuota(providerId: string) {
    return this.smartRouter.getRemainingQuota(providerId)
  }
}

export const unifiedLLM = UnifiedLLMService.getInstance()
