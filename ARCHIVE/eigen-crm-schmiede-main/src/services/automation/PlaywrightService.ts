
import { logger } from '../Logger'

export interface BrowserConfig {
  headless: boolean
  viewport: { width: number; height: number }
  userAgent?: string
  timeout: number
  slowMo?: number
}

export interface AutomationScript {
  id: string
  name: string
  description: string
  url: string
  actions: AutomationAction[]
  schedule?: ScheduleConfig
  config: BrowserConfig
}

export interface AutomationAction {
  type: 'navigate' | 'click' | 'type' | 'extract' | 'wait' | 'scroll' | 'screenshot' | 'select'
  selector?: string
  value?: string
  url?: string
  timeout?: number
  extractAs?: string
  waitFor?: 'selector' | 'timeout' | 'networkidle'
}

export interface ScheduleConfig {
  enabled: boolean
  frequency: 'once' | 'hourly' | 'daily' | 'weekly' | 'monthly'
  time?: string
  timezone?: string
}

export interface AutomationResult {
  success: boolean
  data: Record<string, any>
  screenshots: string[]
  logs: string[]
  error?: string
  duration: number
  timestamp: string
}

export class PlaywrightService {
  private static scripts: Map<string, AutomationScript> = new Map()
  private static running: Set<string> = new Set()

  static async executeScript(scriptId: string): Promise<AutomationResult> {
    const startTime = Date.now()
    const script = this.scripts.get(scriptId)
    
    if (!script) {
      return {
        success: false,
        data: {},
        screenshots: [],
        logs: [],
        error: 'Script not found',
        duration: 0,
        timestamp: new Date().toISOString()
      }
    }

    if (this.running.has(scriptId)) {
      return {
        success: false,
        data: {},
        screenshots: [],
        logs: [],
        error: 'Script already running',
        duration: 0,
        timestamp: new Date().toISOString()
      }
    }

    this.running.add(scriptId)
    logger.info(`Starting browser automation: ${script.name}`)

    try {
      // Since we can't actually use Playwright in the browser,
      // we'll simulate the automation with realistic mock data
      const result = await this.simulateAutomation(script)
      
      return {
        ...result,
        duration: Date.now() - startTime,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        success: false,
        data: {},
        screenshots: [],
        logs: [],
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime,
        timestamp: new Date().toISOString()
      }
    } finally {
      this.running.delete(scriptId)
    }
  }

  private static async simulateAutomation(script: AutomationScript): Promise<Omit<AutomationResult, 'duration' | 'timestamp'>> {
    const extractedData: Record<string, any> = {}
    const screenshots: string[] = []
    const logs: string[] = []

    logs.push(`Opening browser with config: ${JSON.stringify(script.config)}`)
    
    for (const action of script.actions) {
      await this.delay(200) // Simulate action execution time

      switch (action.type) {
        case 'navigate':
          logs.push(`Navigating to: ${action.url}`)
          break
        
        case 'click':
          logs.push(`Clicking element: ${action.selector}`)
          break
        
        case 'type':
          logs.push(`Typing "${action.value}" into: ${action.selector}`)
          break
        
        case 'extract':
          logs.push(`Extracting data from: ${action.selector}`)
          if (action.extractAs) {
            // Simulate extracted data based on common selectors
            extractedData[action.extractAs] = this.getMockDataForSelector(action.selector)
          }
          break
        
        case 'screenshot':
          const screenshotUrl = `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`
          screenshots.push(screenshotUrl)
          logs.push('Screenshot captured')
          break
        
        case 'wait':
          logs.push(`Waiting ${action.timeout || 1000}ms`)
          await this.delay(action.timeout || 1000)
          break
        
        case 'scroll':
          logs.push(`Scrolling to: ${action.selector}`)
          break
        
        case 'select':
          logs.push(`Selecting "${action.value}" from: ${action.selector}`)
          break
      }
    }

    logs.push('Browser automation completed successfully')

    return {
      success: true,
      data: extractedData,
      screenshots,
      logs
    }
  }

  private static getMockDataForSelector(selector?: string): string {
    if (!selector) return 'Mock data'
    
    if (selector.includes('title') || selector.includes('h1')) {
      return 'Sample Page Title'
    }
    if (selector.includes('price') || selector.includes('cost')) {
      return '$99.99'
    }
    if (selector.includes('email')) {
      return '<EMAIL>'
    }
    if (selector.includes('phone')) {
      return '+****************'
    }
    if (selector.includes('address')) {
      return '123 Main St, City, State 12345'
    }
    
    return `Mock data from ${selector}`
  }

  static createScript(script: Omit<AutomationScript, 'id'>): string {
    const id = Date.now().toString()
    const newScript: AutomationScript = {
      ...script,
      id
    }
    
    this.scripts.set(id, newScript)
    logger.info(`Created automation script: ${script.name}`)
    
    return id
  }

  static getScripts(): AutomationScript[] {
    return Array.from(this.scripts.values())
  }

  static getScript(id: string): AutomationScript | undefined {
    return this.scripts.get(id)
  }

  static updateScript(id: string, updates: Partial<AutomationScript>): boolean {
    const script = this.scripts.get(id)
    if (!script) return false
    
    Object.assign(script, updates)
    return true
  }

  static deleteScript(id: string): boolean {
    return this.scripts.delete(id)
  }

  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // Pre-built automation templates
  static getTemplates(): Array<Omit<AutomationScript, 'id'>> {
    return [
      {
        name: 'Lead Research',
        description: 'Extract company information from LinkedIn',
        url: 'https://linkedin.com/company/{company}',
        config: {
          headless: true,
          viewport: { width: 1920, height: 1080 },
          timeout: 30000
        },
        actions: [
          { type: 'navigate', url: 'https://linkedin.com/company/{company}' },
          { type: 'wait', timeout: 3000 },
          { type: 'extract', selector: '.org-top-card-summary__title', extractAs: 'company_name' },
          { type: 'extract', selector: '.org-top-card-summary__tagline', extractAs: 'tagline' },
          { type: 'extract', selector: '.org-about-us__description', extractAs: 'description' },
          { type: 'extract', selector: '.org-top-card-summary__follower-count', extractAs: 'followers' },
          { type: 'screenshot' }
        ]
      },
      {
        name: 'Competitor Price Monitor',
        description: 'Monitor competitor pricing changes',
        url: '{competitor_url}',
        config: {
          headless: true,
          viewport: { width: 1280, height: 720 },
          timeout: 20000
        },
        actions: [
          { type: 'navigate', url: '{competitor_url}' },
          { type: 'wait', timeout: 2000 },
          { type: 'extract', selector: '.price, [data-price], .cost', extractAs: 'current_price' },
          { type: 'extract', selector: '.product-title, h1, .name', extractAs: 'product_name' },
          { type: 'extract', selector: '.availability, .stock', extractAs: 'availability' },
          { type: 'screenshot' }
        ],
        schedule: {
          enabled: true,
          frequency: 'daily',
          time: '09:00'
        }
      },
      {
        name: 'Social Media Monitoring',
        description: 'Monitor brand mentions and sentiment',
        url: 'https://twitter.com/search?q={brand_name}',
        config: {
          headless: true,
          viewport: { width: 1366, height: 768 },
          timeout: 25000
        },
        actions: [
          { type: 'navigate', url: 'https://twitter.com/search?q={brand_name}' },
          { type: 'wait', timeout: 3000 },
          { type: 'extract', selector: '[data-testid="tweet"]', extractAs: 'recent_mentions' },
          { type: 'scroll', selector: 'footer' },
          { type: 'wait', timeout: 2000 },
          { type: 'screenshot' }
        ]
      },
      {
        name: 'News & Content Scraper',
        description: 'Scrape industry news and updates',
        url: '{news_url}',
        config: {
          headless: true,
          viewport: { width: 1440, height: 900 },
          timeout: 15000
        },
        actions: [
          { type: 'navigate', url: '{news_url}' },
          { type: 'wait', timeout: 2000 },
          { type: 'extract', selector: 'h1, .headline, .title', extractAs: 'headlines' },
          { type: 'extract', selector: '.article-content, .content, p', extractAs: 'content' },
          { type: 'extract', selector: '.date, .timestamp', extractAs: 'publish_date' },
          { type: 'screenshot' }
        ]
      }
    ]
  }

  static async validateScript(script: AutomationScript): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = []

    if (!script.name.trim()) {
      errors.push('Script name is required')
    }

    if (!script.url.trim()) {
      errors.push('URL is required')
    }

    if (script.actions.length === 0) {
      errors.push('At least one action is required')
    }

    // Validate actions
    script.actions.forEach((action, index) => {
      if (!action.type) {
        errors.push(`Action ${index + 1}: Type is required`)
      }

      if (['click', 'type', 'extract', 'scroll'].includes(action.type) && !action.selector) {
        errors.push(`Action ${index + 1}: Selector is required for ${action.type}`)
      }

      if (action.type === 'type' && !action.value) {
        errors.push(`Action ${index + 1}: Value is required for type action`)
      }

      if (action.type === 'navigate' && !action.url) {
        errors.push(`Action ${index + 1}: URL is required for navigate action`)
      }
    })

    return {
      valid: errors.length === 0,
      errors
    }
  }
}
