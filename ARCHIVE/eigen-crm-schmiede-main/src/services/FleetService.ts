
import { supabase, isSupabaseConfigured } from '@/lib/supabase';
import { logger } from './Logger';
import type { Database } from '@/lib/supabase';
import type { Vehicle, MaintenanceRecord } from '@/types/fleet';

type Tables = Database['public']['Tables'];

export class FleetService {
  private static isConfigured = isSupabaseConfigured();

  static async getVehicles(): Promise<Vehicle[]> {
    if (!this.isConfigured) {
      return this.getMockVehicles();
    }

    try {
      const { data, error } = await supabase
        .from('vehicles')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      logger.logServiceCall('FleetService', 'getVehicles', true, { 
        vehicleCount: data?.length || 0 
      });
      
      return data || [];
    } catch (error) {
      logger.logServiceCall('FleetService', 'getVehicles', false, { error });
      return this.getMockVehicles();
    }
  }

  static async createVehicle(vehicle: Omit<Vehicle, 'id' | 'created_at' | 'updated_at'>): Promise<Vehicle | null> {
    if (!this.isConfigured) {
      logger.info('Mock: Creating vehicle', { vehicleName: vehicle.name });
      return { 
        ...vehicle, 
        id: `mock-${Date.now()}`, 
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      } as Vehicle;
    }

    try {
      const { data, error } = await supabase
        .from('vehicles')
        .insert(vehicle)
        .select()
        .single();

      if (error) throw error;
      
      logger.logServiceCall('FleetService', 'createVehicle', true, { 
        vehicleId: data.id, 
        vehicleName: vehicle.name 
      });
      
      return data;
    } catch (error) {
      logger.logServiceCall('FleetService', 'createVehicle', false, { 
        vehicleName: vehicle.name, 
        error 
      });
      return null;
    }
  }

  static async updateVehicle(id: string, updates: Partial<Vehicle>): Promise<Vehicle | null> {
    if (!this.isConfigured) {
      logger.info('Mock: Updating vehicle', { vehicleId: id, updates });
      return { 
        ...updates, 
        id, 
        updated_at: new Date().toISOString()
      } as Vehicle;
    }

    try {
      const { data, error } = await supabase
        .from('vehicles')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      
      logger.logServiceCall('FleetService', 'updateVehicle', true, { vehicleId: id });
      
      return data;
    } catch (error) {
      logger.logServiceCall('FleetService', 'updateVehicle', false, { vehicleId: id, error });
      return null;
    }
  }

  static async getMaintenanceRecords(vehicleId?: string): Promise<MaintenanceRecord[]> {
    if (!this.isConfigured) {
      return this.getMockMaintenanceRecords();
    }

    try {
      let query = supabase.from('maintenance_records').select('*');
      
      if (vehicleId) {
        query = query.eq('vehicle_id', vehicleId);
      }

      const { data, error } = await query.order('performed_at', { ascending: false });

      if (error) throw error;
      
      logger.logServiceCall('FleetService', 'getMaintenanceRecords', true, { 
        vehicleId, 
        recordCount: data?.length || 0 
      });
      
      return data || [];
    } catch (error) {
      logger.logServiceCall('FleetService', 'getMaintenanceRecords', false, { vehicleId, error });
      return this.getMockMaintenanceRecords();
    }
  }

  private static getMockVehicles(): Vehicle[] {
    return [
      {
        id: 'vehicle-1',
        name: 'CAT 320 Excavator',
        type: 'excavator',
        make: 'Caterpillar',
        model: '320',
        year: 2022,
        license_plate: 'CNS-001',
        status: 'available',
        location: 'Main Yard',
        mileage: 1250,
        fuel_level: 85,
        last_maintenance: '2024-01-15',
        next_maintenance: '2024-04-15',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'vehicle-2',
        name: 'Ford F-250 Truck',
        type: 'truck',
        make: 'Ford',
        model: 'F-250',
        year: 2023,
        license_plate: 'CNS-002',
        status: 'in_use',
        location: 'Downtown Site',
        mileage: 15500,
        fuel_level: 65,
        last_maintenance: '2024-02-01',
        next_maintenance: '2024-05-01',
        assigned_to: 'user-1',
        project_id: 'project-1',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }

  private static getMockMaintenanceRecords(): MaintenanceRecord[] {
    return [
      {
        id: 'maintenance-1',
        vehicle_id: 'vehicle-1',
        type: 'routine',
        description: 'Oil change and filter replacement',
        cost: 250,
        performed_by: 'Mike Johnson',
        performed_at: '2024-01-15T10:00:00Z',
        next_due_date: '2024-04-15',
        created_at: new Date().toISOString()
      }
    ];
  }
}
