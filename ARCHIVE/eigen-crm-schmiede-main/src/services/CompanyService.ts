
import { supabase, isSupabaseConfigured } from '@/lib/supabase';
import type { Database } from '@/lib/supabase';

type Tables = Database['public']['Tables'];
type Company = Tables['companies']['Row'];

export class CompanyService {
  private static isConfigured = isSupabaseConfigured();

  static async getCompanies(): Promise<Company[]> {
    if (!this.isConfigured) {
      return this.getMockCompanies();
    }

    try {
      const { data, error } = await supabase
        .from('companies')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching companies:', error);
      return this.getMockCompanies();
    }
  }

  static async createCompany(company: Tables['companies']['Insert']): Promise<Company | null> {
    if (!this.isConfigured) {
      console.log('Mock: Creating company:', company);
      return { 
        ...company, 
        id: `mock-${Date.now()}`, 
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      } as Company;
    }

    try {
      const { data, error } = await supabase
        .from('companies')
        .insert(company)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating company:', error);
      return null;
    }
  }

  static async updateCompany(id: string, updates: Tables['companies']['Update']): Promise<Company | null> {
    if (!this.isConfigured) {
      console.log('Mock: Updating company:', { id, updates });
      return { 
        ...updates, 
        id, 
        updated_at: new Date().toISOString()
      } as Company;
    }

    try {
      const { data, error } = await supabase
        .from('companies')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating company:', error);
      return null;
    }
  }

  static async deleteCompany(id: string): Promise<boolean> {
    if (!this.isConfigured) {
      console.log('Mock: Deleting company:', id);
      return true;
    }

    try {
      const { error } = await supabase
        .from('companies')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error deleting company:', error);
      return false;
    }
  }

  private static getMockCompanies(): Company[] {
    return [
      {
        id: 'mock-1',
        name: 'Acme Construction',
        industry: 'Construction',
        size: '50-100',
        location: 'New York, NY',
        website: 'https://acme-construction.com',
        phone: '******-0123',
        email: '<EMAIL>',
        description: 'Leading construction company specializing in commercial projects',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }
}
