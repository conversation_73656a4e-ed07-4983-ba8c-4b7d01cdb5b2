
import { logger } from './Logger';

export interface ServiceError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

export class ErrorService {
  static createError(code: string, message: string, details?: any): ServiceError {
    return {
      code,
      message,
      details,
      timestamp: new Date().toISOString()
    };
  }

  static handleServiceError(error: any, service: string, method: string): ServiceError {
    const serviceError = this.createError(
      `${service.toUpperCase()}_ERROR`,
      error.message || 'An unexpected error occurred',
      error
    );

    logger.logServiceCall(service, method, false, { 
      error: serviceError,
      originalError: error 
    });

    return serviceError;
  }

  static isNetworkError(error: any): boolean {
    return error?.code === 'NETWORK_ERROR' || 
           error?.message?.includes('fetch') ||
           error?.message?.includes('network');
  }

  static isAuthError(error: any): boolean {
    return error?.code === 'UNAUTHORIZED' || 
           error?.status === 401 ||
           error?.message?.includes('authentication');
  }

  static isValidationError(error: any): boolean {
    return error?.code === 'VALIDATION_ERROR' || 
           error?.status === 400 ||
           error?.message?.includes('validation');
  }

  static getUserFriendlyMessage(error: ServiceError): string {
    if (this.isNetworkError(error)) {
      return 'Network connection issue. Please check your internet connection.';
    }
    
    if (this.isAuthError(error)) {
      return 'Authentication required. Please log in and try again.';
    }
    
    if (this.isValidationError(error)) {
      return 'Please check your input and try again.';
    }
    
    return 'An unexpected error occurred. Please try again.';
  }
}
