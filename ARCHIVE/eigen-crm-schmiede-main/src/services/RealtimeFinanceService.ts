
import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { configManager } from './ConfigManager';
import { logger } from './Logger';

interface RealtimeEvent {
  type: 'invoice_updated' | 'expense_created' | 'payment_received';
  data: any;
  timestamp: string;
}

class RealtimeFinanceService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private listeners: ((event: RealtimeEvent) => void)[] = [];

  constructor() {
    this.connect();
  }

  private connect() {
    const config = configManager.getConfig();
    
    if (!config.features.realtime) {
      logger.info('Real-time features disabled');
      return;
    }

    try {
      // In a real app, this would connect to your WebSocket server
      // For demo purposes, we'll simulate events
      this.simulateRealtimeEvents();
      logger.info('Real-time finance service connected');
    } catch (error) {
      logger.error('Failed to connect to real-time service', error);
      this.handleReconnection();
    }
  }

  private simulateRealtimeEvents() {
    // Simulate real-time events for demo
    setInterval(() => {
      const events: RealtimeEvent[] = [
        {
          type: 'payment_received',
          data: { invoiceId: 'INV-002', amount: 180000 },
          timestamp: new Date().toISOString()
        },
        {
          type: 'invoice_updated',
          data: { invoiceId: 'INV-003', status: 'paid' },
          timestamp: new Date().toISOString()
        }
      ];

      const randomEvent = events[Math.floor(Math.random() * events.length)];
      this.notifyListeners(randomEvent);
    }, 30000); // Every 30 seconds
  }

  private handleReconnection() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error('Max reconnection attempts reached');
      return;
    }

    setTimeout(() => {
      this.reconnectAttempts++;
      logger.info(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      this.connect();
    }, this.reconnectDelay * Math.pow(2, this.reconnectAttempts));
  }

  public subscribe(callback: (event: RealtimeEvent) => void) {
    this.listeners.push(callback);
    return () => {
      this.listeners = this.listeners.filter(listener => listener !== callback);
    };
  }

  private notifyListeners(event: RealtimeEvent) {
    this.listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        logger.error('Error in realtime listener', error);
      }
    });
  }

  public disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.listeners = [];
  }
}

export const realtimeFinanceService = new RealtimeFinanceService();

export const useRealtimeFinance = () => {
  const queryClient = useQueryClient();

  useEffect(() => {
    const unsubscribe = realtimeFinanceService.subscribe((event) => {
      logger.info('Received real-time finance event', event);

      switch (event.type) {
        case 'payment_received':
        case 'invoice_updated':
          queryClient.invalidateQueries({ queryKey: ['invoices'] });
          queryClient.invalidateQueries({ queryKey: ['financeMetrics'] });
          break;
        case 'expense_created':
          queryClient.invalidateQueries({ queryKey: ['expenses'] });
          queryClient.invalidateQueries({ queryKey: ['financeMetrics'] });
          break;
      }
    });

    return unsubscribe;
  }, [queryClient]);
};
