
import { LLMService } from '../llm/LLMService'
import { logger } from '../Logger'

export interface BusinessProcess {
  id: string
  name: string
  description: string
  category: 'sales' | 'hr' | 'finance' | 'operations' | 'marketing'
  steps: ProcessStep[]
  triggers: ProcessTrigger[]
  status: 'active' | 'paused' | 'draft'
  lastRun?: string
  runCount: number
}

export interface ProcessStep {
  id: string
  name: string
  type: 'manual' | 'automated' | 'approval' | 'notification' | 'integration'
  description: string
  assignedTo?: string
  estimatedDuration: number
  configuration: Record<string, any>
}

export interface ProcessTrigger {
  type: 'schedule' | 'event' | 'webhook' | 'manual'
  configuration: Record<string, any>
}

export interface DocumentTemplate {
  id: string
  name: string
  type: 'contract' | 'proposal' | 'invoice' | 'report' | 'email'
  template: string
  variables: string[]
  category: string
}

export interface AutomationExecution {
  id: string
  processId: string
  status: 'running' | 'completed' | 'failed' | 'waiting'
  startTime: string
  endTime?: string
  currentStep: number
  results: Record<string, any>
  logs: ExecutionLog[]
}

export interface ExecutionLog {
  timestamp: string
  level: 'info' | 'warning' | 'error'
  message: string
  data?: any
}

export class BusinessOperationsAI {
  private static processes: Map<string, BusinessProcess> = new Map()
  private static templates: Map<string, DocumentTemplate> = new Map()
  private static executions: Map<string, AutomationExecution> = new Map()

  static async createProcessFromDescription(description: string): Promise<BusinessProcess> {
    logger.info('Creating business process from description')
    
    const prompt = `
    Analyze this business process description and create a structured automation workflow:
    
    "${description}"
    
    Create a JSON response with:
    - name: Process name
    - description: Detailed description
    - category: One of [sales, hr, finance, operations, marketing]
    - steps: Array of process steps with names, types, and descriptions
    - triggers: Suggested triggers for this process
    
    Each step should have:
    - name: Step name
    - type: One of [manual, automated, approval, notification, integration]
    - description: What happens in this step
    - estimatedDuration: Time in minutes
    
    Format as valid JSON.
    `

    try {
      const response = await LLMService.processRequest({
        task: 'process_automation',
        prompt,
        context: { processDescription: description }
      })

      const processData = this.parseProcessResponse(response.response)
      
      const process: BusinessProcess = {
        id: Date.now().toString(),
        ...processData,
        steps: processData.steps?.map((step: any, index: number) => ({
          ...step,
          id: `step-${index + 1}`,
          configuration: {}
        })) || [],
        status: 'draft',
        runCount: 0
      }

      this.processes.set(process.id, process)
      return process
    } catch (error) {
      logger.error('Failed to create process from AI', { error })
      throw new Error('Failed to generate business process')
    }
  }

  static async generateDocument(templateId: string, variables: Record<string, any>): Promise<string> {
    const template = this.templates.get(templateId)
    if (!template) {
      throw new Error('Template not found')
    }

    let document = template.template

    // Replace variables in template
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{${key}}}`, 'g')
      document = document.replace(regex, String(value))
    }

    // Use AI to enhance the document if needed
    const prompt = `
    Review and enhance this ${template.type} document for clarity and professionalism:
    
    ${document}
    
    Improve grammar, tone, and structure while maintaining all key information.
    `

    try {
      const response = await LLMService.processRequest({
        task: 'document_generation',
        prompt,
        context: { templateType: template.type, variables }
      })

      return response.response
    } catch (error) {
      logger.error('Failed to enhance document with AI', { error })
      return document // Return template version if AI enhancement fails
    }
  }

  static async executeProcess(processId: string, inputData: Record<string, any> = {}): Promise<string> {
    const process = this.processes.get(processId)
    if (!process) {
      throw new Error('Process not found')
    }

    const execution: AutomationExecution = {
      id: Date.now().toString(),
      processId,
      status: 'running',
      startTime: new Date().toISOString(),
      currentStep: 0,
      results: { ...inputData },
      logs: []
    }

    this.executions.set(execution.id, execution)
    
    try {
      for (let i = 0; i < process.steps.length; i++) {
        const step = process.steps[i]
        execution.currentStep = i
        
        this.addLog(execution, 'info', `Starting step: ${step.name}`)
        
        const stepResult = await this.executeStep(step, execution.results)
        Object.assign(execution.results, stepResult)
        
        this.addLog(execution, 'info', `Completed step: ${step.name}`)
        
        // Simulate step duration
        await this.delay(100)
      }

      execution.status = 'completed'
      execution.endTime = new Date().toISOString()
      
      process.runCount++
      process.lastRun = new Date().toISOString()
      
      this.addLog(execution, 'info', 'Process completed successfully')
      
    } catch (error) {
      execution.status = 'failed'
      execution.endTime = new Date().toISOString()
      this.addLog(execution, 'error', `Process failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }

    return execution.id
  }

  private static async executeStep(step: ProcessStep, context: Record<string, any>): Promise<Record<string, any>> {
    switch (step.type) {
      case 'automated':
        return await this.executeAutomatedStep(step, context)
      case 'notification':
        return await this.executeNotificationStep(step, context)
      case 'integration':
        return await this.executeIntegrationStep(step, context)
      case 'manual':
      case 'approval':
      default:
        // For manual/approval steps, we'll simulate completion
        return { [`${step.id}_result`]: 'completed' }
    }
  }

  private static async executeAutomatedStep(step: ProcessStep, context: Record<string, any>): Promise<Record<string, any>> {
    // Simulate automated step execution
    return {
      [`${step.id}_output`]: `Automated result for ${step.name}`,
      timestamp: new Date().toISOString()
    }
  }

  private static async executeNotificationStep(step: ProcessStep, context: Record<string, any>): Promise<Record<string, any>> {
    // Simulate sending notification
    logger.info(`Sending notification: ${step.name}`)
    return {
      [`${step.id}_sent`]: true,
      recipient: context.email || '<EMAIL>'
    }
  }

  private static async executeIntegrationStep(step: ProcessStep, context: Record<string, any>): Promise<Record<string, any>> {
    // Simulate integration call
    logger.info(`Executing integration: ${step.name}`)
    return {
      [`${step.id}_result`]: 'Integration completed',
      data: { ...context, processed: true }
    }
  }

  private static addLog(execution: AutomationExecution, level: 'info' | 'warning' | 'error', message: string, data?: any): void {
    execution.logs.push({
      timestamp: new Date().toISOString(),
      level,
      message,
      data
    })
  }

  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  static getProcesses(): BusinessProcess[] {
    return Array.from(this.processes.values())
  }

  static getProcess(id: string): BusinessProcess | undefined {
    return this.processes.get(id)
  }

  static getExecution(id: string): AutomationExecution | undefined {
    return this.executions.get(id)
  }

  static getExecutions(processId?: string): AutomationExecution[] {
    const executions = Array.from(this.executions.values())
    return processId ? executions.filter(e => e.processId === processId) : executions
  }

  static createTemplate(template: Omit<DocumentTemplate, 'id'>): string {
    const id = Date.now().toString()
    const newTemplate: DocumentTemplate = { ...template, id }
    this.templates.set(id, newTemplate)
    return id
  }

  static getTemplates(): DocumentTemplate[] {
    return Array.from(this.templates.values())
  }

  static getTemplate(id: string): DocumentTemplate | undefined {
    return this.templates.get(id)
  }

  private static parseProcessResponse(response: string): any {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0])
      }
    } catch (error) {
      logger.error('Failed to parse AI process response', { error })
    }

    return {
      name: 'AI Generated Process',
      description: 'Process created from AI analysis',
      category: 'operations',
      steps: [],
      triggers: []
    }
  }

  // Pre-built process templates
  static getProcessTemplates(): Array<Omit<BusinessProcess, 'id' | 'runCount' | 'lastRun'>> {
    return [
      {
        name: 'Lead Qualification Process',
        description: 'Automated lead scoring and qualification workflow',
        category: 'sales',
        status: 'draft',
        steps: [
          {
            id: 'step-1',
            name: 'Data Collection',
            type: 'automated',
            description: 'Gather lead information from multiple sources',
            estimatedDuration: 5,
            configuration: {}
          },
          {
            id: 'step-2',
            name: 'Lead Scoring',
            type: 'automated',
            description: 'Calculate lead score based on criteria',
            estimatedDuration: 2,
            configuration: {}
          },
          {
            id: 'step-3',
            name: 'Assignment',
            type: 'automated',
            description: 'Assign qualified leads to sales reps',
            estimatedDuration: 1,
            configuration: {}
          },
          {
            id: 'step-4',
            name: 'Notification',
            type: 'notification',
            description: 'Notify sales rep of new qualified lead',
            estimatedDuration: 1,
            configuration: {}
          }
        ],
        triggers: [
          {
            type: 'webhook',
            configuration: { url: '/api/webhooks/new-lead' }
          }
        ]
      },
      {
        name: 'Employee Onboarding',
        description: 'Complete new employee onboarding process',
        category: 'hr',
        status: 'draft',
        steps: [
          {
            id: 'step-1',
            name: 'Welcome Email',
            type: 'notification',
            description: 'Send welcome email with first day information',
            estimatedDuration: 2,
            configuration: {}
          },
          {
            id: 'step-2',
            name: 'Account Setup',
            type: 'automated',
            description: 'Create accounts and access permissions',
            estimatedDuration: 10,
            configuration: {}
          },
          {
            id: 'step-3',
            name: 'Manager Assignment',
            type: 'manual',
            description: 'Assign buddy and schedule first meeting',
            estimatedDuration: 5,
            configuration: {}
          },
          {
            id: 'step-4',
            name: 'Training Schedule',
            type: 'automated',
            description: 'Enroll in required training programs',
            estimatedDuration: 3,
            configuration: {}
          }
        ],
        triggers: [
          {
            type: 'event',
            configuration: { event: 'employee.hired' }
          }
        ]
      },
      {
        name: 'Invoice Processing',
        description: 'Automated invoice approval and payment workflow',
        category: 'finance',
        status: 'draft',
        steps: [
          {
            id: 'step-1',
            name: 'Invoice Validation',
            type: 'automated',
            description: 'Validate invoice data and format',
            estimatedDuration: 3,
            configuration: {}
          },
          {
            id: 'step-2',
            name: 'Approval Request',
            type: 'approval',
            description: 'Request approval from department manager',
            estimatedDuration: 1440, // 24 hours
            configuration: {}
          },
          {
            id: 'step-3',
            name: 'Payment Processing',
            type: 'integration',
            description: 'Process payment through accounting system',
            estimatedDuration: 5,
            configuration: {}
          },
          {
            id: 'step-4',
            name: 'Confirmation',
            type: 'notification',
            description: 'Send payment confirmation to vendor',
            estimatedDuration: 1,
            configuration: {}
          }
        ],
        triggers: [
          {
            type: 'schedule',
            configuration: { frequency: 'daily', time: '09:00' }
          }
        ]
      }
    ]
  }

  // Initialize with default templates
  static initializeTemplates(): void {
    const defaultTemplates: Array<Omit<DocumentTemplate, 'id'>> = [
      {
        name: 'Service Agreement',
        type: 'contract',
        category: 'legal',
        template: `
SERVICE AGREEMENT

This Service Agreement is entered into on {{date}} between {{company_name}} and {{client_name}}.

Services to be provided:
{{services_description}}

Project Timeline:
Start Date: {{start_date}}
End Date: {{end_date}}

Total Investment: {{total_amount}}

Terms and Conditions:
{{terms_and_conditions}}

Signatures:
{{company_name}}: _________________
{{client_name}}: _________________
        `,
        variables: ['date', 'company_name', 'client_name', 'services_description', 'start_date', 'end_date', 'total_amount', 'terms_and_conditions']
      },
      {
        name: 'Project Proposal',
        type: 'proposal',
        category: 'sales',
        template: `
PROJECT PROPOSAL

Prepared for: {{client_name}}
Prepared by: {{company_name}}
Date: {{date}}

PROJECT OVERVIEW
{{project_overview}}

SCOPE OF WORK
{{scope_of_work}}

TIMELINE
{{timeline_details}}

INVESTMENT
{{pricing_details}}

NEXT STEPS
{{next_steps}}

We look forward to working with you on this exciting project.

Best regards,
{{sender_name}}
{{sender_title}}
        `,
        variables: ['client_name', 'company_name', 'date', 'project_overview', 'scope_of_work', 'timeline_details', 'pricing_details', 'next_steps', 'sender_name', 'sender_title']
      },
      {
        name: 'Welcome Email',
        type: 'email',
        category: 'hr',
        template: `
Subject: Welcome to {{company_name}}, {{employee_name}}!

Dear {{employee_name}},

Welcome to the {{company_name}} team! We're excited to have you join us as our new {{position}}.

Your first day is scheduled for {{start_date}} at {{start_time}}. Please report to {{location}} where {{manager_name}} will be waiting to greet you.

What to expect on your first day:
- Office tour and introductions
- IT setup and account creation
- HR orientation and paperwork
- Team meetings

If you have any questions before your start date, please don't hesitate to reach out to {{contact_person}} at {{contact_email}}.

We're looking forward to working with you!

Best regards,
{{sender_name}}
HR Team
        `,
        variables: ['company_name', 'employee_name', 'position', 'start_date', 'start_time', 'location', 'manager_name', 'contact_person', 'contact_email', 'sender_name']
      }
    ]

    defaultTemplates.forEach(template => {
      this.createTemplate(template)
    })
  }
}

// Initialize default templates
BusinessOperationsAI.initializeTemplates()
