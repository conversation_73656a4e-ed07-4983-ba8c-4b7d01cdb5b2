
import { DatabaseService } from '../DatabaseService'
import { LLMService } from '../llm/LLMService'
import { logger } from '../Logger'

export interface KPI {
  id: string
  name: string
  value: number
  unit: string
  change: number
  trend: 'up' | 'down' | 'stable'
  target?: number
  category: 'sales' | 'finance' | 'operations' | 'hr' | 'marketing'
  description: string
  lastUpdated: string
}

export interface BusinessInsight {
  id: string
  type: 'opportunity' | 'risk' | 'trend' | 'recommendation'
  priority: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  impact: string
  actionRequired: boolean
  suggestedActions: string[]
  confidence: number
  category: string
  createdAt: string
}

export interface RevenueAnalysis {
  totalRevenue: number
  growth: number
  forecast: number
  opportunities: number
  pipeline: {
    total: number
    qualified: number
    closing: number
  }
  breakdown: {
    category: string
    amount: number
    percentage: number
  }[]
}

export interface MarketIntelligence {
  competitorAnalysis: {
    competitor: string
    marketShare: number
    strengths: string[]
    weaknesses: string[]
    recentActivity: string[]
  }[]
  marketTrends: {
    trend: string
    impact: 'positive' | 'negative' | 'neutral'
    confidence: number
    description: string
  }[]
  opportunities: {
    title: string
    description: string
    priority: 'low' | 'medium' | 'high'
    estimatedValue: number
  }[]
}

export class BusinessIntelligenceService {
  private static kpis: Map<string, KPI> = new Map()
  private static insights: BusinessInsight[] = []

  static async generateDashboardInsights(): Promise<BusinessInsight[]> {
    logger.info('Generating business intelligence insights')

    const insights: BusinessInsight[] = []

    try {
      // Analyze sales performance
      const salesInsights = await this.analyzeSalesPerformance()
      insights.push(...salesInsights)

      // Analyze financial health
      const financialInsights = await this.analyzeFinancialHealth()
      insights.push(...financialInsights)

      // Analyze operational efficiency
      const operationalInsights = await this.analyzeOperationalEfficiency()
      insights.push(...operationalInsights)

      // Update insights storage
      this.insights = insights

      return insights
    } catch (error) {
      logger.error('Failed to generate business insights', { error })
      return []
    }
  }

  private static async analyzeSalesPerformance(): Promise<BusinessInsight[]> {
    const insights: BusinessInsight[] = []

    try {
      const opportunities = await DatabaseService.getOpportunities()
      const totalValue = opportunities.reduce((sum, opp) => sum + (opp.value || 0), 0)
      const avgDealSize = totalValue / opportunities.length

      if (avgDealSize > 50000) {
        insights.push({
          id: `insight-sales-${Date.now()}`,
          type: 'opportunity',
          priority: 'medium',
          title: 'High-Value Deal Opportunity',
          description: `Average deal size is $${avgDealSize.toLocaleString()}, indicating strong market positioning`,
          impact: 'Potential for increased revenue growth',
          actionRequired: true,
          suggestedActions: [
            'Focus on qualifying larger prospects',
            'Develop premium service offerings',
            'Increase sales team capacity'
          ],
          confidence: 0.8,
          category: 'sales',
          createdAt: new Date().toISOString()
        })
      }

      const pipelineHealth = opportunities.filter(opp => 
        opp.stage === 'proposal' || opp.stage === 'negotiation'
      ).length / opportunities.length

      if (pipelineHealth < 0.3) {
        insights.push({
          id: `insight-pipeline-${Date.now()}`,
          type: 'risk',
          priority: 'high',
          title: 'Pipeline Health Concern',
          description: `Only ${Math.round(pipelineHealth * 100)}% of opportunities are in advanced stages`,
          impact: 'Revenue at risk due to weak pipeline',
          actionRequired: true,
          suggestedActions: [
            'Increase lead generation activities',
            'Improve lead qualification process',
            'Review sales process efficiency'
          ],
          confidence: 0.9,
          category: 'sales',
          createdAt: new Date().toISOString()
        })
      }
    } catch (error) {
      logger.error('Failed to analyze sales performance', { error })
    }

    return insights
  }

  private static async analyzeFinancialHealth(): Promise<BusinessInsight[]> {
    const insights: BusinessInsight[] = []

    try {
      // Mock financial analysis - in a real implementation, this would use actual financial data
      const monthlyRevenue = 150000
      const monthlyExpenses = 120000
      const profitMargin = (monthlyRevenue - monthlyExpenses) / monthlyRevenue

      if (profitMargin > 0.25) {
        insights.push({
          id: `insight-profit-${Date.now()}`,
          type: 'opportunity',
          priority: 'medium',
          title: 'Strong Profit Margins',
          description: `Current profit margin of ${Math.round(profitMargin * 100)}% indicates healthy financial performance`,
          impact: 'Strong foundation for growth investment',
          actionRequired: false,
          suggestedActions: [
            'Consider reinvestment in growth initiatives',
            'Expand into new markets',
            'Increase marketing spend'
          ],
          confidence: 0.85,
          category: 'finance',
          createdAt: new Date().toISOString()
        })
      }

      if (profitMargin < 0.1) {
        insights.push({
          id: `insight-margin-${Date.now()}`,
          type: 'risk',
          priority: 'high',
          title: 'Low Profit Margins',
          description: `Current profit margin of ${Math.round(profitMargin * 100)}% is below industry standards`,
          impact: 'Limited cash flow for growth and emergencies',
          actionRequired: true,
          suggestedActions: [
            'Review and optimize operational costs',
            'Increase pricing where possible',
            'Improve operational efficiency'
          ],
          confidence: 0.9,
          category: 'finance',
          createdAt: new Date().toISOString()
        })
      }
    } catch (error) {
      logger.error('Failed to analyze financial health', { error })
    }

    return insights
  }

  private static async analyzeOperationalEfficiency(): Promise<BusinessInsight[]> {
    const insights: BusinessInsight[] = []

    try {
      // Mock operational analysis
      const completedProjects = 8
      const totalProjects = 10
      const completionRate = completedProjects / totalProjects

      if (completionRate > 0.9) {
        insights.push({
          id: `insight-efficiency-${Date.now()}`,
          type: 'trend',
          priority: 'low',
          title: 'High Project Completion Rate',
          description: `${Math.round(completionRate * 100)}% project completion rate demonstrates strong execution`,
          impact: 'Improved client satisfaction and reputation',
          actionRequired: false,
          suggestedActions: [
            'Document successful project processes',
            'Scale winning methodologies',
            'Share best practices across teams'
          ],
          confidence: 0.9,
          category: 'operations',
          createdAt: new Date().toISOString()
        })
      }

      if (completionRate < 0.7) {
        insights.push({
          id: `insight-delays-${Date.now()}`,
          type: 'risk',
          priority: 'medium',
          title: 'Project Delivery Challenges',
          description: `${Math.round(completionRate * 100)}% completion rate indicates delivery issues`,
          impact: 'Client satisfaction and revenue at risk',
          actionRequired: true,
          suggestedActions: [
            'Review project management processes',
            'Identify common delay factors',
            'Implement better resource planning'
          ],
          confidence: 0.8,
          category: 'operations',
          createdAt: new Date().toISOString()
        })
      }
    } catch (error) {
      logger.error('Failed to analyze operational efficiency', { error })
    }

    return insights
  }

  static async generateRevenueAnalysis(): Promise<RevenueAnalysis> {
    try {
      const opportunities = await DatabaseService.getOpportunities()
      
      const totalRevenue = 1250000 // Mock current revenue
      const lastYearRevenue = 1000000
      const growth = ((totalRevenue - lastYearRevenue) / lastYearRevenue) * 100

      const pipelineTotal = opportunities.reduce((sum, opp) => sum + (opp.value || 0), 0)
      const qualifiedOpps = opportunities.filter(opp => opp.probability && opp.probability > 50)
      const closingOpps = opportunities.filter(opp => opp.stage === 'negotiation' || opp.stage === 'proposal')

      // Mock revenue breakdown by category
      const breakdown = [
        { category: 'Construction Services', amount: 600000, percentage: 48 },
        { category: 'Consulting', amount: 350000, percentage: 28 },
        { category: 'Project Management', amount: 200000, percentage: 16 },
        { category: 'Maintenance', amount: 100000, percentage: 8 }
      ]

      return {
        totalRevenue,
        growth,
        forecast: Math.round(totalRevenue * 1.2),
        opportunities: opportunities.length,
        pipeline: {
          total: pipelineTotal,
          qualified: qualifiedOpps.reduce((sum, opp) => sum + (opp.value || 0), 0),
          closing: closingOpps.reduce((sum, opp) => sum + (opp.value || 0), 0)
        },
        breakdown
      }
    } catch (error) {
      logger.error('Failed to generate revenue analysis', { error })
      return {
        totalRevenue: 0,
        growth: 0,
        forecast: 0,
        opportunities: 0,
        pipeline: { total: 0, qualified: 0, closing: 0 },
        breakdown: []
      }
    }
  }

  static async generateMarketIntelligence(): Promise<MarketIntelligence> {
    // Mock market intelligence data - in a real implementation, this would integrate with market data APIs
    return {
      competitorAnalysis: [
        {
          competitor: 'BuildCorp Solutions',
          marketShare: 25,
          strengths: ['Strong regional presence', 'Established client relationships', 'Competitive pricing'],
          weaknesses: ['Limited technology adoption', 'Slow project delivery', 'Poor digital presence'],
          recentActivity: ['Acquired local construction firm', 'Launched new safety program', 'Lost major client contract']
        },
        {
          competitor: 'Premier Construction Group',
          marketShare: 18,
          strengths: ['Advanced project management', 'Sustainability focus', 'Strong financial backing'],
          weaknesses: ['High pricing', 'Limited geographic reach', 'Complex approval process'],
          recentActivity: ['Invested in new technology platform', 'Hired senior project managers', 'Expanded to new market']
        }
      ],
      marketTrends: [
        {
          trend: 'Increased demand for sustainable construction',
          impact: 'positive',
          confidence: 0.9,
          description: 'Growing client preference for green building practices and materials'
        },
        {
          trend: 'Rising material costs',
          impact: 'negative',
          confidence: 0.85,
          description: 'Inflation affecting construction material prices, impacting project budgets'
        },
        {
          trend: 'Technology adoption in construction',
          impact: 'positive',
          confidence: 0.8,
          description: 'Increasing adoption of AI, IoT, and automation in construction processes'
        }
      ],
      opportunities: [
        {
          title: 'Smart Building Integration',
          description: 'Growing demand for IoT-enabled building systems and automation',
          priority: 'high',
          estimatedValue: 500000
        },
        {
          title: 'Renewable Energy Projects',
          description: 'Increasing investments in solar and wind energy infrastructure',
          priority: 'medium',
          estimatedValue: 750000
        },
        {
          title: 'Government Infrastructure Spending',
          description: 'New government initiatives for infrastructure development',
          priority: 'high',
          estimatedValue: 1200000
        }
      ]
    }
  }

  static async updateKPIs(): Promise<void> {
    logger.info('Updating business KPIs')

    try {
      const opportunities = await DatabaseService.getOpportunities()
      const companies = await DatabaseService.getCompanies()
      const contacts = await DatabaseService.getContacts()

      const kpis: KPI[] = [
        {
          id: 'revenue',
          name: 'Monthly Revenue',
          value: 125000,
          unit: '$',
          change: 12.5,
          trend: 'up',
          target: 150000,
          category: 'finance',
          description: 'Total revenue for current month',
          lastUpdated: new Date().toISOString()
        },
        {
          id: 'opportunities',
          name: 'Active Opportunities',
          value: opportunities.length,
          unit: 'count',
          change: 8.3,
          trend: 'up',
          target: 25,
          category: 'sales',
          description: 'Number of active sales opportunities',
          lastUpdated: new Date().toISOString()
        },
        {
          id: 'pipeline',
          name: 'Sales Pipeline Value',
          value: opportunities.reduce((sum, opp) => sum + (opp.value || 0), 0),
          unit: '$',
          change: 15.2,
          trend: 'up',
          target: 500000,
          category: 'sales',
          description: 'Total value of sales pipeline',
          lastUpdated: new Date().toISOString()
        },
        {
          id: 'clients',
          name: 'Active Clients',
          value: companies.length,
          unit: 'count',
          change: 5.1,
          trend: 'up',
          target: 50,
          category: 'sales',
          description: 'Number of active client companies',
          lastUpdated: new Date().toISOString()
        },
        {
          id: 'contacts',
          name: 'Total Contacts',
          value: contacts.length,
          unit: 'count',
          change: 22.7,
          trend: 'up',
          category: 'marketing',
          description: 'Number of contacts in database',
          lastUpdated: new Date().toISOString()
        },
        {
          id: 'profit_margin',
          name: 'Profit Margin',
          value: 28.5,
          unit: '%',
          change: -2.1,
          trend: 'down',
          target: 30,
          category: 'finance',
          description: 'Current profit margin percentage',
          lastUpdated: new Date().toISOString()
        }
      ]

      kpis.forEach(kpi => {
        this.kpis.set(kpi.id, kpi)
      })

    } catch (error) {
      logger.error('Failed to update KPIs', { error })
    }
  }

  static getKPIs(): KPI[] {
    return Array.from(this.kpis.values())
  }

  static getKPI(id: string): KPI | undefined {
    return this.kpis.get(id)
  }

  static getInsights(): BusinessInsight[] {
    return this.insights
  }

  static async generateExecutiveSummary(): Promise<string> {
    const kpis = this.getKPIs()
    const insights = this.getInsights()
    const revenueAnalysis = await this.generateRevenueAnalysis()

    const prompt = `
    Generate an executive summary based on the following business data:
    
    Key Performance Indicators:
    ${kpis.map(kpi => `- ${kpi.name}: ${kpi.value}${kpi.unit} (${kpi.change > 0 ? '+' : ''}${kpi.change}%)`).join('\n')}
    
    Business Insights:
    ${insights.map(insight => `- ${insight.title}: ${insight.description}`).join('\n')}
    
    Revenue Analysis:
    - Total Revenue: $${revenueAnalysis.totalRevenue.toLocaleString()}
    - Growth: ${revenueAnalysis.growth}%
    - Pipeline: $${revenueAnalysis.pipeline.total.toLocaleString()}
    
    Create a concise executive summary highlighting key achievements, concerns, and recommendations.
    `

    try {
      const response = await LLMService.processRequest({
        task: 'executive_summary',
        prompt,
        context: { kpis, insights, revenue: revenueAnalysis }
      })

      return response.response
    } catch (error) {
      logger.error('Failed to generate executive summary', { error })
      return 'Executive summary temporarily unavailable'
    }
  }
}

// Initialize KPIs on service load
BusinessIntelligenceService.updateKPIs()
