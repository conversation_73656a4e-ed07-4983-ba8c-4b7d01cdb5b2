
import { LLMService } from '../llm/LLMService'
import { logger } from '../Logger'

export interface Project {
  id: string
  name: string
  description: string
  status: 'planning' | 'active' | 'on-hold' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'critical'
  startDate: string
  endDate: string
  estimatedHours: number
  actualHours?: number
  budget: number
  spentBudget?: number
  tasks: ProjectTask[]
  team: TeamMember[]
  risks: ProjectRisk[]
  milestones: ProjectMilestone[]
}

export interface ProjectTask {
  id: string
  title: string
  description: string
  status: 'todo' | 'in-progress' | 'review' | 'completed' | 'blocked'
  priority: 'low' | 'medium' | 'high'
  assignedTo?: string
  estimatedHours: number
  actualHours?: number
  dependencies: string[]
  dueDate: string
  tags: string[]
}

export interface TeamMember {
  id: string
  name: string
  role: string
  email: string
  hourlyRate?: number
  availability: number // percentage
  skills: string[]
}

export interface ProjectRisk {
  id: string
  description: string
  impact: 'low' | 'medium' | 'high'
  probability: 'low' | 'medium' | 'high'
  mitigation: string
  status: 'identified' | 'mitigating' | 'resolved'
}

export interface ProjectMilestone {
  id: string
  title: string
  description: string
  dueDate: string
  status: 'pending' | 'completed' | 'delayed'
  dependencies: string[]
}

export interface ProjectRecommendation {
  type: 'timeline' | 'resource' | 'risk' | 'optimization'
  priority: 'low' | 'medium' | 'high'
  title: string
  description: string
  action: string
  impact: string
}

export class ProjectManagementAI {
  static async createProjectFromDescription(description: string): Promise<Partial<Project>> {
    logger.info('Creating project from AI description')
    
    const prompt = `
    Analyze this project description and create a structured project plan:
    
    "${description}"
    
    Provide a JSON response with:
    - name: Short project name
    - description: Detailed description
    - estimatedHours: Total estimated hours
    - budget: Estimated budget
    - tasks: Array of 5-10 key tasks with titles, descriptions, estimated hours, and priorities
    - milestones: 3-5 key milestones with titles and descriptions
    - risks: 3-5 potential risks with impact and probability assessments
    
    Format as valid JSON.
    `

    try {
      const response = await LLMService.processRequest({
        task: 'project_planning',
        prompt,
        context: { projectDescription: description }
      })

      // Parse the AI response (in a real implementation, this would be more robust)
      const projectData = this.parseProjectResponse(response.response)
      
      return {
        ...projectData,
        status: 'planning' as const,
        priority: 'medium' as const,
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
        tasks: projectData.tasks?.map((task: any, index: number) => ({
          ...task,
          id: `task-${index + 1}`,
          status: 'todo' as const,
          dependencies: [],
          dueDate: new Date(Date.now() + (index + 1) * 7 * 24 * 60 * 60 * 1000).toISOString(),
          tags: []
        })) || [],
        team: [],
        risks: projectData.risks?.map((risk: any, index: number) => ({
          ...risk,
          id: `risk-${index + 1}`,
          status: 'identified' as const,
          mitigation: risk.mitigation || 'To be determined'
        })) || [],
        milestones: projectData.milestones?.map((milestone: any, index: number) => ({
          ...milestone,
          id: `milestone-${index + 1}`,
          status: 'pending' as const,
          dependencies: [],
          dueDate: new Date(Date.now() + (index + 1) * 10 * 24 * 60 * 60 * 1000).toISOString()
        })) || []
      }
    } catch (error) {
      logger.error('Failed to create project from AI', { error })
      throw new Error('Failed to generate project plan')
    }
  }

  static async generateRecommendations(project: Project): Promise<ProjectRecommendation[]> {
    const recommendations: ProjectRecommendation[] = []

    // Timeline Analysis
    const timelineRec = await this.analyzeTimeline(project)
    if (timelineRec) recommendations.push(timelineRec)

    // Resource Analysis
    const resourceRec = await this.analyzeResources(project)
    if (resourceRec) recommendations.push(resourceRec)

    // Risk Analysis
    const riskRec = await this.analyzeRisks(project)
    if (riskRec) recommendations.push(riskRec)

    // Budget Analysis
    const budgetRec = await this.analyzeBudget(project)
    if (budgetRec) recommendations.push(budgetRec)

    return recommendations
  }

  private static async analyzeTimeline(project: Project): Promise<ProjectRecommendation | null> {
    const totalTasks = project.tasks.length
    const completedTasks = project.tasks.filter(t => t.status === 'completed').length
    const progress = totalTasks > 0 ? completedTasks / totalTasks : 0

    const daysToDeadline = Math.ceil((new Date(project.endDate).getTime() - Date.now()) / (1000 * 60 * 60 * 24))
    
    if (progress < 0.5 && daysToDeadline < 30) {
      return {
        type: 'timeline',
        priority: 'high',
        title: 'Timeline Risk Detected',
        description: `Project is ${Math.round(progress * 100)}% complete with ${daysToDeadline} days remaining`,
        action: 'Consider extending deadline or increasing resources',
        impact: 'May prevent project delays and budget overruns'
      }
    }

    return null
  }

  private static async analyzeResources(project: Project): Promise<ProjectRecommendation | null> {
    const teamUtilization = project.team.reduce((acc, member) => acc + member.availability, 0) / project.team.length
    
    if (teamUtilization > 90) {
      return {
        type: 'resource',
        priority: 'medium',
        title: 'Team Overutilization',
        description: `Team is ${Math.round(teamUtilization)}% utilized`,
        action: 'Consider adding team members or redistributing tasks',
        impact: 'Prevent burnout and maintain quality'
      }
    }

    return null
  }

  private static async analyzeRisks(project: Project): Promise<ProjectRecommendation | null> {
    const highRisks = project.risks.filter(r => r.impact === 'high' && r.probability === 'high' && r.status !== 'resolved')
    
    if (highRisks.length > 0) {
      return {
        type: 'risk',
        priority: 'high',
        title: 'High-Impact Risks Identified',
        description: `${highRisks.length} high-impact, high-probability risks need attention`,
        action: 'Review and implement mitigation strategies immediately',
        impact: 'Reduce project failure probability'
      }
    }

    return null
  }

  private static async analyzeBudget(project: Project): Promise<ProjectRecommendation | null> {
    if (project.spentBudget && project.spentBudget > project.budget * 0.8) {
      const burnRate = project.spentBudget / project.budget
      return {
        type: 'optimization',
        priority: 'high',
        title: 'Budget Burn Rate High',
        description: `${Math.round(burnRate * 100)}% of budget consumed`,
        action: 'Review spending and optimize resource allocation',
        impact: 'Prevent budget overruns'
      }
    }

    return null
  }

  private static parseProjectResponse(response: string): any {
    try {
      // Extract JSON from the response
      const jsonMatch = response.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0])
      }
    } catch (error) {
      logger.error('Failed to parse AI project response', { error })
    }

    // Fallback to manual parsing or default structure
    return {
      name: 'AI Generated Project',
      description: 'Project created from AI analysis',
      estimatedHours: 100,
      budget: 10000,
      tasks: [],
      milestones: [],
      risks: []
    }
  }

  static async optimizeTaskSchedule(project: Project): Promise<ProjectTask[]> {
    // Implement task scheduling optimization
    const optimizedTasks = [...project.tasks]
    
    // Sort by priority and dependencies
    optimizedTasks.sort((a, b) => {
      const priorityWeight = { high: 3, medium: 2, low: 1 }
      return priorityWeight[b.priority] - priorityWeight[a.priority]
    })

    return optimizedTasks
  }

  static async predictProjectCompletion(project: Project): Promise<{
    estimatedCompletion: string
    confidence: number
    factors: string[]
  }> {
    const completedTasks = project.tasks.filter(t => t.status === 'completed').length
    const totalTasks = project.tasks.length
    const progress = totalTasks > 0 ? completedTasks / totalTasks : 0

    const averageTaskDuration = project.tasks
      .filter(t => t.actualHours)
      .reduce((acc, t) => acc + (t.actualHours || 0), 0) / completedTasks || project.tasks[0]?.estimatedHours || 8

    const remainingTasks = totalTasks - completedTasks
    const estimatedRemainingDays = Math.ceil(remainingTasks * averageTaskDuration / 8)
    
    const estimatedCompletion = new Date(Date.now() + estimatedRemainingDays * 24 * 60 * 60 * 1000).toISOString()
    
    const confidence = Math.min(0.95, 0.5 + (progress * 0.5))
    
    const factors = [
      `${Math.round(progress * 100)}% tasks completed`,
      `${remainingTasks} tasks remaining`,
      `Average ${averageTaskDuration}h per task`,
      `${project.team.length} team members`,
      `${project.risks.filter(r => r.status !== 'resolved').length} active risks`
    ]

    return {
      estimatedCompletion,
      confidence,
      factors
    }
  }
}
