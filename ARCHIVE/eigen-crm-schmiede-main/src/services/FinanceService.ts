
import { performanceTracker } from './PerformanceTracker';
import type { Invoice, Expense } from '@/types/finance';

export const financeService = {
  getInvoices: async (): Promise<Invoice[]> => {
    return performanceTracker.measureAsync('getInvoices', async () => {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return [
        {
          id: 'INV-001',
          client: 'ABC Corporation',
          project: 'Downtown Office Complex',
          amount: 250000,
          status: 'paid',
          dueDate: '2024-02-15',
          issueDate: '2024-01-15',
          description: 'Phase 1 Construction - Foundation'
        },
        {
          id: 'INV-002',
          client: 'City Development',
          project: 'Highway Bridge Renovation',
          amount: 180000,
          status: 'pending',
          dueDate: '2024-03-01',
          issueDate: '2024-02-01',
          description: 'Bridge Assessment and Planning'
        },
        {
          id: 'INV-003',
          client: 'Residential Group',
          project: 'Residential Development Phase 2',
          amount: 95000,
          status: 'overdue',
          dueDate: '2024-02-20',
          issueDate: '2024-01-20',
          description: 'Site Preparation and Utilities'
        }
      ];
    });
  },

  getExpenses: async (): Promise<Expense[]> => {
    return performanceTracker.measureAsync('getExpenses', async () => {
      await new Promise(resolve => setTimeout(resolve, 800));
      
      return [
        {
          id: 'EXP-001',
          category: 'Materials',
          description: 'Steel Reinforcement Bars',
          amount: 25000,
          date: '2024-02-10',
          project: 'Downtown Office Complex',
          vendor: 'Steel Supply Co.',
          status: 'approved'
        },
        {
          id: 'EXP-002',
          category: 'Equipment',
          description: 'Crane Rental - 3 weeks',
          amount: 18000,
          date: '2024-02-15',
          project: 'Highway Bridge Renovation',
          vendor: 'Heavy Equipment Rental',
          status: 'pending'
        },
        {
          id: 'EXP-003',
          category: 'Labor',
          description: 'Overtime - Weekend Work',
          amount: 8500,
          date: '2024-02-18',
          project: 'Residential Development Phase 2',
          vendor: 'Internal',
          status: 'approved'
        }
      ];
    });
  },

  createInvoice: async (invoice: Omit<Invoice, 'id'>): Promise<Invoice> => {
    return performanceTracker.measureAsync('createInvoice', async () => {
      await new Promise(resolve => setTimeout(resolve, 1200));
      return {
        ...invoice,
        id: `INV-${Date.now()}`
      };
    });
  },

  updateInvoiceStatus: async (id: string, status: Invoice['status']): Promise<Invoice> => {
    return performanceTracker.measureAsync('updateInvoiceStatus', async () => {
      await new Promise(resolve => setTimeout(resolve, 600));
      const invoices = await financeService.getInvoices();
      const invoice = invoices.find(inv => inv.id === id);
      if (!invoice) throw new Error('Invoice not found');
      return { ...invoice, status };
    });
  }
};
