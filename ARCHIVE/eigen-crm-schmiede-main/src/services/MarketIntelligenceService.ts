
import { MarketTrendsService } from './market/MarketTrendsService'
import { CompetitorAnalysisService } from './market/CompetitorAnalysisService'
import { OpportunityDiscoveryService } from './market/OpportunityDiscoveryService'
import { CompetitorMonitoringService } from './market/CompetitorMonitoringService'

export type { MarketTrend, CompetitorInsight, OpportunityDiscovery } from '@/types/market'

export class MarketIntelligenceService {
  private marketTrendsService: MarketTrendsService
  private competitorAnalysisService: CompetitorAnalysisService
  private opportunityDiscoveryService: OpportunityDiscoveryService
  private competitorMonitoringService: CompetitorMonitoringService

  constructor() {
    this.marketTrendsService = new MarketTrendsService()
    this.competitorAnalysisService = new CompetitorAnalysisService()
    this.opportunityDiscoveryService = new OpportunityDiscoveryService()
    this.competitorMonitoringService = new CompetitorMonitoringService()
  }

  async identifyMarketTrends() {
    return this.marketTrendsService.identifyMarketTrends()
  }

  async analyzeCompetitors() {
    return this.competitorAnalysisService.analyzeCompetitors()
  }

  async discoverOpportunities() {
    return this.opportunityDiscoveryService.discoverOpportunities()
  }

  async monitorCompetitorMoves() {
    return this.competitorMonitoringService.monitorCompetitorMoves()
  }
}

export const marketIntelligenceService = new MarketIntelligenceService()
