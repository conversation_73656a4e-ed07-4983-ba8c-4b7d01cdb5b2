
import { useQuery } from '@tanstack/react-query';
import { useInvoices } from './useInvoices';
import { useExpenses } from './useExpenses';
import type { FinanceMetrics } from '@/types/finance';

export const useFinanceMetrics = () => {
  const { data: invoices } = useInvoices();
  const { data: expenses } = useExpenses();
  
  return useQuery({
    queryKey: ['financeMetrics', invoices, expenses],
    queryFn: (): FinanceMetrics => {
      if (!invoices || !expenses) {
        return {
          totalRevenue: 0,
          paidInvoices: 0,
          pendingInvoices: 0,
          totalExpenses: 0,
          netProfit: 0
        };
      }
      
      const totalRevenue = invoices.reduce((sum, inv) => sum + inv.amount, 0);
      const paidInvoices = invoices.filter(inv => inv.status === 'paid').reduce((sum, inv) => sum + inv.amount, 0);
      const pendingInvoices = invoices.filter(inv => inv.status === 'pending').reduce((sum, inv) => sum + inv.amount, 0);
      const totalExpenses = expenses.filter(exp => exp.status === 'approved').reduce((sum, exp) => sum + exp.amount, 0);
      
      return {
        totalRevenue,
        paidInvoices,
        pendingInvoices,
        totalExpenses,
        netProfit: paidInvoices - totalExpenses
      };
    },
    enabled: !!invoices && !!expenses
  });
};
