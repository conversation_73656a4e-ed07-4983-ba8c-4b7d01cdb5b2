
import { useState } from 'react';
import { isAfter, isBefore } from 'date-fns';
import { Project } from '@/types/project';

interface Filters {
  search: string;
  status: string;
  category: string;
  priority: string;
  budgetMin: string;
  budgetMax: string;
  startDate: Date | undefined;
  endDate: Date | undefined;
  manager: string;
}

export const useProjectFilters = (projects: Project[]) => {
  const [filters, setFilters] = useState<Filters>({
    search: '',
    status: 'all',
    category: 'all',
    priority: 'all',
    budgetMin: '',
    budgetMax: '',
    startDate: undefined,
    endDate: undefined,
    manager: ''
  });

  const filteredProjects = projects.filter(project => {
    const matchesSearch = 
      project.name.toLowerCase().includes(filters.search.toLowerCase()) ||
      project.clientName.toLowerCase().includes(filters.search.toLowerCase()) ||
      project.location.toLowerCase().includes(filters.search.toLowerCase());
    
    const matchesStatus = filters.status === 'all' || project.status === filters.status;
    const matchesCategory = filters.category === 'all' || project.category === filters.category;
    const matchesPriority = filters.priority === 'all' || project.priority === filters.priority;
    
    const matchesBudgetMin = !filters.budgetMin || project.estimatedBudget >= parseFloat(filters.budgetMin);
    const matchesBudgetMax = !filters.budgetMax || project.estimatedBudget <= parseFloat(filters.budgetMax);
    
    const matchesStartDate = !filters.startDate || !isBefore(new Date(project.startDate), filters.startDate);
    const matchesEndDate = !filters.endDate || !isAfter(new Date(project.endDate), filters.endDate);
    
    return matchesSearch && matchesStatus && matchesCategory && matchesPriority && 
           matchesBudgetMin && matchesBudgetMax && matchesStartDate && matchesEndDate;
  });

  const resetFilters = () => {
    setFilters({
      search: '',
      status: 'all',
      category: 'all',
      priority: 'all',
      budgetMin: '',
      budgetMax: '',
      startDate: undefined,
      endDate: undefined,
      manager: ''
    });
  };

  return {
    filters,
    setFilters,
    filteredProjects,
    resetFilters
  };
};
