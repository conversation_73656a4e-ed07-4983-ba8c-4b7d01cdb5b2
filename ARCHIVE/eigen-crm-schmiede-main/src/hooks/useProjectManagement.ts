
import { useState } from 'react';
import { Project, ProjectFormData } from '@/types/project';

export const useProjectManagement = (initialProjects: Project[]) => {
  const [projects, setProjects] = useState<Project[]>(initialProjects);
  const [showForm, setShowForm] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);

  const handleAddProject = (data: ProjectFormData) => {
    const newProject: Project = {
      id: Date.now().toString(),
      ...data,
      status: data.status as Project['status'],
      priority: data.priority as Project['priority'],
      category: data.category as Project['category'],
      estimatedBudget: parseFloat(data.estimatedBudget),
      actualBudget: 0,
      progress: 0,
      clientId: Date.now().toString(),
      team: [],
      milestones: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    setProjects(prev => [...prev, newProject]);
    setShowForm(false);
  };

  const handleEditProject = (data: ProjectFormData) => {
    if (editingProject) {
      setProjects(prev => prev.map(project => 
        project.id === editingProject.id 
          ? { 
              ...project, 
              ...data,
              status: data.status as Project['status'],
              priority: data.priority as Project['priority'],
              category: data.category as Project['category'],
              estimatedBudget: parseFloat(data.estimatedBudget),
              updatedAt: new Date().toISOString()
            }
          : project
      ));
      setEditingProject(null);
    }
  };

  return {
    projects,
    showForm,
    setShowForm,
    editingProject,
    setEditingProject,
    handleAddProject,
    handleEditProject
  };
};
