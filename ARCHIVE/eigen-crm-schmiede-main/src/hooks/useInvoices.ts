
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { errorHandler } from '@/services/ErrorHandler';
import { useLoadingStore } from '@/stores/loadingStore';
import { financeService } from '@/services/FinanceService';
import type { Invoice } from '@/types/finance';

export const useInvoices = () => {
  const { setComponentLoading } = useLoadingStore();
  
  return useQuery({
    queryKey: ['invoices'],
    queryFn: async () => {
      setComponentLoading('invoices', true, 'Loading invoices...');
      try {
        const data = await financeService.getInvoices();
        return data;
      } catch (error) {
        errorHandler.handleError(error as Error, {
          component: 'Finance',
          action: 'loadInvoices',
          severity: 'medium',
          category: 'api'
        });
        throw error;
      } finally {
        setComponentLoading('invoices', false);
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2
  });
};

export const useCreateInvoice = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: financeService.createInvoice,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
      queryClient.invalidateQueries({ queryKey: ['financeMetrics'] });
    },
    onError: (error) => {
      errorHandler.handleError(error as Error, {
        component: 'Finance',
        action: 'createInvoice',
        severity: 'high',
        category: 'api'
      });
    }
  });
};

export const useUpdateInvoiceStatus = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: Invoice['status'] }) =>
      financeService.updateInvoiceStatus(id, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
      queryClient.invalidateQueries({ queryKey: ['financeMetrics'] });
    },
    onError: (error) => {
      errorHandler.handleError(error as Error, {
        component: 'Finance',
        action: 'updateInvoiceStatus',
        severity: 'medium',
        category: 'api'
      });
    }
  });
};
