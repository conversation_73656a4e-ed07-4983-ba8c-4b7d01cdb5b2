
import React, { createContext, useContext, useEffect, ReactNode, useState } from 'react';
import { SmartRouter } from '@/services/provider/SmartRouter';
import { LLMService } from '@/services/llm/LLMService';
import { RoutingRequest, ProviderResponse, RoutingProfile } from '@/types/provider';
import { conversationService } from '@/services/ConversationService';
import { agentService } from '@/services/AgentService';
import { vectorDB } from '@/services/VectorDatabase';

interface EnhancedAIContextType {
  sendMessage: (message: string, options?: {
    dataSensitivity?: 'low' | 'medium' | 'high';
    maxCost?: number;
    preferredProfile?: RoutingProfile;
  }) => Promise<void>;
  initializeAgents: () => Promise<void>;
  startRealTimeUpdates: () => void;
  router: SmartRouter;
  isInitialized: boolean;
  currentProfile: RoutingProfile;
  setRoutingProfile: (profile: RoutingProfile) => void;
  getUsageStats: () => any;
}

const EnhancedAIContext = createContext<EnhancedAIContextType | null>(null);

export const useEnhancedAI = () => {
  const context = useContext(EnhancedAIContext);
  if (!context) {
    throw new Error('useEnhancedAI must be used within EnhancedAIProvider');
  }
  return context;
};

interface EnhancedAIProviderProps {
  children: ReactNode;
}

export const EnhancedAIProvider: React.FC<EnhancedAIProviderProps> = ({ children }) => {
  const [router] = useState(() => new SmartRouter());
  const [isInitialized, setIsInitialized] = useState(false);
  const [currentProfile, setCurrentProfile] = useState<RoutingProfile>('balanced');

  const sendMessage = async (
    message: string, 
    options: {
      dataSensitivity?: 'low' | 'medium' | 'high';
      maxCost?: number;
      preferredProfile?: RoutingProfile;
    } = {}
  ) => {
    const request: RoutingRequest = {
      taskType: inferTaskType(message),
      prompt: message,
      dataSensitivity: options.dataSensitivity || 'medium',
      maxCost: options.maxCost,
      preferredProfile: options.preferredProfile || currentProfile
    };

    console.log('Processing enhanced AI request:', request);

    const selectedProvider = router.selectProvider(request);
    
    if (!selectedProvider) {
      console.error('No suitable provider found for request');
      throw new Error('No available AI providers meet the current requirements');
    }

    console.log('Selected provider:', selectedProvider.name);

    try {
      // Use the existing conversation service but with provider awareness
      await conversationService.processMessage(message);
      
      // Record successful usage (mock values for now)
      router.recordUsage(selectedProvider.id, 100, selectedProvider.costPerRequest);
      
    } catch (error) {
      console.error('AI request failed:', error);
      throw error;
    }
  };

  const initializeAgents = async () => {
    await agentService.initializeAgents();
  };

  const startRealTimeUpdates = () => {
    return agentService.startRealTimeUpdates();
  };

  const setRoutingProfile = (profile: RoutingProfile) => {
    setCurrentProfile(profile);
    localStorage.setItem('ai_routing_profile', profile);
    console.log('Routing profile changed to:', profile);
  };

  const getUsageStats = () => {
    return router.getUsageStats();
  };

  const initializeRAG = async () => {
    try {
      console.log('Initializing enhanced RAG system...');
      await vectorDB.initialize();
      console.log('Enhanced RAG system initialized successfully');
    } catch (error) {
      console.warn('RAG initialization failed, continuing without vector search:', error);
    }
  };

  useEffect(() => {
    const initialize = async () => {
      // Load saved routing profile
      const savedProfile = localStorage.getItem('ai_routing_profile') as RoutingProfile;
      if (savedProfile) {
        setCurrentProfile(savedProfile);
      }

      await initializeAgents();
      await initializeRAG();
      const cleanup = startRealTimeUpdates();
      
      setIsInitialized(true);
      console.log('Enhanced AI system initialized');
      
      return cleanup;
    };
    
    const cleanup = initialize();
    
    return () => {
      cleanup.then(fn => fn && fn());
    };
  }, []);

  const value: EnhancedAIContextType = {
    sendMessage,
    initializeAgents,
    startRealTimeUpdates,
    router,
    isInitialized,
    currentProfile,
    setRoutingProfile,
    getUsageStats
  };

  return (
    <EnhancedAIContext.Provider value={value}>
      {children}
    </EnhancedAIContext.Provider>
  );
};

// Helper function to infer task type from message
function inferTaskType(message: string): any {
  const lowerMessage = message.toLowerCase();
  
  if (lowerMessage.includes('code') || lowerMessage.includes('programming') || lowerMessage.includes('function')) {
    return 'code_generation';
  }
  if (lowerMessage.includes('research') || lowerMessage.includes('find') || lowerMessage.includes('search')) {
    return 'research';
  }
  if (lowerMessage.includes('analyze') || lowerMessage.includes('analysis')) {
    return 'analysis';
  }
  if (lowerMessage.includes('creative') || lowerMessage.includes('story') || lowerMessage.includes('poem')) {
    return 'creative';
  }
  if (lowerMessage.includes('translate')) {
    return 'translation';
  }
  
  return 'conversation';
}
