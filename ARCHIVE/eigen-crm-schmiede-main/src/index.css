
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 250 250 250;
    --foreground: 60 9% 18%;

    --card: 255 255 255;
    --card-foreground: 60 9% 18%;

    --popover: 255 255 255;
    --popover-foreground: 60 9% 18%;

    --primary: 157 23% 44%;
    --primary-foreground: 0 0% 98%;

    --secondary: 210 14% 89%;
    --secondary-foreground: 60 9% 18%;

    --muted: 210 14% 95%;
    --muted-foreground: 25 5% 45%;

    --accent: 142 36% 76%;
    --accent-foreground: 60 9% 18%;

    --destructive: 0 72% 51%;
    --destructive-foreground: 0 0% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 157 23% 44%;

    --radius: 0.5rem;

    --sidebar-background: 250 250 250;
    --sidebar-foreground: 60 9% 18%;
    --sidebar-primary: 157 23% 44%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 210 14% 95%;
    --sidebar-accent-foreground: 60 9% 18%;
    --sidebar-border: 214 32% 91%;
    --sidebar-ring: 157 23% 44%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    font-weight: 400;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .glass-card {
    @apply bg-white/90 backdrop-blur-sm border border-border rounded-lg shadow-sm;
  }
  
  .gradient-text {
    @apply bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent;
  }
  
  .hover-glow {
    @apply transition-all duration-200 hover:shadow-md;
  }
}
