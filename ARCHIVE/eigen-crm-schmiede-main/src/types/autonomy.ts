
export type AutonomyLevel = 'manual' | 'guided' | 'supervised' | 'autonomous' | 'full-autonomous'

export type ActionRiskLevel = 'low' | 'medium' | 'high' | 'critical'

export interface ApprovalRequest {
  id: string
  agentId: string
  actionType: string
  actionName: string
  description: string
  parameters: Record<string, any>
  riskLevel: ActionRiskLevel
  timestamp: string
  status: 'pending' | 'approved' | 'rejected' | 'expired'
  requiredBy?: string
  approvedBy?: string
  approvedAt?: string
  expiresAt: string
}

export interface AutonomyConfiguration {
  globalAutonomyLevel: AutonomyLevel
  agentSpecificLevels: Record<string, AutonomyLevel>
  approvalTimeout: number // minutes
  emergencyStopEnabled: boolean
  batchApprovalEnabled: boolean
  autoApprovalPatterns: string[]
}

export interface ActionClassification {
  name: string
  riskLevel: ActionRiskLevel
  category: string
  requiresApprovalAt: AutonomyLevel[]
  description: string
}
