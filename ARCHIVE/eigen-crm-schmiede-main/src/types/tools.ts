
export interface ToolParameter {
  name: string
  type: 'string' | 'number' | 'boolean' | 'object' | 'array'
  required: boolean
  description: string
  default?: any
}

export interface ToolDefinition {
  id: string
  name: string
  description: string
  category: 'communication' | 'crm' | 'calendar' | 'social' | 'database' | 'workflow' | 'api'
  version: string
  parameters: ToolParameter[]
  icon?: string
  status: 'active' | 'inactive' | 'error' | 'configuring'
  lastUsed?: string
  usageCount: number
  configuration?: Record<string, any>
}

export interface ToolExecution {
  id: string
  toolId: string
  agentId: string
  parameters: Record<string, any>
  timestamp: string
  status: 'pending' | 'running' | 'success' | 'error'
  result?: any
  error?: string
  duration?: number
}

export interface ToolExecutionRequest {
  toolId: string
  parameters: Record<string, any>
  agentId: string
  timeout?: number
}

export interface ToolExecutionResult {
  success: boolean
  data?: any
  error?: string
  duration: number
  metadata?: Record<string, any>
}
