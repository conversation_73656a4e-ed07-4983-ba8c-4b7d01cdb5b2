
export interface Vehicle {
  id: string;
  name: string;
  type: 'truck' | 'excavator' | 'crane' | 'bulldozer' | 'van' | 'car';
  make?: string;
  model?: string;
  year?: number;
  license_plate?: string;
  vin?: string;
  status: 'available' | 'in_use' | 'maintenance' | 'out_of_service';
  location?: string;
  mileage?: number;
  fuel_level?: number;
  last_maintenance?: string;
  next_maintenance?: string;
  assigned_to?: string;
  project_id?: string;
  created_at: string;
  updated_at: string;
}

export interface MaintenanceRecord {
  id: string;
  vehicle_id: string;
  type: 'routine' | 'repair' | 'inspection';
  description?: string;
  cost?: number;
  performed_by?: string;
  performed_at?: string;
  next_due_date?: string;
  created_at: string;
}

export interface FleetStats {
  total: number;
  available: number;
  in_use: number;
  maintenance: number;
  out_of_service: number;
}
