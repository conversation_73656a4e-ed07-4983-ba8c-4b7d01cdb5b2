
export type ProviderType = 'ollama' | 'openai' | 'anthropic' | 'google' | 'perplexity' | 'huggingface';
export type TaskType = 'research' | 'analysis' | 'conversation' | 'code_generation' | 'creative' | 'translation';
export type RoutingProfile = 'privacy' | 'efficiency' | 'cost' | 'balanced';

export interface ProviderConfig {
  id: string;
  name: string;
  type: ProviderType;
  apiKey?: string;
  baseUrl?: string;
  models: string[];
  isLocal: boolean;
  privacyScore: number; // 1-10 (10 = most private)
  costPerRequest: number; // in cents
  rateLimits: {
    requestsPerMinute: number;
    requestsPerDay: number;
    tokensPerMinute?: number;
    tokensPerDay?: number;
  };
  capabilities: TaskType[];
  averageResponseTime: number; // in ms
  reliability: number; // 0-1 (1 = most reliable)
}

export interface ProviderUsage {
  providerId: string;
  requestsToday: number;
  requestsThisMinute: number;
  tokensToday: number;
  tokensThisMinute: number;
  lastRequestTime: number;
  totalCost: number;
  lastResetDate: string;
}

export interface RoutingRequest {
  taskType: TaskType;
  prompt: string;
  context?: any;
  dataSensitivity: 'low' | 'medium' | 'high';
  maxCost?: number;
  preferredProfile: RoutingProfile;
}

export interface ProviderResponse {
  providerId: string;
  response: string;
  model: string;
  cost: number;
  responseTime: number;
  tokensUsed: number;
}
