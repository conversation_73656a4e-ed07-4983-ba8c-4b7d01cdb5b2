
export interface Person {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  position?: string;
  company?: string;
  companyId?: string;
  avatar?: string;
  type: 'client' | 'contractor' | 'employee' | 'vendor';
  status: 'active' | 'inactive';
  tags: string[];
  notes?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  };
  socialMedia?: {
    linkedin?: string;
    twitter?: string;
    website?: string;
  };
  lastContact?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PersonFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  position: string;
  company: string;
  type: 'client' | 'contractor' | 'employee' | 'vendor';
  status: 'active' | 'inactive';
  notes: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  linkedin: string;
  twitter: string;
  website: string;
}
