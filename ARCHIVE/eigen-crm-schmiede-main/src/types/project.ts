
export interface Project {
  id: string;
  name: string;
  description: string;
  status: 'planning' | 'active' | 'on-hold' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'critical';
  startDate: string;
  endDate: string;
  estimatedBudget: number;
  actualBudget: number;
  progress: number; // 0-100
  clientId: string;
  clientName: string;
  projectManager: string;
  team: TeamMember[];
  location: string;
  category: 'residential' | 'commercial' | 'infrastructure' | 'renovation';
  milestones: Milestone[];
  createdAt: string;
  updatedAt: string;
}

export interface TeamMember {
  id: string;
  name: string;
  role: string;
  email: string;
  avatar?: string;
}

export interface Milestone {
  id: string;
  title: string;
  description: string;
  dueDate: string;
  completed: boolean;
  completedDate?: string;
}

export interface ProjectFormData {
  name: string;
  description: string;
  status: 'planning' | 'active' | 'on-hold' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'critical';
  startDate: string;
  endDate: string;
  estimatedBudget: string;
  clientName: string;
  projectManager: string;
  location: string;
  category: 'residential' | 'commercial' | 'infrastructure' | 'renovation';
}
