
version: '3.8'

services:
  # Main CRM Application
  crm-app:
    build: 
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:80"
      - "8443:443"
    environment:
      - VITE_APP_ENV=${VITE_APP_ENV:-production}
      - VITE_SUPABASE_URL=${VITE_SUPABASE_URL}
      - VITE_SUPABASE_ANON_KEY=${VITE_SUPABASE_ANON_KEY}
      - VITE_AI_PROVIDER=${VITE_AI_PROVIDER:-ollama}
      - VITE_OLLAMA_URL=http://ollama:11434
    volumes:
      - ./ssl:/etc/ssl/certs:ro
      - ./logs:/var/log/nginx
    depends_on:
      - supabase-db
      - ollama
    restart: unless-stopped
    networks:
      - crm-network

  # Local AI (Ollama)
  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
      - ./ollama/models:/models:ro
    environment:
      - OLLAMA_ORIGINS=*
      - OLLAMA_HOST=0.0.0.0
    restart: unless-stopped
    networks:
      - crm-network
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # Supabase Database
  supabase-db:
    image: supabase/postgres:**********
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-postgres}
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-your_secure_password}
      - POSTGRES_HOST_AUTH_METHOD=md5
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
      - ./database/seed.sql:/docker-entrypoint-initdb.d/02-seed.sql:ro
    restart: unless-stopped
    networks:
      - crm-network

  # Supabase Auth (GoTrue)
  supabase-auth:
    image: supabase/gotrue:v2.132.3
    ports:
      - "9999:9999"
    environment:
      - API_EXTERNAL_URL=${API_EXTERNAL_URL:-http://localhost:8080}
      - GOTRUE_API_HOST=0.0.0.0
      - GOTRUE_API_PORT=9999
      - GOTRUE_DB_DRIVER=postgres
      - GOTRUE_DB_DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-your_secure_password}@supabase-db:5432/postgres?search_path=auth
      - GOTRUE_SITE_URL=${SITE_URL:-http://localhost:8080}
      - GOTRUE_URI_ALLOW_LIST=${GOTRUE_URI_ALLOW_LIST:-*}
      - GOTRUE_JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-token-with-at-least-32-characters-long}
      - GOTRUE_JWT_EXP=3600
      - GOTRUE_JWT_DEFAULT_GROUP_NAME=authenticated
      - GOTRUE_JWT_ADMIN_ROLES=service_role
      - GOTRUE_JWT_AUD=authenticated
      - GOTRUE_JWT_DEFAULT_GROUP_NAME=authenticated
    depends_on:
      - supabase-db
    restart: unless-stopped
    networks:
      - crm-network

  # Supabase REST API (PostgREST)
  supabase-rest:
    image: postgrest/postgrest:v11.2.0
    ports:
      - "3000:3000"
    environment:
      - PGRST_DB_URI=postgresql://postgres:${POSTGRES_PASSWORD:-your_secure_password}@supabase-db:5432/postgres
      - PGRST_DB_SCHEMAS=public
      - PGRST_DB_ANON_ROLE=anon
      - PGRST_JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-token-with-at-least-32-characters-long}
      - PGRST_DB_USE_LEGACY_GUCS=false
    depends_on:
      - supabase-db
    restart: unless-stopped
    networks:
      - crm-network

  # Supabase Realtime
  supabase-realtime:
    image: supabase/realtime:v2.25.50
    ports:
      - "4000:4000"
    environment:
      - PORT=4000
      - DB_HOST=supabase-db
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=${POSTGRES_PASSWORD:-your_secure_password}
      - DB_NAME=postgres
      - DB_AFTER_CONNECT_QUERY=SET search_path TO _realtime
      - DB_ENC_KEY=supabaserealtime
      - API_JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-token-with-at-least-32-characters-long}
      - FLY_ALLOC_ID=fly123
      - FLY_APP_NAME=realtime
      - SECRET_KEY_BASE=${SECRET_KEY_BASE:-UpNVntn3cDxHJpq99YMc1T1AQgQpc8kfYTuRgBiYa15BLrx8en4CX3bAwd8BYa95}
      - ERL_AFLAGS=-proto_dist inet_tcp
      - ENABLE_TAILSCALE=false
      - DNS_NODES='<EMAIL>'
    depends_on:
      - supabase-db
    restart: unless-stopped
    networks:
      - crm-network

  # Redis (for caching and sessions)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-your_redis_password}
    restart: unless-stopped
    networks:
      - crm-network

  # Vector Database (for AI embeddings)
  qdrant:
    image: qdrant/qdrant:v1.7.4
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
    restart: unless-stopped
    networks:
      - crm-network

  # Monitoring (optional)
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - crm-network
    profiles: ["monitoring"]

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    restart: unless-stopped
    networks:
      - crm-network
    profiles: ["monitoring"]

volumes:
  postgres_data:
    driver: local
  ollama_data:
    driver: local
  redis_data:
    driver: local
  qdrant_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  crm-network:
    driver: bridge
