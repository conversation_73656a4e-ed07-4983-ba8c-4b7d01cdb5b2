const jwt = require('jsonwebtoken');
const db = require('../models');
const { AuthenticationError, NotFoundError } = require('../utils/errors');
const tenantService = require('../services/tenantService');

/**
 * Middleware to extract and validate tenant context
 * This determines the tenant based on various sources:
 * 1. JWT token (for authenticated requests)
 * 2. Hostname/domain (for multi-domain setups)
 * 3. Explicit tenantId in request (for superadmin operations)
 */
const tenantMiddleware = async (req, res, next) => {
  try {
    // Skip tenant resolution for public routes
    if (isPublicRoute(req.path)) {
      return next();
    }

    // For authentication routes, we only set tenant if it's explicitly provided
    if (isAuthRoute(req.path) && req.body && req.body.tenant) {
      try {
        const tenant = await tenantService.getTenantBySlug(req.body.tenant);
        if (tenant && tenant.isActive) {
          req.tenantId = tenant.id;
          req.tenantSlug = tenant.slug;
          req.tenantData = tenant;
        }
      } catch (error) {
        // For auth routes, we don't require a tenant to be found
        console.warn(`Auth route tenant resolution failed: ${error.message}`);
      }
      return next();
    }

    // First priority: Check JWT token for tenant information
    if (req.user && req.user.tenantId) {
      const tenant = await tenantService.getTenantById(req.user.tenantId);
      
      if (!tenant || !tenant.isActive) {
        return next(new AuthenticationError('Tenant is inactive or does not exist'));
      }
      
      req.tenantId = tenant.id;
      req.tenantSlug = tenant.slug;
      req.tenantData = tenant;
      
      return next();
    }

    // Second priority: Check hostname for subdomain or domain matching
    const hostname = req.hostname || '';
    
    // Try to match domain directly
    try {
      const tenant = await tenantService.getTenantByDomain(hostname);
      if (tenant && tenant.isActive) {
        req.tenantId = tenant.id;
        req.tenantSlug = tenant.slug;
        req.tenantData = tenant;
        return next();
      }
    } catch (error) {
      // Domain not found, continue to next resolution method
    }
    
    // Try to extract tenant from subdomain (tenant-name.example.com)
    const subdomainMatch = hostname.match(/^([^.]+)/);
    if (subdomainMatch && subdomainMatch[1] !== 'www') {
      try {
        const tenant = await tenantService.getTenantBySlug(subdomainMatch[1]);
        if (tenant && tenant.isActive) {
          req.tenantId = tenant.id;
          req.tenantSlug = tenant.slug;
          req.tenantData = tenant;
          return next();
        }
      } catch (error) {
        // Subdomain not matched to tenant, continue to next resolution method
      }
    }

    // Third priority: Explicit tenantId in request (for superadmin operations)
    // Note: This should only be allowed for superadmin users to prevent tenant data leakage
    if (req.query.tenantId && req.user && req.user.role === 'superadmin') {
      try {
        const tenant = await tenantService.getTenantById(req.query.tenantId);
        req.tenantId = tenant.id;
        req.tenantSlug = tenant.slug;
        req.tenantData = tenant;
        req.isCrossTenantRequest = true; // Flag to indicate admin is accessing another tenant
        return next();
      } catch (error) {
        return next(new NotFoundError('Tenant not found'));
      }
    }

    // If we get here for a protected route, no tenant was resolved
    if (isProtectedRoute(req.path)) {
      return next(new AuthenticationError('Tenant context not found'));
    }

    // For other routes, we proceed without tenant context
    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Check if a route is public (no auth or tenant required)
 * @param {string} path - Request path
 * @returns {boolean} - Is this a public route
 */
function isPublicRoute(path) {
  const publicPaths = [
    '/api/health',
    '/api/docs',
    '/api/public/',
    '/api/webhook/' // Webhooks might need tenant context but are handled separately
  ];
  
  return publicPaths.some(publicPath => path.startsWith(publicPath));
}

/**
 * Check if a route is an authentication route
 * @param {string} path - Request path
 * @returns {boolean} - Is this an auth route
 */
function isAuthRoute(path) {
  return path === '/api/auth/login' || 
         path === '/api/auth/register' || 
         path === '/api/auth/refresh-token';
}

/**
 * Check if a route requires tenant context
 * @param {string} path - Request path
 * @returns {boolean} - Does this route require tenant context
 */
function isProtectedRoute(path) {
  // All API routes except public routes and auth routes require tenant context
  return path.startsWith('/api/') && 
         !isPublicRoute(path) && 
         !isAuthRoute(path) &&
         !path.startsWith('/api/admin/'); // Admin routes handled separately
}

/**
 * Creates a middleware that checks if a user has tenant access
 * @param {string[]} allowedRoles - Allowed roles for this route
 * @returns {Function} - Express middleware
 */
const requireTenantAccess = (allowedRoles = ['admin', 'manager', 'agent']) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new AuthenticationError('Authentication required'));
    }
    
    if (!req.tenantId) {
      return next(new AuthenticationError('Tenant context required'));
    }
    
    // For cross-tenant requests, only superadmins are allowed
    if (req.isCrossTenantRequest && req.user.role !== 'superadmin') {
      return next(new AuthenticationError('Insufficient permissions for cross-tenant access'));
    }
    
    // Check if user's role is in allowed roles
    if (!allowedRoles.includes(req.user.role) && req.user.role !== 'superadmin') {
      return next(new AuthenticationError('Insufficient permissions'));
    }
    
    // If it's not a cross-tenant request, verify user belongs to the tenant
    if (!req.isCrossTenantRequest && req.user.tenantId !== req.tenantId) {
      return next(new AuthenticationError('User does not belong to this tenant'));
    }
    
    next();
  };
};

module.exports = { 
  tenantMiddleware, 
  requireTenantAccess 
};
