#!/bin/bash

# SPQR-Twenty Deployment Script
# Deploys the integrated SPQR-Twenty system to the server

set -e

# Configuration
SERVER_HOST="**************"
SERVER_USER="vince"
REMOTE_DIR="/home/<USER>/spqr-twenty"
LOCAL_DIR="$(pwd)"
SERVICE_NAME="spqr-twenty"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting SPQR-Twenty Deployment...${NC}"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we can connect to the server
echo -e "${BLUE}🔍 Checking server connection...${NC}"
if ! ssh -o ConnectTimeout=10 ${SERVER_USER}@${SERVER_HOST} "echo 'Connection successful'"; then
    print_error "Cannot connect to server ${SERVER_HOST}"
    exit 1
fi
print_status "Server connection verified"

# Build the project locally
echo -e "${BLUE}🔨 Building project locally...${NC}"
if ! yarn install; then
    print_error "Failed to install dependencies"
    exit 1
fi

if ! yarn build; then
    print_error "Failed to build project"
    exit 1
fi
print_status "Project built successfully"

# Create deployment package
echo -e "${BLUE}📦 Creating deployment package...${NC}"
DEPLOY_PACKAGE="spqr-twenty-$(date +%Y%m%d-%H%M%S).tar.gz"

# Create a temporary directory for deployment files
TEMP_DIR=$(mktemp -d)
cp -r . "$TEMP_DIR/spqr-twenty"

# Remove unnecessary files
cd "$TEMP_DIR/spqr-twenty"
rm -rf node_modules
rm -rf .git
rm -rf packages/*/node_modules
rm -rf packages/*/dist
rm -rf packages/twenty-front/build

# Create the package
cd "$TEMP_DIR"
tar -czf "$DEPLOY_PACKAGE" spqr-twenty/
mv "$DEPLOY_PACKAGE" "$LOCAL_DIR/"
cd "$LOCAL_DIR"
rm -rf "$TEMP_DIR"

print_status "Deployment package created: $DEPLOY_PACKAGE"

# Upload to server
echo -e "${BLUE}📤 Uploading to server...${NC}"
scp "$DEPLOY_PACKAGE" ${SERVER_USER}@${SERVER_HOST}:/tmp/
print_status "Package uploaded to server"

# Deploy on server
echo -e "${BLUE}🚀 Deploying on server...${NC}"
ssh ${SERVER_USER}@${SERVER_HOST} << EOF
set -e

echo "🔧 Setting up deployment environment..."

# Create application directory
sudo mkdir -p ${REMOTE_DIR}
sudo chown ${SERVER_USER}:${SERVER_USER} ${REMOTE_DIR}

# Backup existing installation if it exists
if [ -d "${REMOTE_DIR}/current" ]; then
    echo "📋 Backing up existing installation..."
    sudo mv ${REMOTE_DIR}/current ${REMOTE_DIR}/backup-\$(date +%Y%m%d-%H%M%S) || true
fi

# Extract new version
echo "📦 Extracting new version..."
cd ${REMOTE_DIR}
tar -xzf /tmp/${DEPLOY_PACKAGE}
mv spqr-twenty current
rm -f /tmp/${DEPLOY_PACKAGE}

# Install dependencies
echo "📚 Installing dependencies..."
cd ${REMOTE_DIR}/current
npm install --production

# Install PM2 if not already installed
if ! command -v pm2 &> /dev/null; then
    echo "📦 Installing PM2..."
    sudo npm install -g pm2
fi

# Create environment file
echo "⚙️ Creating environment configuration..."
cat > ${REMOTE_DIR}/current/.env << 'ENVEOF'
# Database Configuration
DATABASE_URL=postgresql://twenty:twenty@localhost:5432/twenty
REDIS_URL=redis://localhost:6379

# Server Configuration
PORT=3000
NODE_ENV=production
CORS_ORIGIN=*

# Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
ACCESS_TOKEN_SECRET=your-access-token-secret
LOGIN_TOKEN_SECRET=your-login-token-secret
REFRESH_TOKEN_SECRET=your-refresh-token-secret
FILE_TOKEN_SECRET=your-file-token-secret

# Storage
STORAGE_TYPE=local
STORAGE_LOCAL_PATH=/tmp/twenty-storage

# Email (optional)
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_SYSTEM_ADDRESS=<EMAIL>

# LLM Configuration for SPQR MCP
LLM_PROVIDER=openai
LLM_API_KEY=your-openai-api-key
LLM_MODEL=gpt-4

# SPQR MCP Configuration
MCP_ENABLED=true
MCP_AUTO_DISCOVERY=true
MCP_LOG_LEVEL=info

# External Services (optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
ENVEOF

# Create PM2 ecosystem file
echo "📋 Creating PM2 configuration..."
cat > ${REMOTE_DIR}/current/ecosystem.config.js << 'PMEOF'
module.exports = {
  apps: [
    {
      name: 'spqr-twenty-server',
      script: 'packages/twenty-server/dist/main.js',
      cwd: '${REMOTE_DIR}/current',
      instances: 1,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      error_file: '${REMOTE_DIR}/logs/server-error.log',
      out_file: '${REMOTE_DIR}/logs/server-out.log',
      log_file: '${REMOTE_DIR}/logs/server-combined.log',
      time: true,
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024'
    },
    {
      name: 'spqr-twenty-worker',
      script: 'packages/twenty-server/dist/queue-worker/main.js',
      cwd: '${REMOTE_DIR}/current',
      instances: 1,
      env: {
        NODE_ENV: 'production'
      },
      error_file: '${REMOTE_DIR}/logs/worker-error.log',
      out_file: '${REMOTE_DIR}/logs/worker-out.log',
      log_file: '${REMOTE_DIR}/logs/worker-combined.log',
      time: true,
      max_memory_restart: '512M'
    }
  ]
};
PMEOF

# Create log directory
mkdir -p ${REMOTE_DIR}/logs

# Build the server
echo "🔨 Building server..."
cd ${REMOTE_DIR}/current
npm run build

# Setup database (if needed)
echo "🗄️ Setting up database..."
# This would run database migrations
# npm run database:migrate

# Start/restart services
echo "🚀 Starting services..."
pm2 delete spqr-twenty-server spqr-twenty-worker 2>/dev/null || true
pm2 start ecosystem.config.js
pm2 save
pm2 startup

echo "✅ Deployment completed successfully!"
echo "🌐 Server should be available at: http://${SERVER_HOST}:3000"
echo "📊 Monitor with: pm2 monit"
echo "📋 View logs with: pm2 logs"
EOF

print_status "Deployment completed on server"

# Clean up local deployment package
rm -f "$DEPLOY_PACKAGE"

# Verify deployment
echo -e "${BLUE}🔍 Verifying deployment...${NC}"
sleep 5

if ssh ${SERVER_USER}@${SERVER_HOST} "curl -f http://localhost:3000/health 2>/dev/null"; then
    print_status "Health check passed - server is responding"
else
    print_warning "Health check failed - server may still be starting up"
    echo -e "${YELLOW}Check server logs with: ssh ${SERVER_USER}@${SERVER_HOST} 'pm2 logs'${NC}"
fi

echo -e "${GREEN}🎉 SPQR-Twenty deployment completed!${NC}"
echo -e "${BLUE}📋 Next steps:${NC}"
echo -e "  1. Configure your environment variables in ${REMOTE_DIR}/current/.env"
echo -e "  2. Set up your database connection"
echo -e "  3. Configure LLM API keys for natural language processing"
echo -e "  4. Access the application at: http://${SERVER_HOST}:3000"
echo -e "  5. Monitor the application: ssh ${SERVER_USER}@${SERVER_HOST} 'pm2 monit'"

echo -e "${BLUE}🔧 Useful commands:${NC}"
echo -e "  • View logs: ssh ${SERVER_USER}@${SERVER_HOST} 'pm2 logs'"
echo -e "  • Restart: ssh ${SERVER_USER}@${SERVER_HOST} 'pm2 restart all'"
echo -e "  • Stop: ssh ${SERVER_USER}@${SERVER_HOST} 'pm2 stop all'"
echo -e "  • Status: ssh ${SERVER_USER}@${SERVER_HOST} 'pm2 status'"
