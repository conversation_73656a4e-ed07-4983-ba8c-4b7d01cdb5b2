# SPQR-Twenty MCP Integration

This document describes the integration of SPQR's dynamic MCP (Model Context Protocol) system into Twenty CRM.

## 🎯 Overview

SPQR-Twenty combines the robust CRM foundation of Twenty with SPQR's revolutionary dynamic MCP architecture, creating a self-organizing ecosystem of tools, agents, and sub-agents that can:

- **Auto-discover** new APIs and services
- **Dynamically compose** tools into new capabilities
- **Spawn agents** on-demand based on task requirements
- **Process natural language** commands and route them to appropriate capabilities
- **Self-heal** when components fail
- **Synchronize state** across all components in real-time

## 🏗️ Architecture

### Core Components

1. **MCPCoordinatorService** - Central orchestrator for the entire ecosystem
2. **DynamicToolRegistryService** - Auto-discovers and composes tools
3. **AgentFactoryService** - Creates agents and sub-agents dynamically
4. **ContextMeshService** - Real-time state synchronization
5. **TwentyIntegrationService** - Seamless integration with Twenty CRM
6. **NaturalLanguageService** - Interprets and executes natural language commands

### Database Schema

The integration adds the following tables to Twenty's database:

- `mcp_capabilities` - Available system capabilities
- `mcp_agents` - Agent instances and configurations
- `mcp_workflows` - Workflow definitions and execution stats
- `mcp_executions` - Execution history and metrics
- `mcp_contexts` - Session contexts and shared state

## 🚀 Features

### 1. Natural Language Control

Process commands like:
- "Process my emails and create tasks for action items"
- "Schedule a meeting with John next Tuesday"
- "Show me the status of all active agents"
- "Create a workflow to automatically follow up on leads"

### 2. Dynamic Capability Expansion

The system automatically:
- Discovers new APIs from environment variables
- Creates tools for new services
- Composes existing tools into new capabilities
- Spawns agents when needed

### 3. Self-Organizing Agents

Agents can:
- Spawn sub-agents for specialized tasks
- Request new capabilities
- Collaborate through the context mesh
- Self-terminate when tasks are complete

### 4. Twenty CRM Integration

Seamlessly integrates with:
- Twenty's object system (Person, Company, Opportunity, etc.)
- Twenty's GraphQL and REST APIs
- Twenty's authentication and workspace system
- Twenty's existing workflow engine

## 🛠️ Installation & Deployment

### Prerequisites

- Node.js 18+
- PostgreSQL 13+
- Redis (optional, for caching)
- LLM API key (OpenAI, Anthropic, etc.)

### Local Development

1. **Clone and setup:**
   ```bash
   git clone <repository-url> twenty-spqr
   cd twenty-spqr
   yarn install
   ```

2. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Run database migrations:**
   ```bash
   yarn database:migrate
   ```

4. **Start development server:**
   ```bash
   yarn dev
   ```

### Production Deployment

Use the provided deployment script:

```bash
chmod +x deploy-to-server.sh
./deploy-to-server.sh
```

This will:
- Build the project locally
- Create a deployment package
- Upload to your server (ssh vince@**************)
- Set up PM2 process management
- Configure environment variables
- Start the services

## 🔧 Configuration

### Environment Variables

```bash
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/twenty
REDIS_URL=redis://localhost:6379

# LLM Configuration
LLM_PROVIDER=openai
LLM_API_KEY=sk-your-openai-key
LLM_MODEL=gpt-4

# SPQR MCP Configuration
MCP_ENABLED=true
MCP_AUTO_DISCOVERY=true
MCP_LOG_LEVEL=info

# External Services (auto-discovered)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
SLACK_API_TOKEN=xoxb-your-slack-token
```

## 📡 API Usage

### GraphQL Endpoints

#### Execute Capability
```graphql
mutation ExecuteCapability($input: ExecuteCapabilityInput!) {
  executeCapability(input: $input) {
    success
    result
    message
  }
}
```

#### Process Natural Language
```graphql
mutation ProcessNaturalLanguage($input: NaturalLanguageInput!) {
  processNaturalLanguage(input: $input) {
    success
    command {
      intent
      confidence
      suggestedActions {
        type
        description
        confidence
      }
    }
    response {
      text
      suggestions
    }
  }
}
```

#### Get System Status
```graphql
query MCPStatus {
  mcpStatus {
    totalCapabilities
    activeContexts
    totalActiveAgents
    capabilities {
      id
      name
      type
      lifecycle
    }
  }
}
```

### REST Endpoints

#### Health Check
```bash
GET /health
```

#### MCP Health
```bash
GET /health/mcp
```

## 🎮 Usage Examples

### 1. Natural Language Email Processing

```typescript
const result = await client.mutate({
  mutation: PROCESS_NATURAL_LANGUAGE,
  variables: {
    input: {
      sessionId: 'user-session-123',
      text: 'Process my unread emails and create tasks for any action items',
      userId: 'user-123',
      workspaceId: 'workspace-456'
    }
  }
});
```

### 2. Dynamic Agent Spawning

```typescript
const result = await client.mutate({
  mutation: EXECUTE_CAPABILITY,
  variables: {
    input: {
      sessionId: 'user-session-123',
      capabilityId: 'email-agent',
      parameters: {
        action: 'process_inbox',
        filters: { unread: true, priority: 'high' }
      }
    }
  }
});
```

### 3. Workflow Creation

```typescript
const workflow = await client.mutate({
  mutation: CREATE_WORKFLOW,
  variables: {
    input: {
      name: 'Lead Follow-up Automation',
      description: 'Automatically follow up on new leads',
      definition: {
        triggers: ['lead.created'],
        steps: [
          { type: 'wait', duration: '24h' },
          { type: 'send_email', template: 'follow-up' },
          { type: 'create_task', title: 'Call lead' }
        ]
      },
      triggers: ['lead.created'],
      requiredCapabilities: ['email-agent', 'task-agent']
    }
  }
});
```

## 🔍 Monitoring & Debugging

### System Status

Check system health:
```bash
curl http://localhost:3000/health/mcp
```

### Logs

View application logs:
```bash
pm2 logs spqr-twenty-server
```

### Metrics

Query system metrics via GraphQL:
```graphql
query MCPMetrics($workspaceId: String, $timeRange: String) {
  mcpMetrics(workspaceId: $workspaceId, timeRange: $timeRange) {
    totalCapabilities
    activeContexts
    totalActiveAgents
    executionsLast24h
    averageExecutionTime
    successRate
    mostUsedCapabilities
  }
}
```

## 🚧 Development Status

### ✅ Completed
- Core MCP architecture integration
- Database schema and entities
- GraphQL API endpoints
- Natural language processing service
- Deployment scripts
- Health monitoring

### 🔄 In Progress
- Agent factory implementation
- Tool registry with auto-discovery
- Context mesh synchronization
- Twenty service integration

### 📋 Planned
- Frontend workflow builder
- Voice interface integration
- Advanced analytics dashboard
- Multi-workspace support
- Plugin system for custom capabilities

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the logs for error details

---

**Built with ❤️ by the SPQR Team**
