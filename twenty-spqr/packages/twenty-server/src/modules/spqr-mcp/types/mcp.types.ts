import { ObjectType, Field, InputType, Int, Float } from '@nestjs/graphql';
import { GraphQLJSON } from 'graphql-type-json';

@ObjectType()
export class MCPCapabilityType {
  @Field()
  id: string;

  @Field()
  name: string;

  @Field()
  type: string;

  @Field(() => GraphQLJSON)
  schema: any;

  @Field(() => [String])
  dependencies: string[];

  @Field()
  autoSpawn: boolean;

  @Field()
  lifecycle: string;

  @Field({ nullable: true })
  workspaceId?: string;
}

@ObjectType()
export class MCPStatusType {
  @Field(() => Int)
  totalCapabilities: number;

  @Field(() => Int)
  activeContexts: number;

  @Field(() => Int)
  totalActiveAgents: number;

  @Field(() => [MCPCapabilityType])
  capabilities: MCPCapabilityType[];
}

@InputType()
export class ExecuteCapabilityInput {
  @Field()
  sessionId: string;

  @Field()
  capabilityId: string;

  @Field(() => GraphQLJSON)
  parameters: any;

  @Field({ nullable: true })
  userId?: string;

  @Field({ nullable: true })
  workspaceId?: string;
}

@ObjectType()
export class ExecuteCapabilityResponse {
  @Field()
  success: boolean;

  @Field(() => GraphQLJSON, { nullable: true })
  result?: any;

  @Field({ nullable: true })
  error?: string;

  @Field()
  message: string;
}

@InputType()
export class NaturalLanguageInput {
  @Field()
  sessionId: string;

  @Field()
  text: string;

  @Field({ nullable: true })
  userId?: string;

  @Field({ nullable: true })
  workspaceId?: string;

  @Field(() => [GraphQLJSON], { nullable: true })
  conversationHistory?: any[];
}

@ObjectType()
export class NLCommandType {
  @Field()
  intent: string;

  @Field(() => GraphQLJSON)
  entities: any;

  @Field(() => Float)
  confidence: number;

  @Field()
  originalText: string;

  @Field(() => [NLActionType])
  suggestedActions: NLActionType[];
}

@ObjectType()
export class NLActionType {
  @Field()
  type: string;

  @Field({ nullable: true })
  capabilityId?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  parameters?: any;

  @Field()
  description: string;

  @Field(() => Float)
  confidence: number;
}

@ObjectType()
export class NLResponseType {
  @Field()
  text: string;

  @Field(() => [NLActionType], { nullable: true })
  actions?: NLActionType[];

  @Field(() => GraphQLJSON, { nullable: true })
  data?: any;

  @Field(() => [String], { nullable: true })
  suggestions?: string[];
}

@ObjectType()
export class NaturalLanguageResponse {
  @Field()
  success: boolean;

  @Field(() => NLCommandType, { nullable: true })
  command?: NLCommandType;

  @Field(() => NLResponseType, { nullable: true })
  response?: NLResponseType;

  @Field({ nullable: true })
  error?: string;

  @Field()
  message: string;
}

@ObjectType()
export class MCPAgentType {
  @Field()
  id: string;

  @Field()
  name: string;

  @Field()
  type: string;

  @Field(() => [String])
  capabilities: string[];

  @Field()
  autonomyLevel: string;

  @Field()
  lifecycle: string;

  @Field()
  status: string;

  @Field(() => GraphQLJSON, { nullable: true })
  configuration?: any;

  @Field({ nullable: true })
  workspaceId?: string;

  @Field({ nullable: true })
  parentAgentId?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  currentTask?: any;

  @Field(() => GraphQLJSON, { nullable: true })
  metrics?: any;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;

  @Field({ nullable: true })
  lastActivityAt?: Date;
}

@ObjectType()
export class MCPWorkflowType {
  @Field()
  id: string;

  @Field()
  name: string;

  @Field({ nullable: true })
  description?: string;

  @Field(() => GraphQLJSON)
  definition: any;

  @Field()
  status: string;

  @Field()
  isActive: boolean;

  @Field(() => [String])
  triggers: string[];

  @Field(() => [String])
  requiredCapabilities: string[];

  @Field({ nullable: true })
  workspaceId?: string;

  @Field({ nullable: true })
  createdBy?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  metadata?: any;

  @Field(() => GraphQLJSON, { nullable: true })
  executionStats?: any;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;

  @Field({ nullable: true })
  lastExecutedAt?: Date;
}

@ObjectType()
export class MCPExecutionType {
  @Field()
  id: string;

  @Field()
  sessionId: string;

  @Field()
  capabilityId: string;

  @Field(() => GraphQLJSON)
  input: any;

  @Field(() => GraphQLJSON, { nullable: true })
  output?: any;

  @Field()
  status: string;

  @Field({ nullable: true })
  error?: string;

  @Field({ nullable: true })
  workspaceId?: string;

  @Field({ nullable: true })
  userId?: string;

  @Field({ nullable: true })
  agentId?: string;

  @Field({ nullable: true })
  workflowId?: string;

  @Field({ nullable: true })
  parentExecutionId?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  metadata?: any;

  @Field()
  startedAt: Date;

  @Field()
  updatedAt: Date;

  @Field({ nullable: true })
  completedAt?: Date;

  @Field(() => Int, { nullable: true })
  durationMs?: number;
}

@InputType()
export class CreateWorkflowInput {
  @Field()
  name: string;

  @Field({ nullable: true })
  description?: string;

  @Field(() => GraphQLJSON)
  definition: any;

  @Field(() => [String])
  triggers: string[];

  @Field(() => [String])
  requiredCapabilities: string[];

  @Field({ nullable: true })
  workspaceId?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  metadata?: any;
}

@InputType()
export class UpdateWorkflowInput {
  @Field()
  id: string;

  @Field({ nullable: true })
  name?: string;

  @Field({ nullable: true })
  description?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  definition?: any;

  @Field(() => [String], { nullable: true })
  triggers?: string[];

  @Field(() => [String], { nullable: true })
  requiredCapabilities?: string[];

  @Field({ nullable: true })
  status?: string;

  @Field({ nullable: true })
  isActive?: boolean;

  @Field(() => GraphQLJSON, { nullable: true })
  metadata?: any;
}

@InputType()
export class CreateAgentInput {
  @Field()
  name: string;

  @Field({ nullable: true })
  type?: string;

  @Field(() => [String])
  capabilities: string[];

  @Field({ nullable: true })
  autonomyLevel?: string;

  @Field({ nullable: true })
  lifecycle?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  configuration?: any;

  @Field(() => GraphQLJSON, { nullable: true })
  spawnConditions?: any;

  @Field(() => GraphQLJSON, { nullable: true })
  terminationConditions?: any;

  @Field({ nullable: true })
  workspaceId?: string;

  @Field({ nullable: true })
  parentAgentId?: string;
}
