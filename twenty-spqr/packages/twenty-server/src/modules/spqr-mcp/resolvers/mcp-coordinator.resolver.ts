import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { GraphQLJSON } from 'graphql-type-json';

import { MCPCoordinatorService } from '../services/mcp-coordinator.service';
import { NaturalLanguageService } from '../services/natural-language.service';

// GraphQL Types (would typically be in separate files)
import { 
  MCPStatusType, 
  MCPCapabilityType, 
  ExecuteCapabilityInput,
  ExecuteCapabilityResponse,
  NaturalLanguageInput,
  NaturalLanguageResponse 
} from '../types/mcp.types';

@Resolver()
export class MCPCoordinatorResolver {
  constructor(
    private readonly mcpCoordinator: MCPCoordinatorService,
    private readonly naturalLanguage: NaturalLanguageService,
  ) {}

  @Query(() => MCPStatusType)
  async mcpStatus(): Promise<MCPStatusType> {
    return this.mcpCoordinator.getStatus();
  }

  @Query(() => [MCPCapabilityType])
  async mcpCapabilities(
    @Args('workspaceId', { nullable: true }) workspaceId?: string
  ): Promise<MCPCapabilityType[]> {
    const status = this.mcpCoordinator.getStatus();
    
    let capabilities = status.capabilities;
    
    if (workspaceId) {
      capabilities = capabilities.filter(cap => 
        !cap.workspaceId || cap.workspaceId === workspaceId
      );
    }
    
    return capabilities;
  }

  @Mutation(() => ExecuteCapabilityResponse)
  async executeCapability(
    @Args('input') input: ExecuteCapabilityInput,
    @Context() context: any
  ): Promise<ExecuteCapabilityResponse> {
    try {
      const { sessionId, capabilityId, parameters, userId, workspaceId } = input;
      
      const result = await this.mcpCoordinator.executeCapability(
        sessionId,
        capabilityId,
        parameters,
        userId,
        workspaceId
      );

      return {
        success: true,
        result,
        message: `Successfully executed capability: ${capabilityId}`
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: `Failed to execute capability: ${error.message}`
      };
    }
  }

  @Mutation(() => NaturalLanguageResponse)
  async processNaturalLanguage(
    @Args('input') input: NaturalLanguageInput,
    @Context() context: any
  ): Promise<NaturalLanguageResponse> {
    try {
      const { sessionId, text, userId, workspaceId, conversationHistory } = input;
      
      // Get MCP context
      const mcpContext = await this.mcpCoordinator.getContext(
        sessionId,
        userId || '',
        workspaceId || ''
      );
      
      // Process natural language
      const command = await this.naturalLanguage.processNaturalLanguage(
        text,
        mcpContext,
        conversationHistory
      );
      
      // Execute the command if confidence is high enough
      let response;
      if (command.confidence > 0.6 && command.suggestedActions.length > 0) {
        response = await this.naturalLanguage.executeNaturalLanguageCommand(
          command,
          mcpContext
        );
      } else {
        response = {
          text: `I understand you want to: ${command.intent}, but I need more information. Could you be more specific?`,
          suggestions: [
            'Process my emails',
            'Schedule a meeting',
            'Show system status',
            'Create a new contact'
          ]
        };
      }

      return {
        success: true,
        command,
        response,
        message: 'Natural language processed successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: `Failed to process natural language: ${error.message}`,
        response: {
          text: 'Sorry, I encountered an error processing your request.',
          suggestions: ['Try rephrasing your request', 'Get help']
        }
      };
    }
  }

  @Mutation(() => Boolean)
  async registerCapability(
    @Args('capability', { type: () => GraphQLJSON }) capability: any
  ): Promise<boolean> {
    try {
      await this.mcpCoordinator.registerCapability(capability);
      return true;
    } catch (error) {
      throw new Error(`Failed to register capability: ${error.message}`);
    }
  }

  @Query(() => GraphQLJSON)
  async mcpContext(
    @Args('sessionId') sessionId: string,
    @Args('userId', { nullable: true }) userId?: string,
    @Args('workspaceId', { nullable: true }) workspaceId?: string
  ): Promise<any> {
    const context = await this.mcpCoordinator.getContext(
      sessionId,
      userId || '',
      workspaceId || ''
    );

    return {
      sessionId: context.sessionId,
      userId: context.userId,
      workspaceId: context.workspaceId,
      capabilityCount: context.capabilities.size,
      activeAgentCount: context.activeAgents.size,
      sharedStateKeys: Array.from(context.sharedState.keys())
    };
  }

  @Mutation(() => Boolean)
  async clearContext(
    @Args('sessionId') sessionId: string
  ): Promise<boolean> {
    // Implementation would clear the context
    // For now, just return true
    return true;
  }

  @Query(() => [GraphQLJSON])
  async mcpExecutionHistory(
    @Args('sessionId', { nullable: true }) sessionId?: string,
    @Args('capabilityId', { nullable: true }) capabilityId?: string,
    @Args('workspaceId', { nullable: true }) workspaceId?: string,
    @Args('limit', { defaultValue: 50 }) limit?: number
  ): Promise<any[]> {
    // This would query the execution history from the database
    // For now, return empty array
    return [];
  }

  @Query(() => GraphQLJSON)
  async mcpMetrics(
    @Args('workspaceId', { nullable: true }) workspaceId?: string,
    @Args('timeRange', { defaultValue: '24h' }) timeRange?: string
  ): Promise<any> {
    // This would return system metrics
    const status = this.mcpCoordinator.getStatus();
    
    return {
      totalCapabilities: status.totalCapabilities,
      activeContexts: status.activeContexts,
      totalActiveAgents: status.totalActiveAgents,
      timeRange,
      // Additional metrics would be calculated here
      executionsLast24h: 0,
      averageExecutionTime: 0,
      successRate: 100,
      mostUsedCapabilities: []
    };
  }

  @Mutation(() => Boolean)
  async restartMCP(): Promise<boolean> {
    try {
      // This would restart the MCP system
      // For now, just return true
      return true;
    } catch (error) {
      throw new Error(`Failed to restart MCP: ${error.message}`);
    }
  }
}
