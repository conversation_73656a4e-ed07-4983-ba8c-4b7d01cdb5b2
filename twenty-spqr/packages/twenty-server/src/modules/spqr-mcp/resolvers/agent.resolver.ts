import { Resolver, Query, Mutation, Args } from '@nestjs/graphql';
import { MCPAgentType, CreateAgentInput } from '../types/mcp.types';

@Resolver(() => MCPAgentType)
export class AgentResolver {
  @Query(() => [MCPAgentType])
  async mcpAgents(
    @Args('workspaceId', { nullable: true }) workspaceId?: string
  ): Promise<MCPAgentType[]> {
    // Implementation will be added
    return [];
  }

  @Mutation(() => MCPAgentType)
  async createAgent(
    @Args('input') input: CreateAgentInput
  ): Promise<MCPAgentType> {
    // Implementation will be added
    throw new Error('Not implemented yet');
  }
}
