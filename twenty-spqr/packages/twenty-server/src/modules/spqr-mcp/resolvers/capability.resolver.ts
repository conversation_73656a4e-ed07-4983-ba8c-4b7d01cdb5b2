import { Resolver, Query, Args } from '@nestjs/graphql';
import { MCPCapabilityType } from '../types/mcp.types';

@Resolver(() => MCPCapabilityType)
export class CapabilityResolver {
  @Query(() => [MCPCapabilityType])
  async mcpCapabilitiesByType(
    @Args('type') type: string,
    @Args('workspaceId', { nullable: true }) workspaceId?: string
  ): Promise<MCPCapabilityType[]> {
    // Implementation will be added
    return [];
  }
}
