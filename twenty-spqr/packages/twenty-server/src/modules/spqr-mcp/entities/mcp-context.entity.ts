import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('mcp_contexts')
@Index(['workspaceId', 'sessionId'])
@Index(['workspaceId', 'userId'])
export class MCPContextEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  sessionId: string;

  @Column()
  userId: string;

  @Column()
  workspaceId: string;

  @Column('jsonb', { default: {} })
  state: any;

  @Column('text', { array: true, default: [] })
  capabilities: string[];

  @Column('text', { array: true, default: [] })
  activeAgents: string[];

  @Column('jsonb', { nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ nullable: true })
  lastActivityAt: Date;

  @Column({ default: true })
  isActive: boolean;
}
