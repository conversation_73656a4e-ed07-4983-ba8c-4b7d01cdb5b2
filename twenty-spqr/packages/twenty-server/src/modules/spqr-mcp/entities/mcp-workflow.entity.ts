import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('mcp_workflows')
@Index(['workspaceId', 'status'])
@Index(['workspaceId', 'isActive'])
export class MCPWorkflowEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column('jsonb')
  definition: any;

  @Column({
    type: 'enum',
    enum: ['draft', 'active', 'paused', 'archived'],
    default: 'draft',
  })
  status: string;

  @Column({ default: true })
  isActive: boolean;

  @Column('text', { array: true, default: [] })
  triggers: string[];

  @Column('text', { array: true, default: [] })
  requiredCapabilities: string[];

  @Column({ nullable: true })
  workspaceId: string;

  @Column({ nullable: true })
  createdBy: string;

  @Column('jsonb', { nullable: true })
  metadata: any;

  @Column('jsonb', { nullable: true })
  executionStats: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ nullable: true })
  lastExecutedAt: Date;
}
