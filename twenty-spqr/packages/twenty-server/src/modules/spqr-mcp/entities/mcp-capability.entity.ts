import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('mcp_capabilities')
@Index(['workspaceId', 'type'])
@Index(['workspaceId', 'lifecycle'])
export class MCPCapabilityEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  capabilityId: string;

  @Column()
  name: string;

  @Column({
    type: 'enum',
    enum: ['tool', 'agent', 'subagent', 'service'],
  })
  type: string;

  @Column('jsonb')
  schema: any;

  @Column('text', { array: true, default: [] })
  dependencies: string[];

  @Column({ default: false })
  autoSpawn: boolean;

  @Column({
    type: 'enum',
    enum: ['persistent', 'ephemeral', 'on-demand'],
    default: 'on-demand',
  })
  lifecycle: string;

  @Column({ nullable: true })
  workspaceId: string;

  @Column({ default: true })
  isActive: boolean;

  @Column('jsonb', { nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
