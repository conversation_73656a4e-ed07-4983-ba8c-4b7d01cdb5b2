import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('mcp_agents')
@Index(['workspaceId', 'status'])
@Index(['workspaceId', 'type'])
export class MCPAgentEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({
    type: 'enum',
    enum: ['primary', 'sub', 'specialized'],
    default: 'primary',
  })
  type: string;

  @Column('text', { array: true, default: [] })
  capabilities: string[];

  @Column({
    type: 'enum',
    enum: ['supervised', 'semi-autonomous', 'autonomous'],
    default: 'semi-autonomous',
  })
  autonomyLevel: string;

  @Column({
    type: 'enum',
    enum: ['persistent', 'ephemeral', 'on-demand'],
    default: 'on-demand',
  })
  lifecycle: string;

  @Column({
    type: 'enum',
    enum: ['idle', 'running', 'error', 'terminated'],
    default: 'idle',
  })
  status: string;

  @Column('jsonb', { nullable: true })
  configuration: any;

  @Column('jsonb', { nullable: true })
  spawnConditions: any;

  @Column('jsonb', { nullable: true })
  terminationConditions: any;

  @Column({ nullable: true })
  workspaceId: string;

  @Column({ nullable: true })
  parentAgentId: string;

  @Column('jsonb', { nullable: true })
  currentTask: any;

  @Column('jsonb', { nullable: true })
  metrics: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ nullable: true })
  lastActivityAt: Date;
}
