import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('mcp_executions')
@Index(['workspaceId', 'status'])
@Index(['sessionId', 'capabilityId'])
@Index(['workspaceId', 'startedAt'])
export class MCPExecutionEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  sessionId: string;

  @Column()
  capabilityId: string;

  @Column('jsonb')
  input: any;

  @Column('jsonb', { nullable: true })
  output: any;

  @Column({
    type: 'enum',
    enum: ['running', 'completed', 'failed', 'cancelled'],
    default: 'running',
  })
  status: string;

  @Column({ nullable: true })
  error: string;

  @Column({ nullable: true })
  workspaceId: string;

  @Column({ nullable: true })
  userId: string;

  @Column({ nullable: true })
  agentId: string;

  @Column({ nullable: true })
  workflowId: string;

  @Column({ nullable: true })
  parentExecutionId: string;

  @Column('jsonb', { nullable: true })
  metadata: any;

  @CreateDateColumn()
  startedAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ nullable: true })
  completedAt: Date;

  @Column({ type: 'int', nullable: true })
  durationMs: number;
}
