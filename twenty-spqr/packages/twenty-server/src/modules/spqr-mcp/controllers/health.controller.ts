import { Controller, Get } from '@nestjs/common';
import { MCPCoordinatorService } from '../services/mcp-coordinator.service';

@Controller('health')
export class HealthController {
  constructor(
    private readonly mcpCoordinator: MCPCoordinatorService,
  ) {}

  @Get()
  async getHealth() {
    const mcpStatus = this.mcpCoordinator.getStatus();
    
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'SPQR-Twenty MCP System',
      version: '1.0.0',
      mcp: {
        totalCapabilities: mcpStatus.totalCapabilities,
        activeContexts: mcpStatus.activeContexts,
        totalActiveAgents: mcpStatus.totalActiveAgents,
      },
      uptime: process.uptime(),
      memory: process.memoryUsage(),
    };
  }

  @Get('mcp')
  async getMCPHealth() {
    return this.mcpCoordinator.getStatus();
  }
}
