import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { MCPCapabilityEntity } from '../entities/mcp-capability.entity';

@Injectable()
export class MCPCapabilityRepository extends Repository<MCPCapabilityEntity> {
  constructor(
    @InjectRepository(MCPCapabilityEntity)
    private readonly repository: Repository<MCPCapabilityEntity>,
  ) {
    super(repository.target, repository.manager, repository.queryRunner);
  }

  async findByWorkspace(workspaceId: string): Promise<MCPCapabilityEntity[]> {
    return this.repository.find({
      where: { workspaceId, isActive: true },
      order: { createdAt: 'DESC' },
    });
  }

  async findByType(type: string, workspaceId?: string): Promise<MCPCapabilityEntity[]> {
    const where: any = { type, isActive: true };
    if (workspaceId) {
      where.workspaceId = workspaceId;
    }
    
    return this.repository.find({
      where,
      order: { createdAt: 'DESC' },
    });
  }
}
