import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { MCPWorkflowEntity } from '../entities/mcp-workflow.entity';

@Injectable()
export class MCPWorkflowRepository extends Repository<MCPWorkflowEntity> {
  constructor(
    @InjectRepository(MCPWorkflowEntity)
    private readonly repository: Repository<MCPWorkflowEntity>,
  ) {
    super(repository.target, repository.manager, repository.queryRunner);
  }

  async findByWorkspace(workspaceId: string): Promise<MCPWorkflowEntity[]> {
    return this.repository.find({
      where: { workspaceId, isActive: true },
      order: { createdAt: 'DESC' },
    });
  }

  async findActiveWorkflows(workspaceId?: string): Promise<MCPWorkflowEntity[]> {
    const where: any = { status: 'active', isActive: true };
    if (workspaceId) {
      where.workspaceId = workspaceId;
    }
    
    return this.repository.find({
      where,
      order: { lastExecutedAt: 'DESC' },
    });
  }
}
