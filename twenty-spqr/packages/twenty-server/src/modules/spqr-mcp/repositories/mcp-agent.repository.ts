import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { MCPAgentEntity } from '../entities/mcp-agent.entity';

@Injectable()
export class MCPAgentRepository extends Repository<MCPAgentEntity> {
  constructor(
    @InjectRepository(MCPAgentEntity)
    private readonly repository: Repository<MCPAgentEntity>,
  ) {
    super(repository.target, repository.manager, repository.queryRunner);
  }

  async findByWorkspace(workspaceId: string): Promise<MCPAgentEntity[]> {
    return this.repository.find({
      where: { workspaceId },
      order: { createdAt: 'DESC' },
    });
  }

  async findActiveAgents(workspaceId?: string): Promise<MCPAgentEntity[]> {
    const where: any = { status: 'running' };
    if (workspaceId) {
      where.workspaceId = workspaceId;
    }
    
    return this.repository.find({
      where,
      order: { lastActivityAt: 'DESC' },
    });
  }
}
