import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { MCPCoordinatorService, MCPContext } from './mcp-coordinator.service';

export interface NLCommand {
  intent: string;
  entities: { [key: string]: any };
  confidence: number;
  originalText: string;
  suggestedActions: NLAction[];
}

export interface NLAction {
  type: 'execute_capability' | 'create_workflow' | 'spawn_agent' | 'query_data';
  capabilityId?: string;
  parameters?: any;
  description: string;
  confidence: number;
}

export interface NLResponse {
  text: string;
  actions?: NLAction[];
  data?: any;
  suggestions?: string[];
}

@Injectable()
export class NaturalLanguageService {
  private readonly logger = new Logger(NaturalLanguageService.name);
  private llmProvider: string;
  private llmApiKey: string;
  private llmModel: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly mcpCoordinator: MCPCoordinatorService,
  ) {
    this.llmProvider = this.configService.get('LLM_PROVIDER', 'openai');
    this.llmApiKey = this.configService.get('LLM_API_KEY');
    this.llmModel = this.configService.get('LLM_MODEL', 'gpt-4');
  }

  /**
   * Process natural language input and return structured command
   */
  async processNaturalLanguage(
    input: string,
    context: MCPContext,
    conversationHistory?: any[]
  ): Promise<NLCommand> {
    this.logger.log(`🗣️ Processing natural language: "${input}"`);

    try {
      // Get available capabilities for context
      const availableCapabilities = Array.from(context.capabilities.values());
      
      // Create system prompt with context
      const systemPrompt = this.buildSystemPrompt(availableCapabilities);
      
      // Create user prompt with conversation history
      const userPrompt = this.buildUserPrompt(input, conversationHistory);
      
      // Call LLM to interpret the command
      const llmResponse = await this.callLLM(systemPrompt, userPrompt);
      
      // Parse LLM response into structured command
      const command = this.parseLLMResponse(llmResponse, input);
      
      this.logger.log(`🎯 Detected intent: ${command.intent} (confidence: ${command.confidence})`);
      
      return command;
    } catch (error) {
      this.logger.error('Error processing natural language:', error);
      
      // Return fallback command
      return {
        intent: 'unknown',
        entities: {},
        confidence: 0,
        originalText: input,
        suggestedActions: []
      };
    }
  }

  /**
   * Execute natural language command
   */
  async executeNaturalLanguageCommand(
    command: NLCommand,
    context: MCPContext
  ): Promise<NLResponse> {
    this.logger.log(`🚀 Executing NL command: ${command.intent}`);

    try {
      switch (command.intent) {
        case 'execute_capability':
          return await this.executeCapabilityCommand(command, context);
        
        case 'create_workflow':
          return await this.createWorkflowCommand(command, context);
        
        case 'spawn_agent':
          return await this.spawnAgentCommand(command, context);
        
        case 'query_data':
          return await this.queryDataCommand(command, context);
        
        case 'get_status':
          return await this.getStatusCommand(command, context);
        
        case 'help':
          return await this.getHelpCommand(command, context);
        
        default:
          return {
            text: `I'm not sure how to handle "${command.originalText}". Here are some things I can help with:`,
            suggestions: [
              'Process my emails',
              'Schedule a meeting',
              'Create a new contact',
              'Show system status',
              'Help me with workflows'
            ]
          };
      }
    } catch (error) {
      this.logger.error('Error executing NL command:', error);
      
      return {
        text: `Sorry, I encountered an error while processing your request: ${error.message}`,
        suggestions: ['Try rephrasing your request', 'Check system status', 'Get help']
      };
    }
  }

  /**
   * Generate conversational response
   */
  async generateResponse(
    result: any,
    originalCommand: NLCommand,
    context: MCPContext
  ): Promise<NLResponse> {
    try {
      // Create prompt for response generation
      const responsePrompt = this.buildResponsePrompt(result, originalCommand);
      
      // Call LLM to generate natural response
      const llmResponse = await this.callLLM(
        'You are a helpful AI assistant. Generate a natural, conversational response based on the execution result.',
        responsePrompt
      );
      
      return {
        text: llmResponse,
        data: result
      };
    } catch (error) {
      this.logger.error('Error generating response:', error);
      
      return {
        text: 'Task completed successfully.',
        data: result
      };
    }
  }

  /**
   * Build system prompt with available capabilities
   */
  private buildSystemPrompt(capabilities: any[]): string {
    const capabilityList = capabilities.map(cap => 
      `- ${cap.name} (${cap.type}): ${cap.schema?.description || 'No description'}`
    ).join('\n');

    return `You are an AI assistant that interprets natural language commands for a CRM system with dynamic capabilities.

Available capabilities:
${capabilityList}

Your task is to analyze user input and return a JSON response with:
{
  "intent": "execute_capability|create_workflow|spawn_agent|query_data|get_status|help|unknown",
  "entities": { extracted entities },
  "confidence": 0.0-1.0,
  "suggestedActions": [
    {
      "type": "execute_capability|create_workflow|spawn_agent|query_data",
      "capabilityId": "capability-id",
      "parameters": { parameters },
      "description": "human readable description",
      "confidence": 0.0-1.0
    }
  ]
}

Focus on understanding the user's intent and mapping it to available capabilities.`;
  }

  /**
   * Build user prompt with conversation history
   */
  private buildUserPrompt(input: string, history?: any[]): string {
    let prompt = `User input: "${input}"`;
    
    if (history && history.length > 0) {
      const historyText = history.slice(-3).map(h => 
        `User: ${h.input}\nAssistant: ${h.response}`
      ).join('\n');
      
      prompt = `Conversation history:\n${historyText}\n\nCurrent user input: "${input}"`;
    }
    
    return prompt;
  }

  /**
   * Build response generation prompt
   */
  private buildResponsePrompt(result: any, command: NLCommand): string {
    return `Original user request: "${command.originalText}"
Detected intent: ${command.intent}
Execution result: ${JSON.stringify(result, null, 2)}

Generate a natural, helpful response that:
1. Acknowledges what was done
2. Summarizes key results
3. Offers relevant next steps if appropriate
4. Uses a conversational tone`;
  }

  /**
   * Call LLM provider
   */
  private async callLLM(systemPrompt: string, userPrompt: string): Promise<string> {
    switch (this.llmProvider) {
      case 'openai':
        return await this.callOpenAI(systemPrompt, userPrompt);
      
      case 'anthropic':
        return await this.callAnthropic(systemPrompt, userPrompt);
      
      default:
        throw new Error(`Unsupported LLM provider: ${this.llmProvider}`);
    }
  }

  /**
   * Call OpenAI API
   */
  private async callOpenAI(systemPrompt: string, userPrompt: string): Promise<string> {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.llmApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.llmModel,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.3,
        max_tokens: 1000,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  /**
   * Call Anthropic API
   */
  private async callAnthropic(systemPrompt: string, userPrompt: string): Promise<string> {
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'x-api-key': this.llmApiKey,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01',
      },
      body: JSON.stringify({
        model: this.llmModel,
        max_tokens: 1000,
        system: systemPrompt,
        messages: [
          { role: 'user', content: userPrompt }
        ],
      }),
    });

    if (!response.ok) {
      throw new Error(`Anthropic API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.content[0].text;
  }

  /**
   * Parse LLM response into structured command
   */
  private parseLLMResponse(response: string, originalText: string): NLCommand {
    try {
      // Try to parse JSON response
      const parsed = JSON.parse(response);
      
      return {
        intent: parsed.intent || 'unknown',
        entities: parsed.entities || {},
        confidence: parsed.confidence || 0.5,
        originalText,
        suggestedActions: parsed.suggestedActions || []
      };
    } catch (error) {
      // Fallback parsing for non-JSON responses
      return this.fallbackParsing(response, originalText);
    }
  }

  /**
   * Fallback parsing for non-JSON responses
   */
  private fallbackParsing(response: string, originalText: string): NLCommand {
    const lowerText = originalText.toLowerCase();
    
    // Simple keyword-based intent detection
    if (lowerText.includes('email') || lowerText.includes('message')) {
      return {
        intent: 'execute_capability',
        entities: { type: 'email' },
        confidence: 0.7,
        originalText,
        suggestedActions: [{
          type: 'execute_capability',
          capabilityId: 'email-agent',
          parameters: { action: 'process_inbox' },
          description: 'Process emails',
          confidence: 0.7
        }]
      };
    }
    
    if (lowerText.includes('calendar') || lowerText.includes('meeting') || lowerText.includes('schedule')) {
      return {
        intent: 'execute_capability',
        entities: { type: 'calendar' },
        confidence: 0.7,
        originalText,
        suggestedActions: [{
          type: 'execute_capability',
          capabilityId: 'calendar-agent',
          parameters: { action: 'schedule_meeting' },
          description: 'Schedule meeting',
          confidence: 0.7
        }]
      };
    }
    
    if (lowerText.includes('status') || lowerText.includes('health') || lowerText.includes('system')) {
      return {
        intent: 'get_status',
        entities: {},
        confidence: 0.8,
        originalText,
        suggestedActions: []
      };
    }
    
    return {
      intent: 'unknown',
      entities: {},
      confidence: 0,
      originalText,
      suggestedActions: []
    };
  }

  /**
   * Command execution methods
   */
  private async executeCapabilityCommand(command: NLCommand, context: MCPContext): Promise<NLResponse> {
    const action = command.suggestedActions[0];
    if (!action || !action.capabilityId) {
      return { text: 'I need more information about which capability to execute.' };
    }

    const result = await this.mcpCoordinator.executeCapability(
      context.sessionId,
      action.capabilityId,
      action.parameters || {}
    );

    return await this.generateResponse(result, command, context);
  }

  private async createWorkflowCommand(command: NLCommand, context: MCPContext): Promise<NLResponse> {
    return {
      text: 'Workflow creation is not yet implemented. This feature will allow you to create custom automation workflows.',
      suggestions: ['Execute existing capabilities', 'Get system status', 'Spawn an agent']
    };
  }

  private async spawnAgentCommand(command: NLCommand, context: MCPContext): Promise<NLResponse> {
    return {
      text: 'Agent spawning from natural language is not yet implemented. Agents are automatically spawned based on capability requirements.',
      suggestions: ['Execute a capability that requires an agent', 'Get system status']
    };
  }

  private async queryDataCommand(command: NLCommand, context: MCPContext): Promise<NLResponse> {
    return {
      text: 'Data querying is not yet implemented. This feature will allow you to query CRM data using natural language.',
      suggestions: ['Execute capabilities', 'Get system status']
    };
  }

  private async getStatusCommand(command: NLCommand, context: MCPContext): Promise<NLResponse> {
    const status = this.mcpCoordinator.getStatus();
    
    return {
      text: `System Status:
- Total Capabilities: ${status.totalCapabilities}
- Active Contexts: ${status.activeContexts}
- Active Agents: ${status.totalActiveAgents}

The system is running normally and ready to process your requests.`,
      data: status
    };
  }

  private async getHelpCommand(command: NLCommand, context: MCPContext): Promise<NLResponse> {
    const capabilities = Array.from(context.capabilities.values());
    const capabilityList = capabilities.slice(0, 5).map(cap => `• ${cap.name}`).join('\n');
    
    return {
      text: `I can help you with various tasks using the available capabilities:

${capabilityList}
${capabilities.length > 5 ? `... and ${capabilities.length - 5} more` : ''}

Try saying things like:
• "Process my emails"
• "Schedule a meeting"
• "Show system status"
• "Create a new contact"`,
      suggestions: [
        'Process my emails',
        'Schedule a meeting',
        'Show system status',
        'Create a new contact'
      ]
    };
  }
}
