import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { DynamicToolRegistryService } from './dynamic-tool-registry.service';
import { AgentFactoryService } from './agent-factory.service';
import { ContextMeshService } from './context-mesh.service';
import { TwentyIntegrationService } from './twenty-integration.service';

import { MCPCapabilityEntity } from '../entities/mcp-capability.entity';
import { MCPContextEntity } from '../entities/mcp-context.entity';
import { MCPExecutionEntity } from '../entities/mcp-execution.entity';

export interface MCPCapability {
  id: string;
  name: string;
  type: 'tool' | 'agent' | 'subagent' | 'service';
  schema: any;
  dependencies: string[];
  autoSpawn: boolean;
  lifecycle: 'persistent' | 'ephemeral' | 'on-demand';
  workspaceId?: string;
}

export interface MCPContext {
  sessionId: string;
  userId: string;
  workspaceId: string;
  capabilities: Map<string, MCPCapability>;
  activeAgents: Map<string, any>;
  sharedState: Map<string, any>;
  eventStream: EventEmitter2;
}

@Injectable()
export class MCPCoordinatorService implements OnModuleInit {
  private readonly logger = new Logger(MCPCoordinatorService.name);
  private contexts: Map<string, MCPContext> = new Map();
  private globalCapabilities: Map<string, MCPCapability> = new Map();

  constructor(
    @InjectRepository(MCPCapabilityEntity)
    private readonly capabilityRepository: Repository<MCPCapabilityEntity>,
    @InjectRepository(MCPContextEntity)
    private readonly contextRepository: Repository<MCPContextEntity>,
    @InjectRepository(MCPExecutionEntity)
    private readonly executionRepository: Repository<MCPExecutionEntity>,
    private readonly toolRegistry: DynamicToolRegistryService,
    private readonly agentFactory: AgentFactoryService,
    private readonly contextMesh: ContextMeshService,
    private readonly twentyIntegration: TwentyIntegrationService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async onModuleInit() {
    this.logger.log('🚀 Initializing SPQR MCP Coordinator...');
    
    try {
      // Load existing capabilities from database
      await this.loadCapabilitiesFromDatabase();
      
      // Initialize subsystems
      await this.toolRegistry.initialize();
      await this.contextMesh.initialize();
      await this.twentyIntegration.discoverCapabilities();
      
      // Start capability expansion monitoring
      this.startCapabilityMonitoring();
      
      this.logger.log('✅ SPQR MCP Coordinator initialized successfully');
    } catch (error) {
      this.logger.error('❌ Failed to initialize MCP Coordinator:', error);
      throw error;
    }
  }

  /**
   * Get or create context for a session
   */
  async getContext(sessionId: string, userId: string, workspaceId: string): Promise<MCPContext> {
    if (!this.contexts.has(sessionId)) {
      // Try to load from database first
      let contextEntity = await this.contextRepository.findOne({
        where: { sessionId, workspaceId }
      });

      if (!contextEntity) {
        // Create new context
        contextEntity = this.contextRepository.create({
          sessionId,
          userId,
          workspaceId,
          state: {},
          capabilities: Array.from(this.globalCapabilities.keys()),
        });
        await this.contextRepository.save(contextEntity);
      }

      const context: MCPContext = {
        sessionId,
        userId,
        workspaceId,
        capabilities: new Map(this.globalCapabilities),
        activeAgents: new Map(),
        sharedState: new Map(Object.entries(contextEntity.state || {})),
        eventStream: this.eventEmitter
      };
      
      this.contexts.set(sessionId, context);
      
      // Auto-spawn persistent agents for this context
      await this.autoSpawnAgents(context);
    }
    
    return this.contexts.get(sessionId)!;
  }

  /**
   * Register a new capability
   */
  async registerCapability(capability: MCPCapability): Promise<void> {
    this.logger.log(`📦 Registering capability: ${capability.name} (${capability.type})`);
    
    // Save to database
    const capabilityEntity = this.capabilityRepository.create({
      capabilityId: capability.id,
      name: capability.name,
      type: capability.type,
      schema: capability.schema,
      dependencies: capability.dependencies,
      autoSpawn: capability.autoSpawn,
      lifecycle: capability.lifecycle,
      workspaceId: capability.workspaceId,
    });
    
    await this.capabilityRepository.save(capabilityEntity);
    
    // Add to memory
    this.globalCapabilities.set(capability.id, capability);
    
    // Propagate to all active contexts
    for (const context of this.contexts.values()) {
      if (!capability.workspaceId || context.workspaceId === capability.workspaceId) {
        context.capabilities.set(capability.id, capability);
        
        // Auto-spawn if needed
        if (capability.autoSpawn && capability.type === 'agent') {
          await this.spawnAgent(context, capability);
        }
      }
    }
    
    this.eventEmitter.emit('capability.registered', capability);
  }

  /**
   * Execute a capability
   */
  async executeCapability(
    sessionId: string, 
    capabilityId: string, 
    input: any,
    userId?: string,
    workspaceId?: string
  ): Promise<any> {
    const context = await this.getContext(sessionId, userId || '', workspaceId || '');
    const capability = context.capabilities.get(capabilityId);
    
    if (!capability) {
      throw new Error(`Capability ${capabilityId} not found`);
    }

    this.logger.log(`⚡ Executing capability: ${capability.name}`);

    // Create execution record
    const execution = this.executionRepository.create({
      sessionId,
      capabilityId,
      input,
      status: 'running',
      startedAt: new Date(),
      workspaceId: context.workspaceId,
    });
    await this.executionRepository.save(execution);

    try {
      let result: any;

      switch (capability.type) {
        case 'tool':
          result = await this.toolRegistry.executeTool(capabilityId, input, context);
          break;
        
        case 'agent':
          result = await this.executeAgent(context, capabilityId, input);
          break;
        
        case 'subagent':
          result = await this.executeSubAgent(context, capabilityId, input);
          break;
        
        case 'service':
          result = await this.twentyIntegration.executeService(capabilityId, input, context);
          break;
        
        default:
          throw new Error(`Unknown capability type: ${capability.type}`);
      }

      // Update execution record
      execution.output = result;
      execution.status = 'completed';
      execution.completedAt = new Date();
      await this.executionRepository.save(execution);

      this.eventEmitter.emit('capability.executed', { capability, input, result });

      return result;
    } catch (error) {
      // Update execution record with error
      execution.status = 'failed';
      execution.error = error.message;
      execution.completedAt = new Date();
      await this.executionRepository.save(execution);

      this.eventEmitter.emit('capability.failed', { capability, input, error });
      throw error;
    }
  }

  /**
   * Get system status
   */
  getStatus(): any {
    return {
      totalCapabilities: this.globalCapabilities.size,
      activeContexts: this.contexts.size,
      totalActiveAgents: Array.from(this.contexts.values())
        .reduce((sum, ctx) => sum + ctx.activeAgents.size, 0),
      capabilities: Array.from(this.globalCapabilities.values())
    };
  }

  /**
   * Load capabilities from database
   */
  private async loadCapabilitiesFromDatabase(): Promise<void> {
    const capabilities = await this.capabilityRepository.find();
    
    for (const cap of capabilities) {
      const capability: MCPCapability = {
        id: cap.capabilityId,
        name: cap.name,
        type: cap.type as any,
        schema: cap.schema,
        dependencies: cap.dependencies,
        autoSpawn: cap.autoSpawn,
        lifecycle: cap.lifecycle as any,
        workspaceId: cap.workspaceId,
      };
      
      this.globalCapabilities.set(capability.id, capability);
    }
    
    this.logger.log(`📚 Loaded ${capabilities.length} capabilities from database`);
  }

  /**
   * Auto-spawn agents based on context and capabilities
   */
  private async autoSpawnAgents(context: MCPContext): Promise<void> {
    for (const capability of context.capabilities.values()) {
      if (capability.autoSpawn && capability.lifecycle === 'persistent' && capability.type === 'agent') {
        await this.spawnAgent(context, capability);
      }
    }
  }

  /**
   * Spawn an agent
   */
  private async spawnAgent(context: MCPContext, capability: MCPCapability): Promise<any> {
    this.logger.log(`🤖 Spawning agent: ${capability.name}`);
    
    const agent = await this.agentFactory.createAgent(capability, context);
    context.activeAgents.set(capability.id, agent);
    
    // Register agent lifecycle events
    agent.on('task-complete', (result: any) => {
      this.handleAgentTaskComplete(context, capability.id, result);
    });
    
    agent.on('spawn-subagent', (subAgentSpec: any) => {
      this.spawnSubAgent(context, subAgentSpec);
    });
    
    agent.on('request-capability', (capabilityRequest: any) => {
      this.handleCapabilityRequest(context, capabilityRequest);
    });
    
    return agent;
  }

  /**
   * Execute agent capability
   */
  private async executeAgent(context: MCPContext, agentId: string, input: any): Promise<any> {
    const agent = context.activeAgents.get(agentId);
    
    if (!agent) {
      // Spawn on-demand agent
      const capability = context.capabilities.get(agentId);
      if (capability && capability.lifecycle === 'on-demand') {
        const newAgent = await this.spawnAgent(context, capability);
        return await newAgent.execute(input);
      }
      throw new Error(`Agent ${agentId} not found and cannot be spawned`);
    }
    
    return await agent.execute(input);
  }

  /**
   * Execute sub-agent capability
   */
  private async executeSubAgent(context: MCPContext, subAgentId: string, input: any): Promise<any> {
    const capability = context.capabilities.get(subAgentId);
    if (!capability) {
      throw new Error(`Sub-agent capability ${subAgentId} not found`);
    }
    
    const subAgent = await this.agentFactory.createSubAgent(capability, context);
    const result = await subAgent.execute(input);
    
    // Clean up ephemeral sub-agent
    if (capability.lifecycle === 'ephemeral') {
      subAgent.terminate?.();
    }
    
    return result;
  }

  /**
   * Handle agent task completion
   */
  private handleAgentTaskComplete(context: MCPContext, agentId: string, result: any): void {
    // Update context mesh with results
    this.contextMesh.updateSharedState(context, `agent-${agentId}-result`, result);
    
    this.eventEmitter.emit('agent.task.complete', { agentId, result });
  }

  /**
   * Spawn sub-agent
   */
  private async spawnSubAgent(context: MCPContext, spec: any): Promise<any> {
    this.logger.log(`🔧 Spawning sub-agent: ${spec.name}`);
    
    const subAgent = await this.agentFactory.createSubAgent(spec, context);
    
    // Sub-agents are typically ephemeral
    if (spec.lifecycle === 'ephemeral') {
      setTimeout(() => {
        subAgent.terminate?.();
      }, spec.ttl || 300000); // 5 minutes default
    }
    
    return subAgent;
  }

  /**
   * Handle capability requests from agents
   */
  private async handleCapabilityRequest(context: MCPContext, request: any): Promise<void> {
    this.logger.log(`📋 Capability request: ${request.type}`);
    
    // Auto-discover and register new capabilities
    if (request.type === 'discover') {
      await this.toolRegistry.discoverNewCapabilities(request.criteria);
    }
    
    // Compose existing capabilities
    if (request.type === 'compose') {
      await this.composeCapabilities(context, request.capabilities);
    }
  }

  /**
   * Compose multiple capabilities into a new one
   */
  private async composeCapabilities(context: MCPContext, capabilities: string[]): Promise<MCPCapability> {
    this.logger.log(`🔗 Composing capabilities: ${capabilities.join(', ')}`);
    
    const composedCapability = await this.toolRegistry.composeCapabilities(capabilities);
    await this.registerCapability(composedCapability);
    
    return composedCapability;
  }

  /**
   * Start monitoring for new capabilities
   */
  private startCapabilityMonitoring(): void {
    // Monitor for new APIs, services, and tools
    setInterval(async () => {
      try {
        await this.toolRegistry.scanForNewCapabilities();
        await this.twentyIntegration.scanForNewObjects();
      } catch (error) {
        this.logger.error('Error during capability monitoring:', error);
      }
    }, 30000); // Every 30 seconds
  }
}
