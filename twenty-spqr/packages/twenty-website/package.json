{"name": "twenty-website", "private": true, "scripts": {"nx": "NX_DEFAULT_PROJECT=twenty-website node ../../node_modules/nx/bin/nx.js", "dev": "npx next dev", "build": "npx next build", "start": "npx next start", "lint": "npx next lint", "github:sync": "npx tsx src/github/github-sync.ts", "github:init": "npx tsx src/github/github-sync.ts --isFullSync", "database:migrate": "npx tsx src/database/migrate-database.ts", "database:generate:pg": "npx drizzle-kit generate:pg --config=src/database/drizzle-posgres.config.ts"}, "dependencies": {"@docsearch/react": "^3.6.2", "@keystatic/core": "^0.5.45", "@keystatic/next": "^5.0.3", "@markdoc/markdoc": "^0.5.1", "@nivo/calendar": "^0.87.0", "gray-matter": "^4.0.3", "next-runtime-env": "^3.2.2", "postgres": "^3.4.3", "twenty-ui": "workspace:*"}}