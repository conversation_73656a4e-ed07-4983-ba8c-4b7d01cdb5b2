.DocSearch-Hit-source{
    color: #1c1e21;
}

.DocSearch-Search-Icon {
    color: #1c1e21;
}

.DocSearch-Logo {
    display: none;
}

.DocSearch-Footer{
    flex-direction: row;
    box-shadow: none;
    border: 1px solid #14141414;
}

.DocSearch-Form {
    box-shadow: none;
    border: 1px solid #141414;
    border-radius: 8px;
}

.DocSearch-Modal {
    background-color: white;
}

.DocSearch-Hits {
    width: 100%;
    margin-bottom: 2px !important;
}

.DocSearch-Hit[aria-selected=true] mark {
    color: #1961ED !important;
    text-decoration: none;
}

.DocSearch-Hit a {
    box-shadow: none;
}

.DocSearch-Hits mark {
    background-color: #E8EFFD;
    color: #1961ED;
}

.DocSearch-Hit-action {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    margin: 0 8px;
}

.DocSearch-Hit-action h2{
    font-size: .9em;
    margin: 0 0 4px;
    font-weight: 500;
}

.DocSearch-Hit-action p{
    font-size: 12px;
    margin: 0;
    width: 90%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-family: var(--font-inter);
    font-weight: 400;
}

.DocSearch-Button {
    margin: 0px;
    min-height: 36px;
    background-color: white;
    border-radius: 8px;
    border: 1px solid #14141414;
}

.DocSearch-Button:hover {
    color: #B3B3B3;
    box-shadow: none;
}

.DocSearch-Button-Placeholder{
    color: #B3B3B3;
}

.DocSearch-Search-Icon {
    height: 12px;
    width: 12px;
    color: #B3B3B3 !important;
}

.DocSearch-Hit-source {
    background: none;
    font-weight: 600;
    font-size: 12px;
    font-family: var(--font-inter);
}

.DocSearch-Button-Placeholder {
    font-weight: 500;
    font-family: var(--font-gabarito);
}

.DocSearch-Button-Keys {
    display: none
}

:root {
    --docsearch-primary-color: #1c1e21;
    --docsearch-highlight-color: #1414140F;
    --docsearch-hit-active-color: var(--docsearch-muted-color);
  }

.anchor {
    scroll-margin-top: calc(80px);
}
