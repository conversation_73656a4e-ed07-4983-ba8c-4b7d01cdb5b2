---
title: Feature Flags
icon: TbFlag
image: /images/user-guide/table-views/table.png
---

Feature flags are used to hide experimental features. For Twenty, they are set on workspace level and not on a user level.

## Adding a new feature flag

In `FeatureFlagKey.ts` add the feature flag:

```ts
type FeatureFlagKey =
  | 'IS_FEATURENAME_ENABLED'
  | ...;
```

Also add it to the enum in `feature-flag.entity.ts`:

```ts
enum FeatureFlagKeys {
    IsFeatureNameEnabled = 'IS_FEATURENAME_ENABLED',
    ...
}
```



To apply a feature flag on a **backend** feature use:

```ts
@Gate({
  featureFlag: 'IS_FEATURENAME_ENABLED',
})
```

To apply a feature flag on a **frontend** feature use:

```ts
const isFeatureNameEnabled = useIsFeatureEnabled('IS_FEATURENAME_ENABLED');
```


## Configure feature flags for the deployment

Change the corresponding record in the Table `core.featureFlag`:

| id       | key                      | workspaceId   | value  |
|----------|--------------------------|---------------|--------|
|  Random  | `IS_FEATURENAME_ENABLED` |  WorkspaceID  | `true` |

<ArticleEditContent></ArticleEditContent>