---
title: Tasks
info: Understand how to effectively manage tasks in Twenty, including tasks creation, viewing, editing, marking as complete, and deletion.
image: /images/user-guide/tasks/tasks_header.png
sectionInfo: Learn how to connect Twenty to your other tools.
---

Manage all tasks within your workspace using the **Tasks** feature. This guide will show you how to create and manage tasks, switch between upcoming and completed tasks, edit task details, and much more.

## Creating Tasks

Creating tasks in Twenty is seamless. You can either:

-  Go to the `Tasks`tab and press the `+` button at the top right of the page.
-  Use the search function by pressing `cmd/ctrl + k`, then select 'Create task' from the list of quick actions.
-  Go to a `Record page` and press `+`at the top right of the page, or go to the Task tab and press the `Add Task`button. 

<div style={{padding:'70.59% 0 0 0', position:'relative', margin: '32px 0px 0px'}}>
  <iframe 
    src="https://player.vimeo.com/video/928786754?autoplay=1&loop=1&autopause=0&background=1&amp;app_id=58479" 
    frameBorder="0" 
    allow="autoplay; fullscreen; picture-in-picture; clipboard-write" 
    style={{
      position:'absolute',
      top:0,
      left:0,
      width:'100%',
      height:'100%',
      borderRadius: '16px', 
      border:'2px solid black'
    }}
    title="Export data"
  ></iframe>
</div>
<script src="https://player.vimeo.com/api/player.js"></script>

### Adding Task Content

Once you've created a task you can enrich it with rich content, such as Titles, Bullet points or even images. To do so, press "/" and enter the desired command.

## Viewing Tasks

The **Tasks** page displays all your tasks across your workspace. Here you can:

- Filter tasks assigned to a specific user by clicking the button with your name at the top right of the screen.
- Toggle between upcoming (`To do`) and completed (`Done`) tasks to see what needs attention and what you have accomplished.

You can also see tasks for a given Record on its `Record page`.

<div style={{padding:'69.01% 0 0 0', position:'relative', margin: '32px 0px 0px'}}>
  <iframe 
    src="https://player.vimeo.com/video/927908280?autoplay=1&loop=1&autopause=0&background=1&amp;app_id=58479" 
    frameBorder="0" 
    allow="autoplay; fullscreen; picture-in-picture; clipboard-write" 
    style={{
      position:'absolute',
      top:0,
      left:0,
      width:'100%',
      height:'100%',
      borderRadius: '16px', 
      border:'2px solid black'
    }}
    title="Export data"
  ></iframe>
</div>
<script src="https://player.vimeo.com/api/player.js"></script>

## Editing Tasks

To edit a task, you should click on its card. This will open a side panel offering the following features:

-  **Assignee and Due date**: Update the assignee or edit the due date.
-  **Comments**: Work together with your team members by adding comments on tasks to give updates or feedback.
-  **Automations**: Thanks to the API and Webhooks, you can also automate task creation triggered by specific activities in your workspace.

## Marking Tasks as Complete

To mark a task as complete:

1. Locate the task on your `Tasks` page or within a `Record page`.
2. Click on the circle at the left of the task card - it will change to signify completion.
3. The task status will automatically update to `Done`.

This procedure will help keep an updated record of your accomplishments. 

<div style={{padding:'69.01% 0 0 0', position:'relative', margin: '32px 0px 0px'}}>
  <iframe 
    src="https://player.vimeo.com/video/927910083?autoplay=1&loop=1&autopause=0&background=1&amp;app_id=58479" 
    frameBorder="0" 
    allow="autoplay; fullscreen; picture-in-picture; clipboard-write" 
    style={{
      position:'absolute',
      top:0,
      left:0,
      width:'100%',
      height:'100%',
      borderRadius: '16px', 
      border:'2px solid black'
    }}
    title="Export data"
  ></iframe>
</div>
<script src="https://player.vimeo.com/api/player.js"></script>

## Delete a task

To permanently remove a task:

1. Open the task you want to delete by clicking on its card, either from the `Tasks` page or within a `Record page`.
2. Click the trash icon located in the top right corner of the task details panel.

Please note, deleting a task is permanent and can't be undone. Consider marking tasks as `Done` if there is a chance you will need to refer to them again.

<ArticleEditContent></ArticleEditContent>