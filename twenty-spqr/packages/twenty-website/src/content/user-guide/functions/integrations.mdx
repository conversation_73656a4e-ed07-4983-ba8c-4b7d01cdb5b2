---
title: Integrations
info: Learn how to connect Twenty to your other tools.
icon: IconBrandZapier
image: /images/user-guide/integrations/plug.png
sectionInfo: Learn how to connect Twenty to your other tools.
---

## About

Integration with Zapier and Windmill is available for automating your workflows.

## Zapier

Sync Twenty with 3000+ apps using <ArticleLink href="https://zapier.com/apps/twenty/integrations">Zapier</ArticleLink>, and automate your work. Here's how you can connect Twenty to Zapier:

1. Go to Zapier and log in. 
2. Click on `+ Create Zap` in the left sidebar. 
3. Choose the application you want to set as the trigger. A trigger refers to an event that starts the automation. 
4. Select Twenty as the action. An action is the event performed whenever an application triggers an automation. <ArticleLink href="https://zapier.com/how-it-works">Learn more about triggers and actions in Zapier.</ArticleLink>
5. Once you choose the Twenty account that you want to use for your automation, you'll have to allow Zapier to access it by adding an API key. You can learn [how to generate your API key here.](/user-guide/section/functions/api-webhooks)


6. Enter your API key and click on 'Yes, Continue to Twenty.'

<div style={{padding:'69.01% 0 0 0', position:'relative', margin: '32px 0px 0px'}}>
  <iframe 
    src="https://player.vimeo.com/video/*********?autoplay=1&loop=1&autopause=0&background=1&amp;app_id=58479" 
    frameBorder="0" 
    allow="autoplay; fullscreen; picture-in-picture; clipboard-write" 
    style={{
      position:'absolute',
      top:0,
      left:0,
      width:'100%',
      height:'100%',
      borderRadius: '16px', 
      border:'2px solid black'
    }}
    title="Export data"
  ></iframe>
</div>
<script src="https://player.vimeo.com/api/player.js"></script>

You can now continue creating your automation!

<ArticleEditContent></ArticleEditContent>
