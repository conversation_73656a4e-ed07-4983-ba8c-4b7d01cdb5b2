---
title: Notes
info: Explore how to efficiently manage notes within record pages in Twenty, including procedures for creating, formatting, commenting, saving, and deleting notes.
icon: IconNote
image: /images/user-guide/notes/notes_header.png
sectionInfo: Learn how to connect Twenty to your other tools.
---

Manage your record-linked notes efficiently using the powerful **Notes** feature. This guide walks through how to create, format, comment, and delete notes seamlessly within record pages. 

## Creating Notes

Creating notes in the system is intuitive and dynamic. You can either:

- Use the search function by pressing `cmd/ctrl + k`, then select 'Create note' from the list of quick actions.
- Go to a `Record page` and select `+` at the top right of the page, or go to the Notes tab and press the `New Note` button.

<img src="/images/user-guide/notes/add-note.png"style={{width:'100%'}}/>


### Adding Content

Start typing directly or press '/' to add elements like headings, files, or images instantly.

### Format Content

You can format your notes right from the editor. Use Markdown syntax, press the `/` key or click on the `+` icon on the editor to see the different block options, such as headings, tables, and lists. You can also attach images to your note.

Highlight the text to see more formatting options like bold, italics, and alignment options. 

You can also change the background color and text color of each block to highlight important things in your note. To do so, hover over the block you want to format and click on the `⋮⋮` icon besides the `+` icon. Click on <b>Colors</b> to open up all color options for both the text and the background.

<div style={{padding:'69.01% 0 0 0', position:'relative', margin: '32px 0px 0px'}}>
  <iframe 
    src="https://player.vimeo.com/video/*********?autoplay=1&loop=1&autopause=0&background=1&amp;app_id=58479" 
    frameBorder="0" 
    allow="autoplay; fullscreen; picture-in-picture; clipboard-write" 
    style={{
      position:'absolute',
      top:0,
      left:0,
      width:'100%',
      height:'100%',
      borderRadius: '16px', 
      border:'2px solid black'
    }}
    title="Export data"
  ></iframe>
</div>
<script src="https://player.vimeo.com/api/player.js"></script>

## Viewing Notes

The system displays all your notes linked to a specific record under the Notes section on the corresponding `Record page`.

## Commenting
**Comments**: Work together effectively with your team members by adding comments on notes for updates, feedback, or discussions.

## Saving And Deleting

All edits and additions to the note are automatically saved. 

To delete a note:

1. Open the note you wish to remove by clicking on it from within the `Record page`.
2. Click the trashcan icon located in the top right corner of the screen.

Please be aware that deleting a note is permanent and can't be undone.

<ArticleEditContent></ArticleEditContent>