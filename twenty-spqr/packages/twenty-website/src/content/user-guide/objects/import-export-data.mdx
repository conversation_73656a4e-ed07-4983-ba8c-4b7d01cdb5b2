---
title: Import/Export Data
info: Learn the procedures for importing and exporting data.
icon: IconNote
image: /images/user-guide/import-export-data/cloud.png
sectionInfo: Discover how to use standard and custom objects in your workspace.
---

## Import data

You can import People and Companies data into Twenty from other apps using a .csv, .xlsx, or .xls file. In the <b>Companies</b> or <b>People</b> page, click on <b>Options</b> and then on <b>Import</b>.

Upload your file, match the columns, check your data and import it.

<img src="/images/user-guide/import-export-data/match-columns.png" style={{width:'100%'}}/>

## Export data

To export data from an object:

1. Visit the object index.
2. Choose the view for data export.
3. Access the `Options` menu.
4. Click on `Export`.
5. Select the save location for the CSV data. Note that exporting may take time with a large record count.

<div style={{padding:'71.24% 0 0 0', position:'relative', margin: '32px 0px 0px'}}>
  <iframe 
    src="https://player.vimeo.com/video/926226303?autoplay=1&loop=1&autopause=0&background=1&amp;app_id=58479" 
    frameBorder="0" 
    allow="autoplay; fullscreen; picture-in-picture; clipboard-write" 
    style={{
      position:'absolute',
      top:0,
      left:0,
      width:'100%',
      height:'100%',
      borderRadius: '16px', 
      border:'2px solid black'
    }}
    title="Export data"
  ></iframe>
</div>
<script src="https://player.vimeo.com/api/player.js"></script>

<ArticleEditContent></ArticleEditContent>