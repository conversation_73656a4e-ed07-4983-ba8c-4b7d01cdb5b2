---
title: GitHub
info: Learn about the Twenty GitHub repository and the variety of resources it hosts including source code, documentation, and discussions.
icon: IconGitHub
image: /images/user-guide/github/github-header.png
sectionInfo: Discover tips and tricks to optimize your experience.
---

## About

The Twenty GitHub repository hosts a vast array of resources like source code, documentation, discussions, and issue tracking. This is where you will be able to access the full code behind Twenty.

[Visit Twenty on GitHub](https://github.com/twentyhq/twenty)

## Contributing

Contributing to the Twenty project on GitHub is a rewarding way to help improve the software you use. Whether you're fixing bugs, suggesting features, or improving documentation, your contributions are welcome.

### Reporting Issues

Encounter an issue? <ArticleLink href="https://github.com/twentyhq/twenty/issues/new">Create an issue</ArticleLink> on GitHub, providing as much detail as possible.

<img src="/images/user-guide/github/new-issue.png" style={{width:'100%'}}/>

### Suggesting Features

What improvements would you like to see on Twenty? No matter your technical know-how, you can join <ArticleLink href="https://github.com/twentyhq/twenty/discussions">the conversation here</ArticleLink>.

<img src="/images/user-guide/github/github-conversations.png" style={{width:'100%'}}/>

### Coding a feature

Start your journey by finding beginner-friendly tasks:

1. Navigate to the **[Issues](https://github.com/twentyhq/twenty/issues)** tab on the Twenty repository.
2. Filter by **[Good First Issue](https://github.com/twentyhq/twenty/labels/good%20first%20issue)** label to find tasks suited for newcomers.
3. Pick an issue, fork the repository, and start contributing.

<img src="/images/user-guide/github/good-first-issues.png" style={{width:'100%'}}/>

Ensure you're assigned to the issue to avoid overlapping work with other contributors.

### Code of Conduct

Remember to adhere to Twenty's <ArticleLink href="https://github.com/twentyhq/twenty/blob/main/.github/CODE_OF_CONDUCT.md">Code of Conduct</ArticleLink> throughout your contribution process.

## Discord

If you have any question, for example on how to contribute, join the community on [Discord](https://discord.gg/cx5n4Jzs57)

<img src="/images/user-guide/github/discord-welcome.png" style={{width:'100%'}}/>

<br/>
<br/>
Thank you for contributing to Twenty ❤️

<ArticleEditContent></ArticleEditContent>