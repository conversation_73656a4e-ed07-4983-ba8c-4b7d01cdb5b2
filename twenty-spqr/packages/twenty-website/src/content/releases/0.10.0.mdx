---
release: 0.10.0
Date: Apr 15th 2024
---

# More Field Types, More Power

Enhance your data handling capabilities with the addition of four new field types:

## Multi-Select Field

The `Multi-Select Field` allows for tagging a record with multiple attributes, providing a flexible way to classify and filter data.

**Example Use Case**: Tag a company record with multiple industries such as "Retail," "Technology," and "Finance," enabling more nuanced segmentation and analysis.

![](/images/releases/0.10/0.10-multi-select.png)

## Currency Field

Designed specifically for financial data, the `Currency Field` ensures correct calculation and standard formatting for monetary figures.

**Example Use Case**: Record and manage global transactions in their original currencies while maintaining precision in financial reporting.

![](/images/releases/0.10/0.10-currency.png)

## Datetime Field

With the `Datetime Field`, you can effectively track events, deadlines, and activities with enhanced accuracy, improving time management and scheduling.

**Example Use Case**: Precisely track and set reminders for project milestones and deadlines.

![](/images/releases/0.10/0.10-datetime.png)

## JSON Field

The `JSON Field` allows for the storage of complex, structured data within a single field, thus expanding the capabilities for data customisation and integration.

**Example Use Case**: Store configurable data for a product, such as feature flags or customization options, directly within a CRM record.

![](/images/releases/0.10/0.10-json.png)