---
title: Breadcrumb
icon: TbSquareChevronsRight
image: /images/user-guide/fields/field.png
---
Renders a breadcrumb navigation bar.

<ArticleTabs label1="Usage" label2="Props">
<ArticleTab>

<SandpackEditor content={`import { <PERSON>rowserRouter } from "react-router-dom";
import { Breadcrumb } from "@/ui/navigation/bread-crumb/components/Breadcrumb";

export const MyComponent = () => {
  const breadcrumbLinks = [
    { children: "Home", href: "/" },
    { children: "Category", href: "/category" },
    { children: "Subcategory", href: "/category/subcategory" },
    { children: "Current Page" },
  ];

  return (
    <BrowserRouter>
      <Breadcrumb className links={breadcrumbLinks} />
    </BrowserRouter>
    )
};`} />

</ArticleTab>
<ArticleTab>

<ArticlePropsTable options={[
  ['className', 'string', 'Optional class name for additional styling'],
  ['links', 'array', 'An array of objects, each representing a breadcrumb link. Each object has a `children` property (the text content of the link) and an optional `href` property (the URL to navigate to when the link is clicked)']
]} />

</ArticleTab>
</ArticleTabs>

<ArticleEditContent></ArticleEditContent>