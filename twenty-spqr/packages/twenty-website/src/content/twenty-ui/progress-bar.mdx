---
title: Feedback
icon: TbLoader2
image: /images/user-guide/emails/emails_header.png
---

Indicates progress or countdown and moves from right to left. 

<ArticleTabs label1="Usage" label2="Props">

<ArticleTab>

<SandpackEditor content={`import { ProgressBar } from "twenty-ui/feedback";

export const MyComponent = () => {
  return (
    <ProgressBar
      duration={6000}
      delay={0}
      easing="easeInOut"
      barHeight={10}
      barColor="#4bb543"
      autoStart={true}
    />
  );
};`} />

</ArticleTab>

<ArticleTab>

<ArticlePropsTable options={[
  ['duration', 'number', 'The total duration of the progress bar animation in milliseconds', '3'],
  ['delay', 'number', 'The delay in starting the progress bar animation in milliseconds', '0'],
  ['easing', 'string', 'Easing function for the progress bar animation', 'easeInOut'],
  ['barHeight', 'number', 'The height of the bar in pixels', '24'],
  ['barColor', 'string', 'The color of the bar', 'gray80'],
  ['autoStart', 'boolean', 'If `true`, the progress bar animation starts automatically when the component mounts', '`true`']
]} />

</ArticleTab>
</ArticleTabs>


## Circular Progress Bar

Indicates the progress of a task, often used in loading screens or areas where you want to communicate ongoing processes to the user. 

<ArticleTabs label1="Usage" label2="Props">

<ArticleTab>

<SandpackEditor content={`import { CircularProgressBar } from "@/ui/feedback/progress-bar/components/CircularProgressBar";

export const MyComponent = () => {
  return <CircularProgressBar size={80} barWidth={6} barColor="green" />;
};`} />

</ArticleTab>

<ArticleTab>

<ArticlePropsTable options={[
  ['size', 'number', 'The size of the circular progress bar', '50'],
  ['barWidth', 'number', 'The width of the progress bar line', '5'],
  ['barColor', 'string', 'The color of the progress bar', 'currentColor']
]} />

</ArticleTab>

</ArticleTabs>

<ArticleEditContent></ArticleEditContent>