{"id": "b3a89784-eb82-49d8-b081-31c49e6906dc", "prevId": "a7895a79-44a3-4fad-b750-f89d8c04d85c", "version": "5", "dialect": "pg", "tables": {"githubReleases": {"name": "githubReleases", "schema": "", "columns": {"tagName": {"name": "tagName", "type": "text", "primaryKey": true, "notNull": true}, "publishedAt": {"name": "publishedAt", "type": "date", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "githubStars": {"name": "githubStars", "schema": "", "columns": {"timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "numberOfStars": {"name": "numberOfStars", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "issueLabels": {"name": "issueLabels", "schema": "", "columns": {"issueId": {"name": "issueId", "type": "text", "primaryKey": false, "notNull": false}, "labelId": {"name": "labelId", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"issueLabels_issueId_issues_id_fk": {"name": "issueLabels_issueId_issues_id_fk", "tableFrom": "issueLabels", "tableTo": "issues", "columnsFrom": ["issueId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "issueLabels_labelId_labels_id_fk": {"name": "issueLabels_labelId_labels_id_fk", "tableFrom": "issueLabels", "tableTo": "labels", "columnsFrom": ["labelId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "issues": {"name": "issues", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "externalId": {"name": "externalId", "type": "text", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": false}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": false}, "closedAt": {"name": "closedAt", "type": "text", "primaryKey": false, "notNull": false}, "authorId": {"name": "authorId", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"issues_authorId_users_id_fk": {"name": "issues_authorId_users_id_fk", "tableFrom": "issues", "tableTo": "users", "columnsFrom": ["authorId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "labels": {"name": "labels", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "externalId": {"name": "externalId", "type": "text", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "pullRequestLabels": {"name": "pullRequestLabels", "schema": "", "columns": {"pullRequestExternalId": {"name": "pullRequestExternalId", "type": "text", "primaryKey": false, "notNull": false}, "labelId": {"name": "labelId", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"pullRequestLabels_pullRequestExternalId_pullRequests_id_fk": {"name": "pullRequestLabels_pullRequestExternalId_pullRequests_id_fk", "tableFrom": "pullRequestLabels", "tableTo": "pullRequests", "columnsFrom": ["pullRequestExternalId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "pullRequestLabels_labelId_labels_id_fk": {"name": "pullRequestLabels_labelId_labels_id_fk", "tableFrom": "pullRequestLabels", "tableTo": "labels", "columnsFrom": ["labelId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "pullRequests": {"name": "pullRequests", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": false}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": false}, "closedAt": {"name": "closedAt", "type": "text", "primaryKey": false, "notNull": false}, "mergedAt": {"name": "mergedAt", "type": "text", "primaryKey": false, "notNull": false}, "authorId": {"name": "authorId", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"pullRequests_authorId_users_id_fk": {"name": "pullRequests_authorId_users_id_fk", "tableFrom": "pullRequests", "tableTo": "users", "columnsFrom": ["authorId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "avatarUrl": {"name": "avatarUrl", "type": "text", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false}, "isEmployee": {"name": "isEmployee", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}