{"name": "twenty-zapier", "version": "2.0.1", "description": "Effortlessly sync Twenty with 3000+ apps. Automate tasks, boost productivity, and supercharge your customer relationships!", "main": "src/index.ts", "scripts": {"nx": "NX_DEFAULT_PROJECT=twenty-zapier node ../../node_modules/nx/bin/nx.js", "format": "prettier . --write \"!build\"", "test": "yarn build && jest --testTimeout 10000 --rootDir ./lib/test", "validate": "yarn build && zapier validate", "versions": "yarn build && zapier versions", "watch": "yarn clean && npx tsc --watch"}, "engines": {"node": "^18.17.1", "npm": "please-use-yarn", "yarn": "^4.0.2"}, "private": true, "zapier": {"convertedByCLIVersion": "15.4.1"}, "dependencies": {"dotenv": "^16.4.5", "zapier-platform-core": "15.5.1"}, "devDependencies": {"jest": "29.7.0", "rimraf": "^3.0.2", "zapier-platform-cli": "^15.4.1"}, "installConfig": {"hoistingLimits": "dependencies"}}