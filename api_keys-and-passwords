### API Keys and Passwords 


github acces token : ****************************************



Vapi AI API Key : 342ca77b-f45d-411c-a104-32f7bf4552ec


openAI API Key  (paid account) : ********************************************************************************************************************************************************************


claude AI API Key (paid account): ************************************************************************************************************


google AI studio API Key : AIzaSyDEZPNmGvZqnfgNFev7Zss4NSGbJzWNmbs 


open router api Key : 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


 
nvidia Nim API KEy : **********************************************************************


Mistral API Key:  5zzh6GldrXGuxWEO8u7XvVGv2STvfKzK

cohere API Key : d2cchd71cXgNdh2IcFkrNNrJuVYjpdBSzBCsna0X

cloudflare workers AI APi KEy : ****************************************

huggingface access token: *************************************




Google OAuth Client: web	
client_id	"************-08jvm0irh1geer7378rbu6vr247svmng.apps.googleusercontent.com"
project_id	"jarvis-386211"
auth_uri	"https://accounts.google.com/o/oauth2/auth"
token_uri	"https://oauth2.googleapis.com/token"
auth_provider_x509_cert_url	"https://www.googleapis.com/oauth2/v1/certs"
client_secret	"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"









Anbieter	Rate Limits	Modelle (Beispiele)
OpenRouter	20 Anfragen/Minute, 50 Anfragen/Tag, 1000 Anfragen/Tag mit $10 Kreditbalance	Bytedance UI Tars 72B, DeepHermes 3 Llama 3 8B Preview, DeepSeek R1, etc.

Google AI Studio	Variiert, z. B. Gemini 2.5 Pro: 5.000.000 Tokens/Tag, 1.000.000 Tokens/Minute, 25 Anfragen/Tag, 5 Anfragen/Minute	Gemini 2.5 Pro, Gemini 2.0 Flash, Gemma 3 27B Instruct, etc.

NVIDIA NIM	40 Anfragen/Minute	Verschiedene offene Modelle

Mistral (La Plateforme)	1 Anfrage/Sekunde, 500.000 Tokens/Minute, 1.000.000.000 Tokens/Monat	Offene und proprietäre Mistral-Modelle

Mistral (Codestral)	30 Anfragen/Minute, 2.000 Anfragen/Tag	Codestral

HuggingFace Serverless Inference	Variabel, aktuell $0.10 pro Monat, begrenzt auf Modelle <10GB	Verschiedene offene Modelle


Cerebras	Variiert, z. B. Llama 4 Scout: 30 Anfragen/Minute, 60.000 Tokens/Minute, etc.	Llama 4 Scout, Llama 3.1 8B, Llama 3.3 70B, etc.
Groq	Variiert, z. B. Allam 2 7B: 7.000 Anfragen/Tag, 6.000 Tokens/Minute	Allam 2 7B, DeepSeek R1 Distill Llama 70B, Gemma 2 9B Instruct, etc.
OVH AI Endpoints (Free Beta)	12 Anfragen/Minute	DeepSeek R1 Distill Llama 70B, Llama 3.1 70B Instruct, Qwen 2.5 VL 72B Instruct, etc.


Cohere	20 Anfragen/Minute, 1.000 Anfragen/Monat	Command-R, Command-R+, Command-R7B, Aya Expanse 8B, etc.
GitHub Models	Abhängig vom Copilot-Abonnement-Tarif, sehr restriktive Token-Limits	AI21 Jamba 1.5 Large, Cohere Command R, Llama-3.2-11B-Vision-Instruct, etc.
Chutes	Verteilte, dezentrale Nutzung, keine spezifische Grenze angegeben	DeepHermes 3 Llama 3 8B Preview, DeepSeek R1, Gemma 3 12B Instruct, etc.


Cloudflare Workers AI	10.000 Neuronen/Tag	DeepSeek R1 Distill Qwen 32B, Gemma 2B Instruct, Llama 3.1 8B Instruct, etc.

Google Cloud Vertex AI	Variiert, z. B. Llama 4 Maverick Instruct: 60 Anfragen/Minute (kostenlos während Vorschau)	Llama 4 Maverick Instruct, Llama 4 Scout Instruct, Gemini 2.5 Pro Experimental, etc.
