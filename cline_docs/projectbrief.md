# Project Brief: Personal Assistant System ("MVP ++")

## Core Requirements and Goals

The primary goal is to build a functional Personal Assistant System based on Twenty CRM, extended with a powerful AI Playground and various AI-driven agents and features. The project follows the "Apple Way" philosophy, emphasizing simplicity, elegance, functionality, and intuitive UX.

The "MVP ++" scope includes:

*   **Twenty CRM Setup:** Install and configure Twenty CRM with a Supabase backend.
*   **AI Playground:** Implement a visual workflow builder similar to n8n.
*   **Essential AI Agents:** Develop agents for Email, Calendar, and Contact Intelligence.
*   **Advanced AI Agents:** Develop additional agents for expanded capabilities.
*   **Unified Credentials:** Implement a secure, centralized system for managing API keys and credentials (using Supabase Vault).
*   **Web Interface:** The primary user interface will be a web application based on Twenty's React frontend.
*   **Telegram Integration:** Add Telegram as an additional interaction channel.
*   **Advanced RAG:** Integrate Supabase Vector for advanced Retrieval Augmented Generation.
*   **Multi-User Support:** Implement basic support for multiple users.
*   **Voice Integration (VAPI):** Integrate VAPI for voice-based interaction.
*   **n8n Integration:** Allow workflows to interact with external n8n instances via webhooks.

## Project Scope

The scope is defined by the features listed above. Features explicitly *not* included in the original MVP (Mobile App, Complex RAG beyond Supabase Vector, Advanced Workflows beyond the visual builder and n8n integration) are now included in the "MVP ++" scope, except for the Mobile App which is deferred.

## Source of Truth

This document serves as the primary source of truth for the project's core requirements and overall scope.

## Future Scalability Considerations

*   The architecture should be modular to allow for adding more agents and integration types in the future.
*   The Credential Management system should be scalable to handle a growing number of services and users.
*   The Workflow Execution Engine should be designed with potential scaling in mind, although complex distributed execution is not an "MVP ++" requirement.
*   The database schema should be flexible enough to accommodate future data needs.
