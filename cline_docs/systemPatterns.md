# System Patterns: Personal Assistant System ("MVP ++")

## System Architecture

The system follows a modular, service-oriented architecture, centered around a modified Twenty CRM backend and a Supabase database. User interactions occur via Web UI, Telegram, or Voice (VAPI), which communicate with the backend API. The AI Playground backend manages workflow definitions and execution. AI Agents are distinct modules that perform specific tasks, utilizing a dedicated Credential Management module for secure access to external APIs. Supabase provides core services like database (PostgreSQL), authentication (Auth), real-time updates (Realtime), vector storage for RAG (Supabase Vector), and secret management (Supabase Vault). External services like Gmail, Google Calendar, OpenAI/OpenRouter, and potentially n8n are integrated via APIs or webhooks.

```mermaid
graph TD
    A[User] --> B(Web UI)
    A --> C(Telegram Interface)
    A --> D(Voice Interface)
    
    B --> E(Twenty Frontend)
    C --> F(Telegram Bot Service)
    D --> G(VAPI Service)
    
    E --> H(Twenty Backend / API)
    F --> H
    G --> H
    
    H --> I(Supabase)
    I --> J(PostgreSQL DB)
    I --> K(Auth)
    I --> L(Realtime)
    I --> M(Supabase Vector - RAG)
    I --> N(Supabase Vault - Credentials)
    
    H --> O(AI Playground Backend)
    O --> P(Workflow Execution Engine)
    O --> Q(Credential Management Module)
    Q --> N
    
    P --> R(AI Agents)
    R --> Q
    R --> S(External APIs)
    S --> T(Gmail API)
    S --> U(Google Calendar API)
    S --> V(OpenAI/OpenRouter)
    S --> W(n8n Webhook)
    
    subgraph AI Playground
        O
        P
        Q
    end
    
    subgraph Supabase Backend
        I
        J
        K
        L
        M
        N
    end
    
    subgraph External Services
        S
        T
        U
        V
        W
    end
    
    subgraph Core System
        H
        R
    end
    
    subgraph User Interfaces
        B
        C
        D
    end
    
    A
```

## Key Technical Decisions

*   **Base CRM:** Twenty CRM is used as the foundation, leveraging its existing CRM functionalities.
*   **Database & Backend Services:** Supabase provides a managed PostgreSQL database, authentication, real-time capabilities, vector storage, and secret management, simplifying backend development.
*   **Frontend Framework:** React, building upon Twenty's existing frontend.
*   **Workflow Engine:** Custom visual builder and execution engine, potentially integrating with external tools like n8n.
*   **Credential Management:** Centralized, encrypted storage using Supabase Vault for enhanced security.
*   **AI Agent Design:** Agents are designed as modular, reusable components that interact with external APIs and the core system via the backend.

## Design Patterns in Use

*   **Service-Oriented Architecture (SOA):** The system is composed of distinct services (Frontend, Backend, AI Playground, Agents, Telegram Service, VAPI Service) that communicate via APIs.
*   **Model-View-Controller (MVC) or similar:** Likely within the Twenty CRM structure and potentially adapted for new modules.
*   **Observer Pattern:** Potentially used for real-time updates via Supabase Realtime, e.g., notifying the UI about workflow execution status changes.
*   **Strategy Pattern:** Could be applied to AI Agents, where different agent types implement a common interface for execution.
*   **Factory Pattern:** Useful for creating different types of AI Playground nodes or AI Agents.

## Component Relationships

*   User Interfaces (Web, Telegram, Voice) interact with the Twenty Backend/API.
*   The Twenty Backend orchestrates calls to AI Playground Backend, AI Agents, and Supabase.
*   AI Playground Backend manages workflow definitions and triggers the Workflow Execution Engine.
*   Workflow Execution Engine runs workflows, calling AI Agents as needed.
*   AI Agents perform specific tasks, interacting with External APIs and the Credential Management Module.
*   Credential Management Module securely retrieves credentials from Supabase Vault.
*   Supabase provides core data storage, auth, real-time, RAG, and secret services to the Backend and AI Playground Backend.

## Critical Implementation Paths

*   Setting up and integrating Twenty CRM with Supabase.
*   Developing the core AI Playground visual builder and execution engine.
*   Implementing the secure Credential Management system.
*   Building the initial set of AI Agents and their integration with external APIs.
*   Integrating Telegram and VAPI services with the backend and agents.
*   Implementing Multi-User support across the system.
*   Integrating Supabase Vector for RAG.
*   Developing the n8n integration node and logic.
