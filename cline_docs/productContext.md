# Product Context: Personal Assistant System ("MVP ++")

## Why this project exists

The project aims to create a personal assistant system that leverages AI to automate and streamline daily tasks, particularly those related to CRM, communication (email, voice, Telegram), and scheduling. The goal is to provide users with an intelligent tool that proactively assists them, reducing manual effort and improving productivity.

## Problems it solves

*   **Information Overload:** Helps manage emails, contacts, and calendar events more effectively with AI assistance.
*   **Manual Task Repetition:** Automates repetitive tasks through customizable workflows.
*   **Disparate Tools:** Unifies CRM data, communication channels, and AI capabilities into a single interface.
*   **Inefficient Workflows:** Provides a visual way to build and manage complex interactions between different services and AI models.

## How it should work

The system should provide a seamless and intuitive experience. Users interact primarily through the Web UI or Telegram, and potentially voice commands via VAPI. The AI Playground allows users to visually design workflows by connecting different nodes (Agents, Triggers, Actions, Logic). These workflows execute in the background, powered by the backend and interacting with external services via secure credentials. The system should proactively offer assistance and insights based on user data and context.

## User Experience Goals

*   **Intuitive:** Users should be able to understand and use the system with minimal training, following the "Apple Way" principle of design without explanation.
*   **Efficient:** Tasks should be completed quickly, whether through automated workflows or direct interaction.
*   **Reliable:** Workflows and agents should execute reliably, with clear feedback and error handling.
*   **Secure:** User data and credentials must be handled with the highest level of security.
*   **Elegant:** The interface should be visually appealing, clean, and consistent, adhering to the "Apple Way" design principles.
