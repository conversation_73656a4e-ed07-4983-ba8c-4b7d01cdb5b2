# Tech Stack: Personal Assistant System ("MVP ++")

## Backend

*   **Base CRM:** Twenty CRM (Fork/Modify)
*   **Database & Backend Services:** Supabase (PostgreSQL, Auth, Realtime, Supabase Vector, Supabase Vault)
*   **Language:** Node.js/TypeScript
*   **Credential Management:** Custom module leveraging Supabase Vault
*   **AI Playground Backend:** Custom implementation for workflow definition and execution
*   **AI Agents:** Modular TypeScript classes/functions
*   **Telegram Integration:** Node.js library for Telegram Bot API
*   **Voice Integration:** VAPI service integration

## Frontend

*   **Framework:** React (extending Twenty's frontend)
*   **Styling:** Tailwind CSS
*   **Icons:** Lucide Icons
*   **Workflow Builder:** React Flow
*   **Design System:** Custom implementation based on "Apple Way" principles and defined CSS variables

## AI Integration

*   **LLM APIs:** OpenAI/OpenRouter APIs
*   **Email Integration:** Gmail API
*   **Calendar Integration:** Google Calendar API
*   **RAG:** Supabase Vector
*   **External Workflow Integration:** Webhooks (for n8n)

## Development Setup

*   **Version Control:** Git
*   **Package Manager:** npm (or yarn/pnpm, based on Twenty's preference)
*   **Code Quality:** ESLint, Prettier
*   **Testing:** Jest/React Testing Library (or similar, based on Twenty's setup)
*   **Local Environment:** Docker (optional, but good for consistency)

## Technical Constraints

*   Building upon the existing Twenty CRM architecture.
*   Supabase as the primary backend service provider.
*   Adherence to "Apple Way" design principles in the frontend.

## Dependencies

*   Twenty CRM codebase
*   Supabase client libraries (JS)
*   React and related libraries
*   React Flow library
*   Tailwind CSS
*   Lucide Icons
*   Libraries for interacting with Gmail API, Google Calendar API, OpenAI/OpenRouter APIs
*   Library for Telegram Bot API
*   VAPI integration library/SDK
*   Potentially webhook handling libraries
